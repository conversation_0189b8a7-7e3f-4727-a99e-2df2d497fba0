-- Fix appointments RLS policy to include system_admin role
-- This allows system admins to view all appointments across all organizations

-- Drop the existing policy
DROP POLICY IF EXISTS "appointments_access" ON "public"."appointments";

-- Create updated policy that includes system_admin and uses user_roles table
CREATE POLICY "appointments_access" ON "public"."appointments" FOR SELECT USING (
  -- System admin can see all appointments
  (EXISTS (
    SELECT 1 FROM user_roles
    WHERE user_id = auth.uid()
    AND role = 'system_admin'
  )) OR
  -- Patient can see their own appointments
  (patient_id = auth.uid()) OR
  -- Provider can see appointments they're assigned to
  (provider_id IN (
    SELECT id FROM healthcare_providers
    WHERE user_id = auth.uid()
  )) OR
  -- Admin and clinical staff can see appointments in their organization
  (EXISTS (
    SELECT 1 FROM user_roles ur
    WHERE ur.user_id = auth.uid()
    AND ur.role IN ('org_admin', 'clinical_admin')
    AND ur.organization_id = appointments.organization_id
  ))
);

-- Also update the update policy to include system_admin
DROP POLICY IF EXISTS "optimized_appointments_update" ON "public"."appointments";

CREATE POLICY "optimized_appointments_update" ON "public"."appointments" FOR UPDATE USING (
  -- System admin can update all appointments
  (EXISTS (
    SELECT 1 FROM user_roles
    WHERE user_id = auth.uid()
    AND role = 'system_admin'
  )) OR
  -- Patient can update their own appointments
  (patient_id = auth.uid()) OR
  -- Provider can update appointments they're assigned to
  (provider_id IN (
    SELECT id FROM healthcare_providers
    WHERE user_id = auth.uid()
  )) OR
  -- Admin and clinical staff can update appointments in their organization
  (EXISTS (
    SELECT 1 FROM user_roles ur
    WHERE ur.user_id = auth.uid()
    AND ur.role IN ('org_admin', 'clinical_admin')
    AND ur.organization_id = appointments.organization_id
  ))
);