

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;


CREATE EXTENSION IF NOT EXISTS "pg_net" WITH SCHEMA "extensions";






COMMENT ON SCHEMA "public" IS 'standard public schema';



CREATE EXTENSION IF NOT EXISTS "pg_graphql" WITH SCHEMA "graphql";






CREATE EXTENSION IF NOT EXISTS "pg_stat_statements" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "pgcrypto" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "pgjwt" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "supabase_vault" WITH SCHEMA "vault";






CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA "extensions";






CREATE TYPE "public"."administration_route" AS ENUM (
    'intramuscular',
    'subcutaneous',
    'intradermal',
    'oral',
    'nasal'
);


ALTER TYPE "public"."administration_route" OWNER TO "postgres";


CREATE TYPE "public"."administration_site" AS ENUM (
    'left_arm',
    'right_arm',
    'left_thigh',
    'right_thigh',
    'other'
);


ALTER TYPE "public"."administration_site" OWNER TO "postgres";


CREATE TYPE "public"."alert_severity" AS ENUM (
    'low',
    'medium',
    'high',
    'critical'
);


ALTER TYPE "public"."alert_severity" OWNER TO "postgres";


CREATE TYPE "public"."alert_status" AS ENUM (
    'active',
    'inactive',
    'resolved'
);


ALTER TYPE "public"."alert_status" OWNER TO "postgres";


CREATE TYPE "public"."allergy_severity" AS ENUM (
    'mild',
    'moderate',
    'severe',
    'life_threatening'
);


ALTER TYPE "public"."allergy_severity" OWNER TO "postgres";


CREATE TYPE "public"."allergy_status" AS ENUM (
    'active',
    'inactive',
    'resolved'
);


ALTER TYPE "public"."allergy_status" OWNER TO "postgres";


CREATE TYPE "public"."appointment_status" AS ENUM (
    'scheduled',
    'checked_in',
    'in_progress',
    'completed',
    'cancelled',
    'no_show'
);


ALTER TYPE "public"."appointment_status" OWNER TO "postgres";


CREATE TYPE "public"."conversation_type" AS ENUM (
    'direct',
    'group',
    'department',
    'announcement'
);


ALTER TYPE "public"."conversation_type" OWNER TO "postgres";


CREATE TYPE "public"."department_type" AS ENUM (
    'primary_care',
    'pediatrics',
    'cardiology',
    'neurology',
    'orthopedics',
    'emergency',
    'laboratory',
    'pharmacy',
    'radiology',
    'billing',
    'administration'
);


ALTER TYPE "public"."department_type" OWNER TO "postgres";


CREATE TYPE "public"."gender" AS ENUM (
    'male',
    'female',
    'other',
    'prefer_not_to_say'
);


ALTER TYPE "public"."gender" OWNER TO "postgres";


CREATE TYPE "public"."material_format" AS ENUM (
    'text',
    'pdf',
    'video',
    'audio',
    'interactive'
);


ALTER TYPE "public"."material_format" OWNER TO "postgres";


CREATE TYPE "public"."message_state" AS ENUM (
    'sent',
    'delivered',
    'read'
);


ALTER TYPE "public"."message_state" OWNER TO "postgres";


CREATE TYPE "public"."notification_priority" AS ENUM (
    'low',
    'medium',
    'high',
    'urgent'
);


ALTER TYPE "public"."notification_priority" OWNER TO "postgres";


CREATE TYPE "public"."notification_status" AS ENUM (
    'pending',
    'sent',
    'delivered',
    'read',
    'failed'
);


ALTER TYPE "public"."notification_status" OWNER TO "postgres";


CREATE TYPE "public"."notification_type" AS ENUM (
    'appointment_reminder',
    'lab_result',
    'prescription_update',
    'medical_record_update',
    'task_assignment',
    'message',
    'alert',
    'system_update'
);


ALTER TYPE "public"."notification_type" OWNER TO "postgres";


CREATE TYPE "public"."order_priority" AS ENUM (
    'routine',
    'urgent',
    'stat',
    'emergency'
);


ALTER TYPE "public"."order_priority" OWNER TO "postgres";


CREATE TYPE "public"."order_status" AS ENUM (
    'pending',
    'approved',
    'in_progress',
    'completed',
    'cancelled',
    'declined'
);


ALTER TYPE "public"."order_status" OWNER TO "postgres";


CREATE TYPE "public"."order_type" AS ENUM (
    'lab',
    'imaging',
    'medication',
    'procedure',
    'referral',
    'consultation',
    'other'
);


ALTER TYPE "public"."order_type" OWNER TO "postgres";


CREATE TYPE "public"."provider_type" AS ENUM (
    'doctor',
    'nurse',
    'specialist',
    'admin'
);


ALTER TYPE "public"."provider_type" OWNER TO "postgres";


CREATE TYPE "public"."referral_priority" AS ENUM (
    'routine',
    'urgent',
    'emergency'
);


ALTER TYPE "public"."referral_priority" OWNER TO "postgres";


CREATE TYPE "public"."referral_status" AS ENUM (
    'pending',
    'scheduled',
    'completed',
    'cancelled',
    'declined'
);


ALTER TYPE "public"."referral_status" OWNER TO "postgres";


CREATE TYPE "public"."task_priority" AS ENUM (
    'low',
    'medium',
    'high',
    'urgent'
);


ALTER TYPE "public"."task_priority" OWNER TO "postgres";


CREATE TYPE "public"."task_status" AS ENUM (
    'pending',
    'in_progress',
    'completed',
    'cancelled',
    'blocked'
);


ALTER TYPE "public"."task_status" OWNER TO "postgres";


CREATE TYPE "public"."user_role" AS ENUM (
    'system_admin',
    'org_admin',
    'clinical_admin',
    'physician',
    'nurse_practitioner',
    'registered_nurse',
    'medical_assistant',
    'front_desk',
    'billing_staff',
    'pharmacist',
    'lab_technician',
    'patient'
);


ALTER TYPE "public"."user_role" OWNER TO "postgres";


CREATE TYPE "public"."workflow_status" AS ENUM (
    'pending',
    'in_progress',
    'completed',
    'cancelled',
    'failed'
);


ALTER TYPE "public"."workflow_status" OWNER TO "postgres";


CREATE TYPE "public"."workflow_trigger" AS ENUM (
    'scheduled',
    'event_based',
    'manual'
);


ALTER TYPE "public"."workflow_trigger" OWNER TO "postgres";


CREATE TYPE "public"."workflow_type" AS ENUM (
    'appointment_reminder',
    'lab_result_notification',
    'prescription_renewal',
    'patient_followup',
    'referral_management',
    'insurance_verification',
    'document_review'
);


ALTER TYPE "public"."workflow_type" OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."accept_organization_invite"("invite_id" "uuid") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO ''
    AS $$
DECLARE
  v_org_id UUID;
  v_role TEXT;
  v_email TEXT;
BEGIN
  -- Get and validate invite
  SELECT organization_id, role, email
  INTO v_org_id, v_role, v_email
  FROM public.organization_invites
  WHERE id = invite_id
    AND status = 'pending'
    AND expires_at > NOW()
    AND email = auth.jwt()->>'email';

  IF NOT FOUND THEN
    RETURN FALSE;
  END IF;

  -- Begin transaction
  BEGIN
    -- Update invite status
    UPDATE public.organization_invites
    SET status = 'accepted'
    WHERE id = invite_id;

    -- Create user role
    INSERT INTO public.user_roles (user_id, organization_id, role, invitation_status)
    VALUES (auth.uid(), v_org_id, v_role, 'accepted');

    RETURN TRUE;
  EXCEPTION WHEN OTHERS THEN
    RETURN FALSE;
  END;
END;
$$;


ALTER FUNCTION "public"."accept_organization_invite"("invite_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."add_lab_results"("p_patient_id" "uuid", "p_provider_id" "uuid", "p_test_name" "text", "p_results" "jsonb", "p_normal_range" "jsonb", "p_notes" "text") RETURNS "uuid"
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO ''
    AS $$
DECLARE
    v_lab_result_id UUID;
BEGIN
    INSERT INTO lab_results (
        patient_id,
        provider_id,
        test_name,
        test_date,
        results,
        normal_range,
        notes
    ) VALUES (
        p_patient_id,
        p_provider_id,
        p_test_name,
        CURRENT_TIMESTAMP,
        p_results,
        p_normal_range,
        p_notes
    ) RETURNING id INTO v_lab_result_id;

    RETURN v_lab_result_id;
END;
$$;


ALTER FUNCTION "public"."add_lab_results"("p_patient_id" "uuid", "p_provider_id" "uuid", "p_test_name" "text", "p_results" "jsonb", "p_normal_range" "jsonb", "p_notes" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."add_medical_record"("p_patient_id" "uuid", "p_provider_id" "uuid", "p_chief_complaint" "text", "p_diagnosis" "text"[], "p_treatment_plan" "text", "p_notes" "text", "p_attachments" "jsonb" DEFAULT '{"files": []}'::"jsonb") RETURNS "uuid"
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO ''
    AS $$
DECLARE
    v_record_id UUID;
BEGIN
    INSERT INTO medical_records (
        patient_id,
        provider_id,
        visit_date,
        chief_complaint,
        diagnosis,
        treatment_plan,
        notes,
        attachments
    ) VALUES (
        p_patient_id,
        p_provider_id,
        CURRENT_TIMESTAMP,
        p_chief_complaint,
        p_diagnosis,
        p_treatment_plan,
        p_notes,
        p_attachments
    ) RETURNING id INTO v_record_id;

    RETURN v_record_id;
END;
$$;


ALTER FUNCTION "public"."add_medical_record"("p_patient_id" "uuid", "p_provider_id" "uuid", "p_chief_complaint" "text", "p_diagnosis" "text"[], "p_treatment_plan" "text", "p_notes" "text", "p_attachments" "jsonb") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."assign_task"("p_title" "text", "p_description" "text", "p_assigned_to" "uuid", "p_priority" "public"."task_priority" DEFAULT 'medium'::"public"."task_priority", "p_due_date" timestamp with time zone DEFAULT NULL::timestamp with time zone, "p_related_to" "jsonb" DEFAULT NULL::"jsonb", "p_metadata" "jsonb" DEFAULT NULL::"jsonb") RETURNS "uuid"
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO ''
    AS $$
DECLARE
    v_task_id UUID;
    v_org_id UUID;
    v_dept_id UUID;
BEGIN
    -- Get organization and department ID from assigned user's role
    SELECT organization_id, department_id INTO v_org_id, v_dept_id
    FROM user_roles
    WHERE user_id = p_assigned_to
    LIMIT 1;

    -- Create task
    INSERT INTO tasks (
        organization_id,
        department_id,
        title,
        description,
        priority,
        assigned_to,
        assigned_by,
        due_date,
        related_to,
        metadata
    ) VALUES (
        v_org_id,
        v_dept_id,
        p_title,
        p_description,
        p_priority,
        p_assigned_to,
        auth.uid(),
        p_due_date,
        COALESCE(p_related_to, '{}'::jsonb),
        COALESCE(p_metadata, '{}'::jsonb)
    ) RETURNING id INTO v_task_id;

    -- Send notification to assigned user
    PERFORM send_notification(
        'task_assignment'::notification_type,
        p_assigned_to,
        'New Task Assigned: ' || p_title,
        p_description,
        jsonb_build_object('task_id', v_task_id)
    );

    RETURN v_task_id;
END;
$$;


ALTER FUNCTION "public"."assign_task"("p_title" "text", "p_description" "text", "p_assigned_to" "uuid", "p_priority" "public"."task_priority", "p_due_date" timestamp with time zone, "p_related_to" "jsonb", "p_metadata" "jsonb") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."audit_log_changes"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO ''
    AS $$
DECLARE
    old_data jsonb;
    new_data jsonb;
BEGIN
    -- Only proceed if the audit_logs table exists
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'audit_logs') THEN
        IF (TG_OP = 'DELETE') THEN
            old_data = to_jsonb(OLD);
            new_data = null;
        ELSIF (TG_OP = 'UPDATE') THEN
            old_data = to_jsonb(OLD);
            new_data = to_jsonb(NEW);
        ELSIF (TG_OP = 'INSERT') THEN
            old_data = null;
            new_data = to_jsonb(NEW);
        END IF;

        -- Insert audit log
        INSERT INTO public.audit_logs (
            table_name,
            record_id,
            action,
            old_data,
            new_data,
            changed_by,
            timestamp
        ) VALUES (
            TG_TABLE_NAME,
            CASE
                WHEN TG_OP = 'DELETE' THEN OLD.id
                ELSE NEW.id
            END,
            TG_OP,
            old_data,
            new_data,
            auth.uid(),
            CURRENT_TIMESTAMP
        );
    END IF;

    RETURN NULL;
END;
$$;


ALTER FUNCTION "public"."audit_log_changes"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."can_access_analytics"("org_id" "uuid") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO ''
    AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.user_roles ur
    WHERE ur.user_id = (select auth.uid()) 
    AND ur.organization_id = org_id
    AND ur.role::text IN ('super_admin', 'admin', 'staff') 
  );
END;
$$;


ALTER FUNCTION "public"."can_access_analytics"("org_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."check_user_permission"("p_user_id" "uuid", "p_resource" "text", "p_action" "text", "p_context" "jsonb" DEFAULT '{}'::"jsonb") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO ''
    AS $$
DECLARE
    v_role user_role;
    v_permissions JSONB;
BEGIN
    -- Get user's role and custom permissions
    SELECT role, custom_permissions
    INTO v_role, v_permissions
    FROM user_roles
    WHERE user_id = p_user_id;

    -- Check if user has permission through role
    RETURN EXISTS (
        SELECT 1
        FROM role_permissions
        WHERE role = v_role
        AND resource = p_resource
        AND p_action = ANY(actions)
        AND (
            conditions @> p_context
            OR
            custom_permissions->p_resource->p_action = 'true'
        )
    );
END;
$$;


ALTER FUNCTION "public"."check_user_permission"("p_user_id" "uuid", "p_resource" "text", "p_action" "text", "p_context" "jsonb") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."clean_expired_invites"() RETURNS "void"
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO ''
    AS $$
BEGIN
    DELETE FROM public.organization_invites
    WHERE expires_at < now()
    AND status = 'pending';
END;
$$;


ALTER FUNCTION "public"."clean_expired_invites"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."decline_organization_invite"("invite_id" "uuid") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO ''
    AS $$
BEGIN
  UPDATE public.organization_invites
  SET status = 'declined'
  WHERE id = invite_id
    AND status = 'pending'
    AND expires_at > NOW()
    AND email = auth.jwt()->>'email';

  RETURN FOUND;
END;
$$;


ALTER FUNCTION "public"."decline_organization_invite"("invite_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."disable_rls"() RETURNS "void"
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO ''
    AS $$
BEGIN
    -- This function is used for testing purposes only
    -- It should not be used in production
    RAISE NOTICE 'RLS has been temporarily disabled for testing';
END;
$$;


ALTER FUNCTION "public"."disable_rls"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_appointment_statistics"("p_provider_id" "uuid" DEFAULT NULL::"uuid", "p_start_date" "date" DEFAULT (CURRENT_DATE - '30 days'::interval), "p_end_date" "date" DEFAULT CURRENT_DATE) RETURNS TABLE("total_appointments" integer, "completed_appointments" integer, "cancelled_appointments" integer, "avg_duration" numeric, "most_common_reason" "text")
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO ''
    AS $$
BEGIN
    RETURN QUERY
    WITH appointment_stats AS (
        SELECT 
            COUNT(*) as total,
            COUNT(*) FILTER (WHERE status = 'completed') as completed,
            COUNT(*) FILTER (WHERE status = 'cancelled') as cancelled,
            AVG(duration_minutes) as avg_dur,
            MODE() WITHIN GROUP (ORDER BY reason) as common_reason
        FROM appointments
        WHERE 
            appointment_date::DATE BETWEEN p_start_date AND p_end_date
            AND (p_provider_id IS NULL OR provider_id = p_provider_id)
    )
    SELECT 
        total,
        completed,
        cancelled,
        ROUND(avg_dur::DECIMAL, 2),
        common_reason
    FROM appointment_stats;
END;
$$;


ALTER FUNCTION "public"."get_appointment_statistics"("p_provider_id" "uuid", "p_start_date" "date", "p_end_date" "date") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_medication_statistics"("p_provider_id" "uuid" DEFAULT NULL::"uuid", "p_start_date" "date" DEFAULT (CURRENT_DATE - '30 days'::interval)) RETURNS TABLE("medication_name" "text", "total_prescriptions" integer, "active_prescriptions" integer, "avg_duration_days" integer)
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO ''
    AS $$
BEGIN
    RETURN QUERY
    SELECT 
        m.medication_name,
        COUNT(*)::INTEGER as total_prescriptions,
        COUNT(*) FILTER (WHERE m.active = true)::INTEGER as active_prescriptions,
        AVG(
            CASE 
                WHEN m.end_date IS NOT NULL THEN 
                    (m.end_date - m.start_date)
                ELSE
                    (CURRENT_DATE - m.start_date)
            END
        )::INTEGER as avg_duration_days
    FROM medications m
    WHERE 
        m.start_date >= p_start_date
        AND (p_provider_id IS NULL OR m.provider_id = p_provider_id)
    GROUP BY m.medication_name
    ORDER BY total_prescriptions DESC;
END;
$$;


ALTER FUNCTION "public"."get_medication_statistics"("p_provider_id" "uuid", "p_start_date" "date") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_organization_metrics"("p_organization_id" "uuid", "p_start_date" timestamp with time zone DEFAULT (CURRENT_DATE - '30 days'::interval), "p_end_date" timestamp with time zone DEFAULT CURRENT_DATE) RETURNS TABLE("metric_name" "text", "current_value" numeric, "previous_value" numeric, "change_percentage" numeric)
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO ''
    AS $$
BEGIN
    RETURN QUERY
    WITH current_period AS (
        SELECT 
            'appointments' as metric_name,
            COUNT(*) as value
        FROM appointments
        WHERE organization_id = p_organization_id
        AND appointment_date BETWEEN p_start_date AND p_end_date
        
        UNION ALL
        
        SELECT 
            'patients',
            COUNT(DISTINCT patient_id)
        FROM appointments
        WHERE organization_id = p_organization_id
        AND appointment_date BETWEEN p_start_date AND p_end_date
        
        UNION ALL
        
        SELECT 
            'providers',
            COUNT(DISTINCT provider_id)
        FROM appointments
        WHERE organization_id = p_organization_id
        AND appointment_date BETWEEN p_start_date AND p_end_date
        
        UNION ALL
        
        SELECT 
            'avg_duration',
            AVG(duration_minutes)
        FROM appointments
        WHERE organization_id = p_organization_id
        AND appointment_date BETWEEN p_start_date AND p_end_date
    ),
    previous_period AS (
        SELECT 
            'appointments' as metric_name,
            COUNT(*) as value
        FROM appointments
        WHERE organization_id = p_organization_id
        AND appointment_date BETWEEN 
            p_start_date - (p_end_date - p_start_date) AND 
            p_start_date
        
        UNION ALL
        
        SELECT 
            'patients',
            COUNT(DISTINCT patient_id)
        FROM appointments
        WHERE organization_id = p_organization_id
        AND appointment_date BETWEEN 
            p_start_date - (p_end_date - p_start_date) AND 
            p_start_date
        
        UNION ALL
        
        SELECT 
            'providers',
            COUNT(DISTINCT provider_id)
        FROM appointments
        WHERE organization_id = p_organization_id
        AND appointment_date BETWEEN 
            p_start_date - (p_end_date - p_start_date) AND 
            p_start_date
        
        UNION ALL
        
        SELECT 
            'avg_duration',
            AVG(duration_minutes)
        FROM appointments
        WHERE organization_id = p_organization_id
        AND appointment_date BETWEEN 
            p_start_date - (p_end_date - p_start_date) AND 
            p_start_date
    )
    SELECT 
        c.metric_name,
        c.value as current_value,
        p.value as previous_value,
        CASE 
            WHEN p.value = 0 THEN NULL
            ELSE ((c.value - p.value) / p.value * 100)
        END as change_percentage
    FROM current_period c
    LEFT JOIN previous_period p ON c.metric_name = p.metric_name;
END;
$$;


ALTER FUNCTION "public"."get_organization_metrics"("p_organization_id" "uuid", "p_start_date" timestamp with time zone, "p_end_date" timestamp with time zone) OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_patient_demographics"("p_provider_id" "uuid" DEFAULT NULL::"uuid") RETURNS TABLE("age_group" "text", "gender_distribution" "jsonb", "patient_count" integer)
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO ''
    AS $$
BEGIN
    RETURN QUERY
    WITH patient_ages AS (
        SELECT 
            CASE 
                WHEN age(date_of_birth) < INTERVAL '18 years' THEN '0-17'
                WHEN age(date_of_birth) < INTERVAL '30 years' THEN '18-29'
                WHEN age(date_of_birth) < INTERVAL '50 years' THEN '30-49'
                WHEN age(date_of_birth) < INTERVAL '70 years' THEN '50-69'
                ELSE '70+'
            END as age_group,
            gender
        FROM patients p
        WHERE p_provider_id IS NULL OR EXISTS (
            SELECT 1 FROM medical_records mr 
            WHERE mr.patient_id = p.id 
            AND mr.provider_id = p_provider_id
        )
    )
    SELECT 
        age_group,
        jsonb_object_agg(gender, COUNT(*)::INTEGER) as gender_distribution,
        COUNT(*)::INTEGER as patient_count
    FROM patient_ages
    GROUP BY age_group
    ORDER BY age_group;
END;
$$;


ALTER FUNCTION "public"."get_patient_demographics"("p_provider_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_patient_summary"("p_patient_id" "uuid") RETURNS "jsonb"
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO ''
    AS $$
DECLARE
    v_summary jsonb;
BEGIN
    SELECT jsonb_build_object(
        'patient_id', p.id,
        'name', p.first_name || ' ' || p.last_name,
        'age', extract(year from age(now(), p.date_of_birth)),
        'gender', p.gender,
        'recent_visits', (
            SELECT jsonb_agg(jsonb_build_object(
                'date', a.appointment_date,
                'provider', pr.first_name || ' ' || pr.last_name,
                'reason', a.reason
            ))
            FROM appointments a
            JOIN providers pr ON a.provider_id = pr.id
            WHERE a.patient_id = p_patient_id
            ORDER BY a.appointment_date DESC
            LIMIT 5
        ),
        'active_medications', (
            SELECT jsonb_agg(jsonb_build_object(
                'name', m.medication_name,
                'dosage', m.dosage,
                'frequency', m.frequency
            ))
            FROM medications m
            WHERE m.patient_id = p_patient_id AND m.active = true
        )
    ) INTO v_summary
    FROM patients p
    WHERE p.id = p_patient_id;

    RETURN v_summary;
END;
$$;


ALTER FUNCTION "public"."get_patient_summary"("p_patient_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_patient_visit_history"("p_patient_id" "uuid", "p_start_date" "date" DEFAULT (CURRENT_DATE - '1 year'::interval), "p_end_date" "date" DEFAULT CURRENT_DATE) RETURNS TABLE("visit_date" "date", "visit_type" "text", "provider_name" "text", "diagnosis" "text"[], "medications_prescribed" "text"[], "lab_tests_performed" "text"[])
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO ''
    AS $$
BEGIN
    RETURN QUERY
    WITH visit_data AS (
        SELECT 
            mr.visit_date::DATE,
            'Medical Record'::TEXT as visit_type,
            hp.first_name || ' ' || hp.last_name as provider_name,
            mr.diagnosis,
            ARRAY(
                SELECT m.medication_name
                FROM medications m
                WHERE m.patient_id = mr.patient_id
                AND m.start_date::DATE = mr.visit_date::DATE
            ) as medications,
            ARRAY(
                SELECT lr.test_name
                FROM lab_results lr
                WHERE lr.patient_id = mr.patient_id
                AND lr.test_date::DATE = mr.visit_date::DATE
            ) as lab_tests
        FROM medical_records mr
        JOIN healthcare_providers hp ON hp.id = mr.provider_id
        WHERE 
            mr.patient_id = p_patient_id
            AND mr.visit_date::DATE BETWEEN p_start_date AND p_end_date
        
        UNION ALL
        
        SELECT 
            a.appointment_date::DATE,
            'Appointment'::TEXT,
            hp.first_name || ' ' || hp.last_name,
            NULL::TEXT[],
            NULL::TEXT[],
            NULL::TEXT[]
        FROM appointments a
        JOIN healthcare_providers hp ON hp.id = a.provider_id
        WHERE 
            a.patient_id = p_patient_id
            AND a.appointment_date::DATE BETWEEN p_start_date AND p_end_date
            AND NOT EXISTS (
                SELECT 1 
                FROM medical_records mr 
                WHERE mr.patient_id = a.patient_id 
                AND mr.visit_date::DATE = a.appointment_date::DATE
            )
    )
    SELECT * FROM visit_data
    ORDER BY visit_date DESC;
END;
$$;


ALTER FUNCTION "public"."get_patient_visit_history"("p_patient_id" "uuid", "p_start_date" "date", "p_end_date" "date") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_provider_workload"("p_start_date" "date" DEFAULT (CURRENT_DATE - '30 days'::interval), "p_end_date" "date" DEFAULT CURRENT_DATE) RETURNS TABLE("provider_id" "uuid", "provider_name" "text", "provider_type" "public"."provider_type", "total_appointments" integer, "total_patients" integer, "avg_appointments_per_day" numeric)
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO ''
    AS $$
BEGIN
    RETURN QUERY
    WITH provider_stats AS (
        SELECT 
            hp.id,
            hp.first_name || ' ' || hp.last_name as name,
            hp.provider_type,
            COUNT(DISTINCT a.id) as appointment_count,
            COUNT(DISTINCT a.patient_id) as patient_count,
            COUNT(DISTINCT a.id)::DECIMAL / 
                GREATEST(1, (p_end_date - p_start_date + 1)) as avg_daily_appointments
        FROM healthcare_providers hp
        LEFT JOIN appointments a ON 
            a.provider_id = hp.id AND 
            a.appointment_date::DATE BETWEEN p_start_date AND p_end_date
        GROUP BY hp.id, hp.first_name, hp.last_name, hp.provider_type
    )
    SELECT 
        id,
        name,
        provider_type,
        appointment_count,
        patient_count,
        ROUND(avg_daily_appointments, 2)
    FROM provider_stats
    ORDER BY appointment_count DESC;
END;
$$;


ALTER FUNCTION "public"."get_provider_workload"("p_start_date" "date", "p_end_date" "date") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_user_departments"("p_user_id" "uuid") RETURNS TABLE("department_id" "uuid", "department_name" "text")
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO ''
    AS $$
BEGIN
    RETURN QUERY
    SELECT d.id, d.name
    FROM public.departments d
    JOIN public.department_members dm ON d.id = dm.department_id
    WHERE dm.user_id = p_user_id;
END;
$$;


ALTER FUNCTION "public"."get_user_departments"("p_user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."has_patient_access"("provider_id" "uuid", "patient_id" "uuid") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO ''
    AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM medical_records
        WHERE provider_id = provider_id
        AND patient_id = patient_id
    );
END;
$$;


ALTER FUNCTION "public"."has_patient_access"("provider_id" "uuid", "patient_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."is_healthcare_provider"("user_id" "uuid") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO ''
    AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM healthcare_providers
        WHERE id = user_id
    );
END;
$$;


ALTER FUNCTION "public"."is_healthcare_provider"("user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."is_patient"("user_id" "uuid") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO ''
    AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM patients
        WHERE id = user_id
    );
END;
$$;


ALTER FUNCTION "public"."is_patient"("user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."mark_message_read"("p_message_id" "uuid") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO ''
    AS $$
BEGIN
    -- Update message status
    UPDATE message_states
    SET 
        state = 'read',
        updated_at = CURRENT_TIMESTAMP
    WHERE 
        message_id = p_message_id
        AND user_id = auth.uid()
        AND state != 'read';

    -- Update last read timestamp in conversation
    UPDATE conversation_participants
    SET last_read_at = CURRENT_TIMESTAMP
    WHERE 
        conversation_id = (SELECT conversation_id FROM messages WHERE id = p_message_id)
        AND user_id = auth.uid();

    RETURN FOUND;
END;
$$;


ALTER FUNCTION "public"."mark_message_read"("p_message_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."mark_notification_read"("p_notification_id" "uuid") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO ''
    AS $$
BEGIN
    UPDATE notifications
    SET 
        status = 'read'::notification_status,
        read_at = CURRENT_TIMESTAMP
    WHERE 
        id = p_notification_id
        AND recipient_id = auth.uid()
        AND status = 'delivered'::notification_status;
    
    RETURN FOUND;
END;
$$;


ALTER FUNCTION "public"."mark_notification_read"("p_notification_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."prescribe_medication"("p_patient_id" "uuid", "p_provider_id" "uuid", "p_medication_name" "text", "p_dosage" "text", "p_frequency" "text", "p_start_date" "date", "p_end_date" "date", "p_instructions" "text") RETURNS "uuid"
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO ''
    AS $$
DECLARE
    v_medication_id UUID;
BEGIN
    INSERT INTO medications (
        patient_id,
        provider_id,
        medication_name,
        dosage,
        frequency,
        start_date,
        end_date,
        instructions,
        active
    ) VALUES (
        p_patient_id,
        p_provider_id,
        p_medication_name,
        p_dosage,
        p_frequency,
        p_start_date,
        p_end_date,
        p_instructions,
        TRUE
    ) RETURNING id INTO v_medication_id;

    RETURN v_medication_id;
END;
$$;


ALTER FUNCTION "public"."prescribe_medication"("p_patient_id" "uuid", "p_provider_id" "uuid", "p_medication_name" "text", "p_dosage" "text", "p_frequency" "text", "p_start_date" "date", "p_end_date" "date", "p_instructions" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."random_date"("start_date" "date", "end_date" "date") RETURNS "date"
    LANGUAGE "sql"
    SET "search_path" TO ''
    AS $$
  SELECT (start_date + (random() * (end_date - start_date))::integer)::date;
$$;


ALTER FUNCTION "public"."random_date"("start_date" "date", "end_date" "date") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."random_timestamp"("start_timestamp" timestamp without time zone, "end_timestamp" timestamp without time zone) RETURNS timestamp without time zone
    LANGUAGE "sql"
    SET "search_path" TO ''
    AS $$
  SELECT (start_timestamp + (random() * (extract(epoch from end_timestamp) - extract(epoch from start_timestamp))::integer * interval '1 second'))::timestamp;
$$;


ALTER FUNCTION "public"."random_timestamp"("start_timestamp" timestamp without time zone, "end_timestamp" timestamp without time zone) OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."record_analytics_event"("p_event_type" "text", "p_event_data" "jsonb") RETURNS "uuid"
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO ''
    AS $$
DECLARE
    v_org_id UUID;
    v_event_id UUID;
BEGIN
    -- Get organization ID from current user's role
    SELECT organization_id INTO v_org_id
    FROM public.user_roles
    WHERE user_id = auth.uid()
    LIMIT 1;

    -- Record event
    INSERT INTO public.analytics_events (
        organization_id,
        event_type,
        event_data,
        user_id
    ) VALUES (
        v_org_id,
        p_event_type,
        p_event_data,
        auth.uid()
    ) RETURNING id INTO v_event_id;

    RETURN v_event_id;
END;
$$;


ALTER FUNCTION "public"."record_analytics_event"("p_event_type" "text", "p_event_data" "jsonb") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."record_analytics_event"("p_event_type" "text", "p_user_id" "uuid", "p_data" "jsonb") RETURNS "uuid"
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO ''
    AS $$
DECLARE
    v_event_id UUID;
BEGIN
    INSERT INTO analytics (
        event_type,
        user_id,
        event_data,
        created_at
    ) VALUES (
        p_event_type,
        p_user_id,
        p_data,
        now()
    ) RETURNING id INTO v_event_id;

    RETURN v_event_id;
END;
$$;


ALTER FUNCTION "public"."record_analytics_event"("p_event_type" "text", "p_user_id" "uuid", "p_data" "jsonb") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."record_analytics_metric"("p_metric_name" "text", "p_metric_value" numeric, "p_dimensions" "jsonb" DEFAULT NULL::"jsonb") RETURNS "uuid"
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO ''
    AS $$
DECLARE
    v_org_id UUID;
    v_metric_id UUID;
BEGIN
    -- Get organization ID from current user's role
    SELECT organization_id INTO v_org_id
    FROM user_roles
    WHERE user_id = auth.uid()
    LIMIT 1;

    -- Record metric
    INSERT INTO analytics_metrics (
        organization_id,
        metric_name,
        metric_value,
        dimensions
    ) VALUES (
        v_org_id,
        p_metric_name,
        p_metric_value,
        COALESCE(p_dimensions, '{}'::jsonb)
    ) RETURNING id INTO v_metric_id;

    RETURN v_metric_id;
END;
$$;


ALTER FUNCTION "public"."record_analytics_metric"("p_metric_name" "text", "p_metric_value" numeric, "p_dimensions" "jsonb") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."refresh_analytics_views"() RETURNS "void"
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO ''
    AS $$
BEGIN
    REFRESH MATERIALIZED VIEW public.analytics_patient_summary;
    REFRESH MATERIALIZED VIEW public.analytics_daily_summary;
    REFRESH MATERIALIZED VIEW public.analytics_summary;
END;
$$;


ALTER FUNCTION "public"."refresh_analytics_views"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."refresh_materialized_views"() RETURNS "void"
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO ''
    AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY public.analytics_daily_appointments;
    REFRESH MATERIALIZED VIEW CONCURRENTLY public.analytics_patient_metrics;
    REFRESH MATERIALIZED VIEW CONCURRENTLY public.analytics_provider_metrics;
END;
$$;


ALTER FUNCTION "public"."refresh_materialized_views"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."schedule_appointment"("p_patient_id" "uuid", "p_provider_id" "uuid", "p_appointment_date" timestamp without time zone, "p_duration_minutes" integer, "p_reason" "text") RETURNS "uuid"
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO ''
    AS $$
DECLARE
    v_appointment_id UUID;
    v_conflict_count INTEGER;
BEGIN
    -- Check for scheduling conflicts
    SELECT COUNT(*)
    INTO v_conflict_count
    FROM appointments
    WHERE provider_id = p_provider_id
    AND appointment_date < (p_appointment_date + (p_duration_minutes || ' minutes')::INTERVAL)
    AND (appointment_date + (duration_minutes || ' minutes')::INTERVAL) > p_appointment_date;

    IF v_conflict_count > 0 THEN
        RAISE EXCEPTION 'Scheduling conflict detected';
    END IF;

    INSERT INTO appointments (
        patient_id,
        provider_id,
        appointment_date,
        duration_minutes,
        status,
        reason,
        notes
    ) VALUES (
        p_patient_id,
        p_provider_id,
        p_appointment_date,
        p_duration_minutes,
        'scheduled'::appointment_status,
        p_reason,
        ''
    ) RETURNING id INTO v_appointment_id;

    RETURN v_appointment_id;
END;
$$;


ALTER FUNCTION "public"."schedule_appointment"("p_patient_id" "uuid", "p_provider_id" "uuid", "p_appointment_date" timestamp without time zone, "p_duration_minutes" integer, "p_reason" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."search_medical_records"("search_query" "text", "p_patient_id" "uuid" DEFAULT NULL::"uuid", "p_provider_id" "uuid" DEFAULT NULL::"uuid") RETURNS TABLE("id" "uuid", "patient_id" "uuid", "provider_id" "uuid", "visit_date" timestamp without time zone, "chief_complaint" "text", "diagnosis" "text"[], "relevance" double precision)
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO ''
    AS $$
BEGIN
    RETURN QUERY
    SELECT 
        mr.id,
        mr.patient_id,
        mr.provider_id,
        mr.visit_date,
        mr.chief_complaint,
        mr.diagnosis,
        ts_rank(mr.search_vector, to_tsquery('english', search_query)) as relevance
    FROM medical_records mr
    WHERE 
        mr.search_vector @@ to_tsquery('english', search_query)
        AND (p_patient_id IS NULL OR mr.patient_id = p_patient_id)
        AND (p_provider_id IS NULL OR mr.provider_id = p_provider_id)
    ORDER BY relevance DESC;
END;
$$;


ALTER FUNCTION "public"."search_medical_records"("search_query" "text", "p_patient_id" "uuid", "p_provider_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."search_patients"("search_query" "text", "p_provider_id" "uuid" DEFAULT NULL::"uuid") RETURNS TABLE("id" "uuid", "first_name" "text", "last_name" "text", "date_of_birth" "date", "gender" "public"."gender", "relevance" double precision)
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO ''
    AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id,
        p.first_name,
        p.last_name,
        p.date_of_birth,
        p.gender,
        ts_rank(p.search_vector, to_tsquery('english', search_query)) as relevance
    FROM patients p
    WHERE 
        p.search_vector @@ to_tsquery('english', search_query)
        AND (p_provider_id IS NULL OR EXISTS (
            SELECT 1 FROM medical_records mr 
            WHERE mr.patient_id = p.id 
            AND mr.provider_id = p_provider_id
        ))
    ORDER BY relevance DESC;
END;
$$;


ALTER FUNCTION "public"."search_patients"("search_query" "text", "p_provider_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."send_message"("p_conversation_id" "uuid", "p_content" "text", "p_attachments" "jsonb" DEFAULT NULL::"jsonb", "p_metadata" "jsonb" DEFAULT NULL::"jsonb") RETURNS "uuid"
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO ''
    AS $$
DECLARE
    v_message_id UUID;
    v_participant record;
BEGIN
    -- Verify sender is participant
    IF NOT EXISTS (
        SELECT 1 FROM conversation_participants
        WHERE conversation_id = p_conversation_id
        AND user_id = auth.uid()
    ) THEN
        RAISE EXCEPTION 'User is not a participant in this conversation';
    END IF;

    -- Create message
    INSERT INTO messages (
        conversation_id,
        sender_id,
        content,
        attachments,
        metadata
    ) VALUES (
        p_conversation_id,
        auth.uid(),
        p_content,
        COALESCE(p_attachments, '[]'::jsonb),
        COALESCE(p_metadata, '{}'::jsonb)
    ) RETURNING id INTO v_message_id;

    -- Create message status for all participants
    FOR v_participant IN 
        SELECT user_id 
        FROM conversation_participants 
        WHERE conversation_id = p_conversation_id 
        AND user_id != auth.uid()
    LOOP
        INSERT INTO message_states (message_id, user_id, state)
        VALUES (v_message_id, v_participant.user_id, 'sent');

        -- Send notification to participant
        PERFORM send_notification(
            'message'::notification_type,
            v_participant.user_id,
            'New Message',
            p_content,
            jsonb_build_object(
                'conversation_id', p_conversation_id,
                'message_id', v_message_id
            )
        );
    END LOOP;

    -- Update conversation timestamp
    UPDATE conversations
    SET updated_at = CURRENT_TIMESTAMP
    WHERE id = p_conversation_id;

    RETURN v_message_id;
END;
$$;


ALTER FUNCTION "public"."send_message"("p_conversation_id" "uuid", "p_content" "text", "p_attachments" "jsonb", "p_metadata" "jsonb") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."send_notification"("p_type" "public"."notification_type", "p_recipient_id" "uuid", "p_title" "text", "p_content" "text", "p_metadata" "jsonb" DEFAULT NULL::"jsonb", "p_priority" "public"."notification_priority" DEFAULT 'medium'::"public"."notification_priority", "p_scheduled_for" timestamp with time zone DEFAULT NULL::timestamp with time zone) RETURNS "uuid"
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO ''
    AS $$
DECLARE
    v_notification_id UUID;
    v_org_id UUID;
BEGIN
    -- Get organization ID from recipient's role
    SELECT organization_id INTO v_org_id
    FROM user_roles
    WHERE user_id = p_recipient_id
    LIMIT 1;

    -- Create notification
    INSERT INTO notifications (
        organization_id,
        type,
        priority,
        status,
        sender_id,
        recipient_id,
        title,
        content,
        metadata,
        scheduled_for
    ) VALUES (
        v_org_id,
        p_type,
        p_priority,
        CASE 
            WHEN p_scheduled_for IS NULL OR p_scheduled_for <= CURRENT_TIMESTAMP 
            THEN 'sent'::notification_status 
            ELSE 'pending'::notification_status 
        END,
        auth.uid(),
        p_recipient_id,
        p_title,
        p_content,
        COALESCE(p_metadata, '{}'::jsonb),
        p_scheduled_for
    ) RETURNING id INTO v_notification_id;

    -- If notification is for immediate delivery, update sent_at
    IF p_scheduled_for IS NULL OR p_scheduled_for <= CURRENT_TIMESTAMP THEN
        UPDATE notifications 
        SET sent_at = CURRENT_TIMESTAMP 
        WHERE id = v_notification_id;
    END IF;

    RETURN v_notification_id;
END;
$$;


ALTER FUNCTION "public"."send_notification"("p_type" "public"."notification_type", "p_recipient_id" "uuid", "p_title" "text", "p_content" "text", "p_metadata" "jsonb", "p_priority" "public"."notification_priority", "p_scheduled_for" timestamp with time zone) OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."start_conversation"("p_participants" "uuid"[], "p_type" "public"."conversation_type" DEFAULT 'direct'::"public"."conversation_type", "p_title" "text" DEFAULT NULL::"text", "p_initial_message" "text" DEFAULT NULL::"text", "p_metadata" "jsonb" DEFAULT NULL::"jsonb") RETURNS "uuid"
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO ''
    AS $$
DECLARE
    v_conversation_id UUID;
    v_org_id UUID;
    v_participant UUID;
BEGIN
    -- Get organization ID from current user's role
    SELECT organization_id INTO v_org_id
    FROM user_roles
    WHERE user_id = auth.uid()
    LIMIT 1;

    -- Create conversation
    INSERT INTO conversations (
        organization_id,
        type,
        title,
        metadata,
        created_by
    ) VALUES (
        v_org_id,
        p_type,
        COALESCE(p_title, CASE 
            WHEN p_type = 'direct' THEN NULL 
            ELSE 'New Conversation' 
        END),
        COALESCE(p_metadata, '{}'::jsonb),
        auth.uid()
    ) RETURNING id INTO v_conversation_id;

    -- Add participants
    FOREACH v_participant IN ARRAY p_participants || ARRAY[auth.uid()]
    LOOP
        INSERT INTO conversation_participants (conversation_id, user_id)
        VALUES (v_conversation_id, v_participant)
        ON CONFLICT DO NOTHING;
    END LOOP;

    -- Add initial message if provided
    IF p_initial_message IS NOT NULL THEN
        INSERT INTO messages (conversation_id, sender_id, content)
        VALUES (v_conversation_id, auth.uid(), p_initial_message);
    END IF;

    RETURN v_conversation_id;
END;
$$;


ALTER FUNCTION "public"."start_conversation"("p_participants" "uuid"[], "p_type" "public"."conversation_type", "p_title" "text", "p_initial_message" "text", "p_metadata" "jsonb") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."start_workflow"("p_workflow_id" "uuid", "p_context" "jsonb") RETURNS "uuid"
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO ''
    AS $$
DECLARE
    v_instance_id UUID;
BEGIN
    -- Create workflow instance
    INSERT INTO workflow_instances (
        workflow_id,
        context
    ) VALUES (
        p_workflow_id,
        p_context
    ) RETURNING id INTO v_instance_id;

    -- Log workflow start
    INSERT INTO workflow_logs (
        workflow_instance_id,
        step_number,
        step_name,
        status,
        message
    ) VALUES (
        v_instance_id,
        0,
        'workflow_start',
        'in_progress',
        'Workflow started'
    );

    RETURN v_instance_id;
END;
$$;


ALTER FUNCTION "public"."start_workflow"("p_workflow_id" "uuid", "p_context" "jsonb") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_medical_record_search_vector"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO ''
    AS $$
BEGIN
    NEW.search_vector :=
        setweight(to_tsvector('english', coalesce(NEW.chief_complaint, '')), 'A') ||
        setweight(to_tsvector('english', coalesce(array_to_string(NEW.diagnosis, ' '), '')), 'A') ||
        setweight(to_tsvector('english', coalesce(NEW.treatment_plan, '')), 'B') ||
        setweight(to_tsvector('english', coalesce(NEW.notes, '')), 'C');
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."update_medical_record_search_vector"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_patient_search_vector"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO ''
    AS $$
BEGIN
    NEW.search_vector := 
        setweight(to_tsvector('english', coalesce(NEW.first_name, '')), 'A') ||
        setweight(to_tsvector('english', coalesce(NEW.last_name, '')), 'A') ||
        setweight(to_tsvector('english', coalesce(NEW.email, '')), 'B') ||
        setweight(to_tsvector('english', coalesce(NEW.phone, '')), 'B') ||
        setweight(to_tsvector('english', coalesce(NEW.address, '')), 'C');
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."update_patient_search_vector"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_task_status"("p_task_id" "uuid", "p_status" "public"."task_status", "p_comment" "text" DEFAULT NULL::"text") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO ''
    AS $$
DECLARE
    v_task tasks;
BEGIN
    -- Get task details
    SELECT * INTO v_task
    FROM tasks
    WHERE id = p_task_id
    AND (assigned_to = auth.uid() OR assigned_by = auth.uid());

    IF NOT FOUND THEN
        RETURN false;
    END IF;

    -- Update task status
    UPDATE tasks
    SET 
        status = p_status,
        completed_at = CASE WHEN p_status = 'completed' THEN CURRENT_TIMESTAMP ELSE NULL END,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = p_task_id;

    -- Add comment if provided
    IF p_comment IS NOT NULL THEN
        INSERT INTO task_comments (task_id, user_id, content)
        VALUES (p_task_id, auth.uid(), p_comment);
    END IF;

    -- Notify task creator if completed
    IF p_status = 'completed' AND v_task.assigned_by != auth.uid() THEN
        PERFORM send_notification(
            'task_assignment'::notification_type,
            v_task.assigned_by,
            'Task Completed: ' || v_task.title,
            'Task has been marked as completed' || COALESCE(' with comment: ' || p_comment, ''),
            jsonb_build_object('task_id', p_task_id)
        );
    END IF;

    RETURN true;
END;
$$;


ALTER FUNCTION "public"."update_task_status"("p_task_id" "uuid", "p_status" "public"."task_status", "p_comment" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_updated_at_column"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO ''
    AS $$
BEGIN
  NEW.updated_at = TIMEZONE('utc', NOW());
  RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."update_updated_at_column"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_workflow_step"("p_instance_id" "uuid", "p_step_number" integer, "p_status" "public"."workflow_status", "p_result" "jsonb" DEFAULT NULL::"jsonb", "p_message" "text" DEFAULT NULL::"text") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO ''
    AS $$
DECLARE
    v_workflow_instance workflow_instances;
    v_workflow workflows;
BEGIN
    -- Get workflow instance
    SELECT * INTO v_workflow_instance
    FROM workflow_instances
    WHERE id = p_instance_id
    FOR UPDATE;

    IF NOT FOUND THEN
        RETURN false;
    END IF;

    -- Get workflow
    SELECT * INTO v_workflow
    FROM workflows
    WHERE id = v_workflow_instance.workflow_id;

    -- Update workflow instance
    UPDATE workflow_instances
    SET
        status = CASE 
            WHEN p_status = 'completed' AND p_step_number = array_length(v_workflow.steps, 1) THEN 'completed'
            WHEN p_status = 'failed' THEN 'failed'
            ELSE 'in_progress'
        END,
        current_step = CASE 
            WHEN p_status = 'completed' THEN p_step_number + 1
            ELSE p_step_number
        END,
        results = results || COALESCE(p_result, '{}'::jsonb),
        completed_at = CASE 
            WHEN p_status = 'completed' AND p_step_number = array_length(v_workflow.steps, 1) THEN CURRENT_TIMESTAMP
            ELSE completed_at
        END,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = p_instance_id;

    -- Log step update
    INSERT INTO workflow_logs (
        workflow_instance_id,
        step_number,
        step_name,
        status,
        message,
        details
    ) VALUES (
        p_instance_id,
        p_step_number,
        (v_workflow.steps[p_step_number + 1]->>'name'),
        p_status,
        COALESCE(p_message, 'Step ' || p_step_number || ' ' || p_status),
        COALESCE(p_result, '{}'::jsonb)
    );

    RETURN true;
END;
$$;


ALTER FUNCTION "public"."update_workflow_step"("p_instance_id" "uuid", "p_step_number" integer, "p_status" "public"."workflow_status", "p_result" "jsonb", "p_message" "text") OWNER TO "postgres";

SET default_tablespace = '';

SET default_table_access_method = "heap";


CREATE TABLE IF NOT EXISTS "public"."activity_logs" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "organization_id" "uuid" NOT NULL,
    "user_id" "uuid" NOT NULL,
    "action_type" "text" NOT NULL,
    "resource_type" "text" NOT NULL,
    "resource_id" "text" NOT NULL,
    "details" "jsonb" DEFAULT '{}'::"jsonb",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."activity_logs" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."allergies" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "patient_id" "uuid",
    "allergen" "text" NOT NULL,
    "reaction" "text",
    "severity" "public"."allergy_severity" NOT NULL,
    "onset_date" "date",
    "status" "public"."allergy_status" DEFAULT 'active'::"public"."allergy_status",
    "reported_by" "uuid",
    "metadata" "jsonb" DEFAULT '{}'::"jsonb",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."allergies" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."appointments" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "patient_id" "uuid",
    "provider_id" "uuid",
    "appointment_date" timestamp with time zone NOT NULL,
    "duration_minutes" integer DEFAULT 30 NOT NULL,
    "status" "public"."appointment_status" DEFAULT 'scheduled'::"public"."appointment_status" NOT NULL,
    "reason" "text",
    "notes" "text",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "organization_id" "uuid",
    "department_id" "uuid"
);


ALTER TABLE "public"."appointments" OWNER TO "postgres";


CREATE MATERIALIZED VIEW "public"."analytics_daily_appointments" AS
 SELECT "appointments"."organization_id",
    "date_trunc"('day'::"text", "appointments"."appointment_date") AS "date",
    "appointments"."department_id",
    "count"(*) AS "total_appointments",
    "count"(*) FILTER (WHERE ("appointments"."status" = 'completed'::"public"."appointment_status")) AS "completed_appointments",
    "count"(*) FILTER (WHERE ("appointments"."status" = 'cancelled'::"public"."appointment_status")) AS "cancelled_appointments",
    "avg"("appointments"."duration_minutes") AS "avg_duration"
   FROM "public"."appointments"
  GROUP BY "appointments"."organization_id", ("date_trunc"('day'::"text", "appointments"."appointment_date")), "appointments"."department_id"
  WITH NO DATA;


ALTER TABLE "public"."analytics_daily_appointments" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."analytics_events" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "organization_id" "uuid",
    "event_type" "text" NOT NULL,
    "event_data" "jsonb" NOT NULL,
    "user_id" "uuid",
    "timestamp" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."analytics_events" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."analytics_metrics" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "organization_id" "uuid",
    "metric_name" "text" NOT NULL,
    "metric_value" numeric NOT NULL,
    "dimensions" "jsonb" DEFAULT '{}'::"jsonb",
    "timestamp" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."analytics_metrics" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."medical_records" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "patient_id" "uuid",
    "provider_id" "uuid",
    "visit_date" timestamp with time zone NOT NULL,
    "chief_complaint" "text",
    "diagnosis" "text"[],
    "treatment_plan" "text",
    "notes" "text",
    "attachments" "jsonb",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "search_vector" "tsvector",
    "organization_id" "uuid",
    "department_id" "uuid"
);


ALTER TABLE "public"."medical_records" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."medications" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "patient_id" "uuid",
    "provider_id" "uuid",
    "medication_name" "text" NOT NULL,
    "dosage" "text" NOT NULL,
    "frequency" "text" NOT NULL,
    "start_date" "date" NOT NULL,
    "end_date" "date",
    "instructions" "text",
    "active" boolean DEFAULT true,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."medications" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."patients" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "uuid",
    "first_name" "text" NOT NULL,
    "last_name" "text" NOT NULL,
    "date_of_birth" "date" NOT NULL,
    "gender" "public"."gender" NOT NULL,
    "phone" "text",
    "email" "text",
    "address" "text",
    "emergency_contact" "text",
    "insurance_info" "jsonb",
    "medical_history" "jsonb",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "search_vector" "tsvector",
    "organization_id" "uuid"
);


ALTER TABLE "public"."patients" OWNER TO "postgres";


CREATE MATERIALIZED VIEW "public"."analytics_patient_metrics" AS
 SELECT "p"."organization_id",
    "count"(DISTINCT "p"."id") AS "total_patients",
    "count"(DISTINCT
        CASE
            WHEN ("a"."id" IS NOT NULL) THEN "p"."id"
            ELSE NULL::"uuid"
        END) AS "active_patients",
    "avg"(EXTRACT(year FROM "age"(("p"."date_of_birth")::timestamp with time zone))) AS "avg_patient_age",
    "count"(DISTINCT "a"."id") AS "total_appointments",
    "count"(DISTINCT "mr"."id") AS "total_medical_records",
    "count"(DISTINCT "m"."id") AS "total_medications"
   FROM ((("public"."patients" "p"
     LEFT JOIN "public"."appointments" "a" ON (("a"."patient_id" = "p"."id")))
     LEFT JOIN "public"."medical_records" "mr" ON (("mr"."patient_id" = "p"."id")))
     LEFT JOIN "public"."medications" "m" ON (("m"."patient_id" = "p"."id")))
  GROUP BY "p"."organization_id"
  WITH NO DATA;


ALTER TABLE "public"."analytics_patient_metrics" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."healthcare_providers" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "uuid",
    "first_name" "text" NOT NULL,
    "last_name" "text" NOT NULL,
    "provider_type" "public"."provider_type" NOT NULL,
    "specialization" "text",
    "license_number" "text",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "organization_id" "uuid",
    "department_id" "uuid",
    "role" "public"."user_role",
    "specialties" "text"[],
    "credentials" "jsonb",
    "schedule_settings" "jsonb",
    "permissions" "jsonb"
);


ALTER TABLE "public"."healthcare_providers" OWNER TO "postgres";


CREATE MATERIALIZED VIEW "public"."analytics_provider_metrics" AS
 SELECT "hp"."organization_id",
    "hp"."id" AS "provider_id",
    (("hp"."first_name" || ' '::"text") || "hp"."last_name") AS "provider_name",
    "hp"."role",
    "count"(DISTINCT "a"."id") AS "total_appointments",
    "count"(DISTINCT "a"."patient_id") AS "unique_patients",
    "count"(DISTINCT "mr"."id") AS "medical_records_created",
    "count"(DISTINCT "m"."id") AS "medications_prescribed"
   FROM ((("public"."healthcare_providers" "hp"
     LEFT JOIN "public"."appointments" "a" ON (("a"."provider_id" = "hp"."id")))
     LEFT JOIN "public"."medical_records" "mr" ON (("mr"."provider_id" = "hp"."id")))
     LEFT JOIN "public"."medications" "m" ON (("m"."provider_id" = "hp"."id")))
  GROUP BY "hp"."organization_id", "hp"."id", "hp"."first_name", "hp"."last_name", "hp"."role"
  WITH NO DATA;


ALTER TABLE "public"."analytics_provider_metrics" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."audit_logs" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "table_name" "text" NOT NULL,
    "record_id" "uuid" NOT NULL,
    "action" "text" NOT NULL,
    "old_data" "jsonb",
    "new_data" "jsonb",
    "changed_by" "uuid",
    "timestamp" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."audit_logs" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."billing_codes" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "code" "text" NOT NULL,
    "description" "text" NOT NULL,
    "type" "text" NOT NULL,
    "effective_date" "date" NOT NULL,
    "end_date" "date",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."billing_codes" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."care_team_members" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "patient_id" "uuid",
    "provider_id" "uuid",
    "role" "text" NOT NULL,
    "start_date" "date" NOT NULL,
    "end_date" "date",
    "primary_contact" boolean DEFAULT false,
    "notes" "text",
    "metadata" "jsonb" DEFAULT '{}'::"jsonb",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "valid_date_range" CHECK ((("end_date" IS NULL) OR ("end_date" >= "start_date")))
);


ALTER TABLE "public"."care_team_members" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."claims" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "patient_id" "uuid",
    "provider_id" "uuid",
    "insurance_provider_id" "uuid",
    "service_date" "date" NOT NULL,
    "billing_codes" "jsonb" NOT NULL,
    "status" "text" NOT NULL,
    "amount" numeric NOT NULL,
    "submitted_at" timestamp with time zone,
    "processed_at" timestamp with time zone,
    "metadata" "jsonb" DEFAULT '{}'::"jsonb",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."claims" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."clinical_notes" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "medical_record_id" "uuid",
    "note_type" "text" NOT NULL,
    "content" "text" NOT NULL,
    "signed_by" "uuid",
    "signed_at" timestamp with time zone,
    "metadata" "jsonb" DEFAULT '{}'::"jsonb",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."clinical_notes" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."conversation_participants" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "conversation_id" "uuid",
    "user_id" "uuid",
    "role" "text" DEFAULT 'member'::"text",
    "last_read_at" timestamp with time zone,
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."conversation_participants" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."conversations" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "organization_id" "uuid",
    "type" "public"."conversation_type" NOT NULL,
    "title" "text",
    "metadata" "jsonb" DEFAULT '{}'::"jsonb",
    "created_by" "uuid",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."conversations" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."departments" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "location_id" "uuid",
    "name" "text" NOT NULL,
    "type" "public"."department_type" NOT NULL,
    "settings" "jsonb" DEFAULT '{}'::"jsonb",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."departments" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."documents" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "organization_id" "uuid",
    "patient_id" "uuid",
    "document_type" "text" NOT NULL,
    "title" "text" NOT NULL,
    "description" "text",
    "file_path" "text" NOT NULL,
    "mime_type" "text" NOT NULL,
    "metadata" "jsonb" DEFAULT '{}'::"jsonb",
    "created_by" "uuid",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."documents" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."education_materials" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "title" "text" NOT NULL,
    "content" "text" NOT NULL,
    "category" "text" NOT NULL,
    "language" "text" DEFAULT 'en'::"text",
    "format" "public"."material_format" NOT NULL,
    "metadata" "jsonb" DEFAULT '{}'::"jsonb",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."education_materials" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."immunizations" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "patient_id" "uuid",
    "vaccine_name" "text" NOT NULL,
    "vaccine_code" "text",
    "dose_number" integer,
    "administered_date" "date" NOT NULL,
    "administered_by" "uuid",
    "manufacturer" "text",
    "lot_number" "text",
    "expiration_date" "date",
    "site" "public"."administration_site",
    "route" "public"."administration_route",
    "notes" "text",
    "metadata" "jsonb" DEFAULT '{}'::"jsonb",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "valid_dose_number" CHECK (("dose_number" > 0)),
    CONSTRAINT "valid_expiration" CHECK (("expiration_date" >= "administered_date"))
);


ALTER TABLE "public"."immunizations" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."insurance_providers" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "name" "text" NOT NULL,
    "contact_info" "jsonb" NOT NULL,
    "settings" "jsonb" DEFAULT '{}'::"jsonb",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."insurance_providers" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."inventory_items" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "organization_id" "uuid",
    "location_id" "uuid",
    "name" "text" NOT NULL,
    "type" "text" NOT NULL,
    "quantity" integer NOT NULL,
    "unit" "text" NOT NULL,
    "minimum_quantity" integer,
    "location" "text",
    "metadata" "jsonb" DEFAULT '{}'::"jsonb",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."inventory_items" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."inventory_transactions" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "item_id" "uuid",
    "transaction_type" "text" NOT NULL,
    "quantity" integer NOT NULL,
    "performed_by" "uuid",
    "reason" "text",
    "metadata" "jsonb" DEFAULT '{}'::"jsonb",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."inventory_transactions" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."lab_results" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "patient_id" "uuid",
    "provider_id" "uuid",
    "test_name" "text" NOT NULL,
    "test_date" timestamp with time zone NOT NULL,
    "results" "jsonb" NOT NULL,
    "normal_range" "jsonb",
    "notes" "text",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."lab_results" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."locations" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "organization_id" "uuid",
    "name" "text" NOT NULL,
    "type" "text" NOT NULL,
    "address" "jsonb" NOT NULL,
    "contact_info" "jsonb" NOT NULL,
    "operating_hours" "jsonb",
    "settings" "jsonb" DEFAULT '{}'::"jsonb",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."locations" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."message_states" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "message_id" "uuid",
    "user_id" "uuid",
    "state" "public"."message_state" NOT NULL,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."message_states" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."messages" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "conversation_id" "uuid",
    "sender_id" "uuid",
    "content" "text" NOT NULL,
    "attachments" "jsonb" DEFAULT '[]'::"jsonb",
    "metadata" "jsonb" DEFAULT '{}'::"jsonb",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."messages" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."notification_preferences" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "user_id" "uuid",
    "type" "public"."notification_type" NOT NULL,
    "email_enabled" boolean DEFAULT true,
    "sms_enabled" boolean DEFAULT false,
    "push_enabled" boolean DEFAULT true,
    "in_app_enabled" boolean DEFAULT true,
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."notification_preferences" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."notification_templates" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "organization_id" "uuid",
    "type" "public"."notification_type" NOT NULL,
    "name" "text" NOT NULL,
    "subject_template" "text" NOT NULL,
    "content_template" "text" NOT NULL,
    "metadata_template" "jsonb" DEFAULT '{}'::"jsonb",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."notification_templates" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."notifications" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "organization_id" "uuid",
    "type" "public"."notification_type" NOT NULL,
    "priority" "public"."notification_priority" DEFAULT 'medium'::"public"."notification_priority" NOT NULL,
    "status" "public"."notification_status" DEFAULT 'pending'::"public"."notification_status" NOT NULL,
    "sender_id" "uuid",
    "recipient_id" "uuid",
    "title" "text" NOT NULL,
    "content" "text" NOT NULL,
    "metadata" "jsonb" DEFAULT '{}'::"jsonb",
    "scheduled_for" timestamp with time zone,
    "sent_at" timestamp with time zone,
    "read_at" timestamp with time zone,
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."notifications" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."orders" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "patient_id" "uuid",
    "ordering_provider_id" "uuid",
    "order_type" "public"."order_type" NOT NULL,
    "status" "public"."order_status" DEFAULT 'pending'::"public"."order_status",
    "priority" "public"."order_priority" DEFAULT 'routine'::"public"."order_priority",
    "order_details" "jsonb" NOT NULL,
    "diagnosis_codes" "jsonb",
    "notes" "text",
    "ordered_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "scheduled_date" timestamp with time zone,
    "completed_at" timestamp with time zone,
    "metadata" "jsonb" DEFAULT '{}'::"jsonb",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "valid_order_dates" CHECK (((("scheduled_date" IS NULL) OR ("scheduled_date" >= "ordered_at")) AND (("completed_at" IS NULL) OR ("completed_at" >= "ordered_at"))))
);


ALTER TABLE "public"."orders" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."organization_invites" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "email" "text" NOT NULL,
    "organization_id" "uuid",
    "role" "public"."user_role" NOT NULL,
    "status" "text" DEFAULT 'pending'::"text",
    "invited_by" "uuid",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "expires_at" timestamp with time zone DEFAULT ("now"() + '7 days'::interval),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    CONSTRAINT "organization_invites_status_check" CHECK (("status" = ANY (ARRAY['pending'::"text", 'accepted'::"text", 'declined'::"text"])))
);


ALTER TABLE "public"."organization_invites" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."organizations" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "name" "text" NOT NULL,
    "type" "text" NOT NULL,
    "settings" "jsonb" DEFAULT '{}'::"jsonb",
    "subscription_tier" "text",
    "billing_info" "jsonb",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "owner_id" "uuid"
);


ALTER TABLE "public"."organizations" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."patient_alerts" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "patient_id" "uuid",
    "alert_type" "text" NOT NULL,
    "description" "text" NOT NULL,
    "severity" "public"."alert_severity" NOT NULL,
    "status" "public"."alert_status" DEFAULT 'active'::"public"."alert_status",
    "start_date" "date" NOT NULL,
    "end_date" "date",
    "created_by" "uuid",
    "metadata" "jsonb" DEFAULT '{}'::"jsonb",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "valid_date_range" CHECK ((("end_date" IS NULL) OR ("end_date" >= "start_date")))
);


ALTER TABLE "public"."patient_alerts" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."patient_education_records" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "patient_id" "uuid",
    "material_id" "uuid",
    "provider_id" "uuid",
    "provided_date" timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "notes" "text",
    "metadata" "jsonb" DEFAULT '{}'::"jsonb",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."patient_education_records" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."patient_portal_settings" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "patient_id" "uuid",
    "preferences" "jsonb" DEFAULT '{}'::"jsonb",
    "communication_settings" "jsonb" DEFAULT '{}'::"jsonb",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."patient_portal_settings" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."patient_questionnaires" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "patient_id" "uuid",
    "questionnaire_type" "text" NOT NULL,
    "responses" "jsonb" NOT NULL,
    "completed_at" timestamp with time zone,
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."patient_questionnaires" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."referrals" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "patient_id" "uuid",
    "referring_provider_id" "uuid",
    "referred_to_provider_id" "uuid",
    "reason" "text" NOT NULL,
    "priority" "public"."referral_priority" DEFAULT 'routine'::"public"."referral_priority",
    "status" "public"."referral_status" DEFAULT 'pending'::"public"."referral_status",
    "referral_date" "date" NOT NULL,
    "scheduled_date" "date",
    "completed_date" "date",
    "notes" "text",
    "metadata" "jsonb" DEFAULT '{}'::"jsonb",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "valid_referral_dates" CHECK (((("scheduled_date" IS NULL) OR ("scheduled_date" >= "referral_date")) AND (("completed_date" IS NULL) OR ("completed_date" >= "referral_date"))))
);


ALTER TABLE "public"."referrals" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."role_definitions" (
    "role" "public"."user_role" NOT NULL,
    "description" "text" NOT NULL,
    "base_permissions" "jsonb" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."role_definitions" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."role_permissions" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "role" "public"."user_role" NOT NULL,
    "resource" "text" NOT NULL,
    "actions" "text"[] NOT NULL,
    "conditions" "jsonb" DEFAULT '{}'::"jsonb",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."role_permissions" OWNER TO "postgres";


CREATE OR REPLACE VIEW "public"."secure_analytics_daily_appointments" AS
 SELECT "analytics_daily_appointments"."organization_id",
    "analytics_daily_appointments"."date",
    "analytics_daily_appointments"."department_id",
    "analytics_daily_appointments"."total_appointments",
    "analytics_daily_appointments"."completed_appointments",
    "analytics_daily_appointments"."cancelled_appointments",
    "analytics_daily_appointments"."avg_duration"
   FROM "public"."analytics_daily_appointments"
  WHERE "public"."can_access_analytics"("analytics_daily_appointments"."organization_id");


ALTER TABLE "public"."secure_analytics_daily_appointments" OWNER TO "postgres";


CREATE OR REPLACE VIEW "public"."secure_analytics_patient_metrics" AS
 SELECT "analytics_patient_metrics"."organization_id",
    "analytics_patient_metrics"."total_patients",
    "analytics_patient_metrics"."active_patients",
    "analytics_patient_metrics"."avg_patient_age",
    "analytics_patient_metrics"."total_appointments",
    "analytics_patient_metrics"."total_medical_records",
    "analytics_patient_metrics"."total_medications"
   FROM "public"."analytics_patient_metrics"
  WHERE "public"."can_access_analytics"("analytics_patient_metrics"."organization_id");


ALTER TABLE "public"."secure_analytics_patient_metrics" OWNER TO "postgres";


CREATE OR REPLACE VIEW "public"."secure_analytics_provider_metrics" AS
 SELECT "analytics_provider_metrics"."organization_id",
    "analytics_provider_metrics"."provider_id",
    "analytics_provider_metrics"."provider_name",
    "analytics_provider_metrics"."role",
    "analytics_provider_metrics"."total_appointments",
    "analytics_provider_metrics"."unique_patients",
    "analytics_provider_metrics"."medical_records_created",
    "analytics_provider_metrics"."medications_prescribed"
   FROM "public"."analytics_provider_metrics"
  WHERE "public"."can_access_analytics"("analytics_provider_metrics"."organization_id");


ALTER TABLE "public"."secure_analytics_provider_metrics" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."task_comments" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "task_id" "uuid",
    "user_id" "uuid",
    "content" "text" NOT NULL,
    "attachments" "jsonb" DEFAULT '[]'::"jsonb",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."task_comments" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."task_watchers" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "task_id" "uuid",
    "user_id" "uuid",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."task_watchers" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."tasks" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "organization_id" "uuid",
    "department_id" "uuid",
    "title" "text" NOT NULL,
    "description" "text",
    "priority" "public"."task_priority" DEFAULT 'medium'::"public"."task_priority" NOT NULL,
    "status" "public"."task_status" DEFAULT 'pending'::"public"."task_status" NOT NULL,
    "assigned_to" "uuid",
    "assigned_by" "uuid",
    "due_date" timestamp with time zone,
    "completed_at" timestamp with time zone,
    "related_to" "jsonb" DEFAULT '{}'::"jsonb",
    "metadata" "jsonb" DEFAULT '{}'::"jsonb",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."tasks" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."teams" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "department_id" "uuid",
    "name" "text" NOT NULL,
    "description" "text",
    "leader_id" "uuid",
    "members" "jsonb" DEFAULT '[]'::"jsonb",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."teams" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."templates" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "organization_id" "uuid",
    "name" "text" NOT NULL,
    "type" "text" NOT NULL,
    "content" "text" NOT NULL,
    "metadata" "jsonb" DEFAULT '{}'::"jsonb",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."templates" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."user_roles" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "organization_id" "uuid",
    "role" "public"."user_role" NOT NULL,
    "department_id" "uuid",
    "custom_permissions" "jsonb" DEFAULT '{}'::"jsonb",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "invitation_status" "text" DEFAULT 'direct'::"text",
    CONSTRAINT "user_roles_invitation_status_check" CHECK (("invitation_status" = ANY (ARRAY['direct'::"text", 'invited'::"text", 'accepted'::"text"])))
);


ALTER TABLE "public"."user_roles" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."vital_signs" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "patient_id" "uuid",
    "recorded_by" "uuid",
    "recorded_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "blood_pressure_systolic" integer,
    "blood_pressure_diastolic" integer,
    "heart_rate" integer,
    "respiratory_rate" integer,
    "temperature" numeric,
    "temperature_unit" "text" DEFAULT 'C'::"text",
    "oxygen_saturation" integer,
    "height" numeric,
    "weight" numeric,
    "bmi" numeric,
    "pain_level" integer,
    "notes" "text",
    "metadata" "jsonb" DEFAULT '{}'::"jsonb",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "valid_blood_pressure_diastolic" CHECK ((("blood_pressure_diastolic" > 0) AND ("blood_pressure_diastolic" < 200))),
    CONSTRAINT "valid_blood_pressure_systolic" CHECK ((("blood_pressure_systolic" > 0) AND ("blood_pressure_systolic" < 300))),
    CONSTRAINT "valid_heart_rate" CHECK ((("heart_rate" > 0) AND ("heart_rate" < 300))),
    CONSTRAINT "valid_oxygen_saturation" CHECK ((("oxygen_saturation" >= 0) AND ("oxygen_saturation" <= 100))),
    CONSTRAINT "valid_pain_level" CHECK ((("pain_level" >= 0) AND ("pain_level" <= 10))),
    CONSTRAINT "valid_respiratory_rate" CHECK ((("respiratory_rate" > 0) AND ("respiratory_rate" < 100)))
);


ALTER TABLE "public"."vital_signs" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."workflow_instances" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "workflow_id" "uuid",
    "status" "public"."workflow_status" DEFAULT 'pending'::"public"."workflow_status" NOT NULL,
    "context" "jsonb" NOT NULL,
    "current_step" integer DEFAULT 0,
    "results" "jsonb" DEFAULT '[]'::"jsonb",
    "started_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "completed_at" timestamp with time zone,
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."workflow_instances" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."workflow_logs" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "workflow_instance_id" "uuid",
    "step_number" integer NOT NULL,
    "step_name" "text" NOT NULL,
    "status" "public"."workflow_status" NOT NULL,
    "message" "text",
    "details" "jsonb" DEFAULT '{}'::"jsonb",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."workflow_logs" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."workflows" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "organization_id" "uuid",
    "type" "public"."workflow_type" NOT NULL,
    "name" "text" NOT NULL,
    "description" "text",
    "trigger_type" "public"."workflow_trigger" NOT NULL,
    "trigger_config" "jsonb" NOT NULL,
    "steps" "jsonb"[] NOT NULL,
    "enabled" boolean DEFAULT true,
    "created_by" "uuid",
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE "public"."workflows" OWNER TO "postgres";


ALTER TABLE ONLY "public"."activity_logs"
    ADD CONSTRAINT "activity_logs_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."allergies"
    ADD CONSTRAINT "allergies_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."analytics_events"
    ADD CONSTRAINT "analytics_events_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."analytics_metrics"
    ADD CONSTRAINT "analytics_metrics_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."appointments"
    ADD CONSTRAINT "appointments_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."audit_logs"
    ADD CONSTRAINT "audit_logs_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."billing_codes"
    ADD CONSTRAINT "billing_codes_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."care_team_members"
    ADD CONSTRAINT "care_team_members_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."claims"
    ADD CONSTRAINT "claims_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."clinical_notes"
    ADD CONSTRAINT "clinical_notes_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."conversation_participants"
    ADD CONSTRAINT "conversation_participants_conversation_id_user_id_key" UNIQUE ("conversation_id", "user_id");



ALTER TABLE ONLY "public"."conversation_participants"
    ADD CONSTRAINT "conversation_participants_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."conversations"
    ADD CONSTRAINT "conversations_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."departments"
    ADD CONSTRAINT "departments_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."documents"
    ADD CONSTRAINT "documents_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."education_materials"
    ADD CONSTRAINT "education_materials_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."locations"
    ADD CONSTRAINT "locations_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."healthcare_providers"
    ADD CONSTRAINT "healthcare_providers_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."immunizations"
    ADD CONSTRAINT "immunizations_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."insurance_providers"
    ADD CONSTRAINT "insurance_providers_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."inventory_items"
    ADD CONSTRAINT "inventory_items_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."inventory_transactions"
    ADD CONSTRAINT "inventory_transactions_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."lab_results"
    ADD CONSTRAINT "lab_results_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."medical_records"
    ADD CONSTRAINT "medical_records_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."medications"
    ADD CONSTRAINT "medications_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."message_states"
    ADD CONSTRAINT "message_states_message_id_user_id_key" UNIQUE ("message_id", "user_id");



ALTER TABLE ONLY "public"."message_states"
    ADD CONSTRAINT "message_states_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."messages"
    ADD CONSTRAINT "messages_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."notification_preferences"
    ADD CONSTRAINT "notification_preferences_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."notification_preferences"
    ADD CONSTRAINT "notification_preferences_user_id_type_key" UNIQUE ("user_id", "type");



ALTER TABLE ONLY "public"."notification_templates"
    ADD CONSTRAINT "notification_templates_organization_id_name_key" UNIQUE ("organization_id", "name");



ALTER TABLE ONLY "public"."notification_templates"
    ADD CONSTRAINT "notification_templates_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."notifications"
    ADD CONSTRAINT "notifications_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."orders"
    ADD CONSTRAINT "orders_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."organization_invites"
    ADD CONSTRAINT "organization_invites_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."organizations"
    ADD CONSTRAINT "organizations_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."patient_alerts"
    ADD CONSTRAINT "patient_alerts_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."patient_education_records"
    ADD CONSTRAINT "patient_education_records_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."patient_portal_settings"
    ADD CONSTRAINT "patient_portal_settings_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."patient_questionnaires"
    ADD CONSTRAINT "patient_questionnaires_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."patients"
    ADD CONSTRAINT "patients_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."referrals"
    ADD CONSTRAINT "referrals_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."role_definitions"
    ADD CONSTRAINT "role_definitions_pkey" PRIMARY KEY ("role");



ALTER TABLE ONLY "public"."role_permissions"
    ADD CONSTRAINT "role_permissions_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."role_permissions"
    ADD CONSTRAINT "role_permissions_role_resource_key" UNIQUE ("role", "resource");



ALTER TABLE ONLY "public"."task_comments"
    ADD CONSTRAINT "task_comments_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."task_watchers"
    ADD CONSTRAINT "task_watchers_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."task_watchers"
    ADD CONSTRAINT "task_watchers_task_id_user_id_key" UNIQUE ("task_id", "user_id");



ALTER TABLE ONLY "public"."tasks"
    ADD CONSTRAINT "tasks_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."teams"
    ADD CONSTRAINT "teams_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."templates"
    ADD CONSTRAINT "templates_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."user_roles"
    ADD CONSTRAINT "user_roles_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."user_roles"
    ADD CONSTRAINT "user_roles_user_id_organization_id_role_key" UNIQUE ("user_id", "organization_id", "role");



ALTER TABLE ONLY "public"."vital_signs"
    ADD CONSTRAINT "vital_signs_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."workflow_instances"
    ADD CONSTRAINT "workflow_instances_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."workflow_logs"
    ADD CONSTRAINT "workflow_logs_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."workflows"
    ADD CONSTRAINT "workflows_pkey" PRIMARY KEY ("id");



CREATE INDEX "idx_activity_logs_created_at" ON "public"."activity_logs" USING "btree" ("created_at");



CREATE INDEX "idx_activity_logs_organization_id" ON "public"."activity_logs" USING "btree" ("organization_id");



CREATE INDEX "idx_activity_logs_resource_type" ON "public"."activity_logs" USING "btree" ("resource_type");



CREATE INDEX "idx_activity_logs_user_id" ON "public"."activity_logs" USING "btree" ("user_id");



CREATE INDEX "idx_allergies_allergen" ON "public"."allergies" USING "btree" ("allergen");



CREATE INDEX "idx_allergies_patient_id" ON "public"."allergies" USING "btree" ("patient_id");



CREATE INDEX "idx_allergies_reported_by" ON "public"."allergies" USING "btree" ("reported_by");



CREATE INDEX "idx_allergies_severity" ON "public"."allergies" USING "btree" ("severity");



CREATE INDEX "idx_allergies_status" ON "public"."allergies" USING "btree" ("status");



CREATE INDEX "idx_analytics_events_org_type" ON "public"."analytics_events" USING "btree" ("organization_id", "event_type");



CREATE INDEX "idx_analytics_events_organization_id" ON "public"."analytics_events" USING "btree" ("organization_id");



CREATE INDEX "idx_analytics_events_timestamp_brin" ON "public"."analytics_events" USING "brin" ("timestamp");



CREATE INDEX "idx_analytics_events_user_id" ON "public"."analytics_events" USING "btree" ("user_id");



CREATE INDEX "idx_analytics_metrics_org_metric" ON "public"."analytics_metrics" USING "btree" ("organization_id", "metric_name");



CREATE INDEX "idx_analytics_metrics_organization_id" ON "public"."analytics_metrics" USING "btree" ("organization_id");



CREATE INDEX "idx_analytics_metrics_timestamp_brin" ON "public"."analytics_metrics" USING "brin" ("timestamp");



CREATE INDEX "idx_appointments_date_brin" ON "public"."appointments" USING "brin" ("appointment_date") WITH ("pages_per_range"='32');



CREATE INDEX "idx_appointments_department_id" ON "public"."appointments" USING "btree" ("department_id");



CREATE INDEX "idx_appointments_organization_id" ON "public"."appointments" USING "btree" ("organization_id");



CREATE INDEX "idx_appointments_patient_date" ON "public"."appointments" USING "btree" ("patient_id", "appointment_date");



CREATE INDEX "idx_appointments_patient_id" ON "public"."appointments" USING "btree" ("patient_id");



CREATE INDEX "idx_appointments_provider_date" ON "public"."appointments" USING "btree" ("provider_id", "appointment_date");



CREATE INDEX "idx_appointments_provider_id" ON "public"."appointments" USING "btree" ("provider_id");



CREATE INDEX "idx_appointments_provider_patient_date" ON "public"."appointments" USING "btree" ("provider_id", "patient_id", "appointment_date");



CREATE INDEX "idx_appointments_provider_status" ON "public"."appointments" USING "btree" ("provider_id", "status", "appointment_date");



CREATE INDEX "idx_appointments_status_date" ON "public"."appointments" USING "btree" ("status", "appointment_date");



CREATE INDEX "idx_appointments_status_provider_date" ON "public"."appointments" USING "btree" ("status", "provider_id", "appointment_date") WHERE ("status" = 'scheduled'::"public"."appointment_status");



CREATE INDEX "idx_audit_logs_changed_by" ON "public"."audit_logs" USING "btree" ("changed_by");



CREATE INDEX "idx_audit_logs_record_id" ON "public"."audit_logs" USING "btree" ("record_id");



CREATE INDEX "idx_audit_logs_table_name" ON "public"."audit_logs" USING "btree" ("table_name");



CREATE INDEX "idx_audit_logs_timestamp" ON "public"."audit_logs" USING "btree" ("timestamp");



CREATE INDEX "idx_billing_codes_code" ON "public"."billing_codes" USING "btree" ("code");



CREATE INDEX "idx_billing_codes_type" ON "public"."billing_codes" USING "btree" ("type");



CREATE INDEX "idx_care_team_members_patient_id" ON "public"."care_team_members" USING "btree" ("patient_id");



CREATE INDEX "idx_care_team_members_primary_contact" ON "public"."care_team_members" USING "btree" ("primary_contact");



CREATE INDEX "idx_care_team_members_provider_id" ON "public"."care_team_members" USING "btree" ("provider_id");



CREATE INDEX "idx_care_team_members_role" ON "public"."care_team_members" USING "btree" ("role");



CREATE INDEX "idx_claims_insurance_provider_id" ON "public"."claims" USING "btree" ("insurance_provider_id");



CREATE INDEX "idx_claims_patient_id" ON "public"."claims" USING "btree" ("patient_id");



CREATE INDEX "idx_claims_provider_id" ON "public"."claims" USING "btree" ("provider_id");



CREATE INDEX "idx_claims_service_date" ON "public"."claims" USING "btree" ("service_date");



CREATE INDEX "idx_claims_status" ON "public"."claims" USING "btree" ("status");



CREATE INDEX "idx_clinical_notes_medical_record_id" ON "public"."clinical_notes" USING "btree" ("medical_record_id");



CREATE INDEX "idx_clinical_notes_signed_by" ON "public"."clinical_notes" USING "btree" ("signed_by");



CREATE INDEX "idx_conversation_participants_conversation_id" ON "public"."conversation_participants" USING "btree" ("conversation_id");



CREATE INDEX "idx_conversation_participants_user_id" ON "public"."conversation_participants" USING "btree" ("user_id");



CREATE INDEX "idx_conversations_created_by" ON "public"."conversations" USING "btree" ("created_by");



CREATE INDEX "idx_conversations_organization_id" ON "public"."conversations" USING "btree" ("organization_id");



CREATE UNIQUE INDEX "idx_daily_appointments_org_date_dept" ON "public"."analytics_daily_appointments" USING "btree" ("organization_id", "date", "department_id");



CREATE INDEX "idx_departments_location_id" ON "public"."departments" USING "btree" ("location_id");



CREATE INDEX "idx_documents_created_by" ON "public"."documents" USING "btree" ("created_by");



CREATE INDEX "idx_documents_document_type" ON "public"."documents" USING "btree" ("document_type");



CREATE INDEX "idx_documents_organization_id" ON "public"."documents" USING "btree" ("organization_id");



CREATE INDEX "idx_documents_patient_id" ON "public"."documents" USING "btree" ("patient_id");



CREATE INDEX "idx_education_materials_category" ON "public"."education_materials" USING "btree" ("category");



CREATE INDEX "idx_education_materials_format" ON "public"."education_materials" USING "btree" ("format");



CREATE INDEX "idx_education_materials_language" ON "public"."education_materials" USING "btree" ("language");



CREATE INDEX "idx_locations_organization_id" ON "public"."locations" USING "btree" ("organization_id");



CREATE INDEX "idx_healthcare_providers_department_id" ON "public"."healthcare_providers" USING "btree" ("department_id");



CREATE INDEX "idx_healthcare_providers_organization_id" ON "public"."healthcare_providers" USING "btree" ("organization_id");



CREATE INDEX "idx_healthcare_providers_user_id" ON "public"."healthcare_providers" USING "btree" ("user_id");



CREATE INDEX "idx_immunizations_administered_by" ON "public"."immunizations" USING "btree" ("administered_by");



CREATE INDEX "idx_immunizations_administered_date" ON "public"."immunizations" USING "btree" ("administered_date");



CREATE INDEX "idx_immunizations_patient_id" ON "public"."immunizations" USING "btree" ("patient_id");



CREATE INDEX "idx_immunizations_vaccine_code" ON "public"."immunizations" USING "btree" ("vaccine_code");



CREATE INDEX "idx_immunizations_vaccine_name" ON "public"."immunizations" USING "btree" ("vaccine_name");



CREATE INDEX "idx_inventory_items_location_id" ON "public"."inventory_items" USING "btree" ("location_id");



CREATE INDEX "idx_inventory_items_organization_id" ON "public"."inventory_items" USING "btree" ("organization_id");



CREATE INDEX "idx_inventory_items_type" ON "public"."inventory_items" USING "btree" ("type");



CREATE INDEX "idx_inventory_transactions_item_id" ON "public"."inventory_transactions" USING "btree" ("item_id");



CREATE INDEX "idx_inventory_transactions_performed_by" ON "public"."inventory_transactions" USING "btree" ("performed_by");



CREATE INDEX "idx_lab_results_patient_date" ON "public"."lab_results" USING "btree" ("patient_id", "test_date");



CREATE INDEX "idx_lab_results_patient_id" ON "public"."lab_results" USING "btree" ("patient_id");



CREATE INDEX "idx_lab_results_provider_id" ON "public"."lab_results" USING "btree" ("provider_id");



CREATE INDEX "idx_medical_records_department_id" ON "public"."medical_records" USING "btree" ("department_id");



CREATE INDEX "idx_medical_records_org_dept" ON "public"."medical_records" USING "btree" ("organization_id", "department_id");



CREATE INDEX "idx_medical_records_organization_id" ON "public"."medical_records" USING "btree" ("organization_id");



CREATE INDEX "idx_medical_records_patient_date" ON "public"."medical_records" USING "btree" ("patient_id", "visit_date");



CREATE INDEX "idx_medical_records_patient_id" ON "public"."medical_records" USING "btree" ("patient_id");



CREATE INDEX "idx_medical_records_provider_id" ON "public"."medical_records" USING "btree" ("provider_id");



CREATE INDEX "idx_medical_records_provider_patient_date" ON "public"."medical_records" USING "btree" ("provider_id", "patient_id", "visit_date");



CREATE INDEX "idx_medical_records_provider_visit" ON "public"."medical_records" USING "btree" ("provider_id", "visit_date" DESC);



CREATE INDEX "idx_medical_records_visit_date_brin" ON "public"."medical_records" USING "brin" ("visit_date") WITH ("pages_per_range"='32');



CREATE INDEX "idx_medications_patient_active" ON "public"."medications" USING "btree" ("patient_id", "active");



CREATE INDEX "idx_medications_patient_id" ON "public"."medications" USING "btree" ("patient_id");



CREATE INDEX "idx_medications_provider_id" ON "public"."medications" USING "btree" ("provider_id");



CREATE INDEX "idx_medications_provider_patient_active" ON "public"."medications" USING "btree" ("provider_id", "patient_id", "active");



CREATE INDEX "idx_message_states_message_id" ON "public"."message_states" USING "btree" ("message_id");



CREATE INDEX "idx_message_states_user_id" ON "public"."message_states" USING "btree" ("user_id");



CREATE INDEX "idx_messages_conversation_id" ON "public"."messages" USING "btree" ("conversation_id");



CREATE INDEX "idx_messages_sender_id" ON "public"."messages" USING "btree" ("sender_id");



CREATE INDEX "idx_notification_preferences_user_id" ON "public"."notification_preferences" USING "btree" ("user_id");



CREATE INDEX "idx_notification_templates_organization_id" ON "public"."notification_templates" USING "btree" ("organization_id");



CREATE INDEX "idx_notifications_organization_id" ON "public"."notifications" USING "btree" ("organization_id");



CREATE INDEX "idx_notifications_recipient_id" ON "public"."notifications" USING "btree" ("recipient_id");



CREATE INDEX "idx_notifications_sender_id" ON "public"."notifications" USING "btree" ("sender_id");



CREATE INDEX "idx_orders_order_type" ON "public"."orders" USING "btree" ("order_type");



CREATE INDEX "idx_orders_ordered_at" ON "public"."orders" USING "btree" ("ordered_at");



CREATE INDEX "idx_orders_ordering_provider_id" ON "public"."orders" USING "btree" ("ordering_provider_id");



CREATE INDEX "idx_orders_patient_id" ON "public"."orders" USING "btree" ("patient_id");



CREATE INDEX "idx_orders_pending_priority" ON "public"."orders" USING "btree" ("patient_id", "priority") WHERE ("status" = 'pending'::"public"."order_status");



CREATE INDEX "idx_orders_priority" ON "public"."orders" USING "btree" ("priority");



CREATE INDEX "idx_orders_scheduled_date" ON "public"."orders" USING "btree" ("scheduled_date");



CREATE INDEX "idx_orders_status_priority" ON "public"."orders" USING "btree" ("status", "priority") WHERE ("status" = 'pending'::"public"."order_status");



CREATE INDEX "idx_org_invites_email" ON "public"."organization_invites" USING "btree" ("email");



CREATE INDEX "idx_org_invites_org_id" ON "public"."organization_invites" USING "btree" ("organization_id");



CREATE INDEX "idx_organization_invites_invited_by" ON "public"."organization_invites" USING "btree" ("invited_by");



CREATE INDEX "idx_organizations_owner_id" ON "public"."organizations" USING "btree" ("owner_id");



CREATE INDEX "idx_patient_alerts_alert_type" ON "public"."patient_alerts" USING "btree" ("alert_type");



CREATE INDEX "idx_patient_alerts_created_by" ON "public"."patient_alerts" USING "btree" ("created_by");



CREATE INDEX "idx_patient_alerts_patient_id" ON "public"."patient_alerts" USING "btree" ("patient_id");



CREATE INDEX "idx_patient_alerts_severity" ON "public"."patient_alerts" USING "btree" ("severity");



CREATE INDEX "idx_patient_alerts_status" ON "public"."patient_alerts" USING "btree" ("status");



CREATE INDEX "idx_patient_education_records_material_id" ON "public"."patient_education_records" USING "btree" ("material_id");



CREATE INDEX "idx_patient_education_records_patient_id" ON "public"."patient_education_records" USING "btree" ("patient_id");



CREATE INDEX "idx_patient_education_records_provided_date" ON "public"."patient_education_records" USING "btree" ("provided_date");



CREATE INDEX "idx_patient_education_records_provider_id" ON "public"."patient_education_records" USING "btree" ("provider_id");



CREATE UNIQUE INDEX "idx_patient_metrics_org" ON "public"."analytics_patient_metrics" USING "btree" ("organization_id");



CREATE INDEX "idx_patient_portal_settings_patient_id" ON "public"."patient_portal_settings" USING "btree" ("patient_id");



CREATE INDEX "idx_patient_questionnaires_patient_id" ON "public"."patient_questionnaires" USING "btree" ("patient_id");



CREATE INDEX "idx_patient_questionnaires_type" ON "public"."patient_questionnaires" USING "btree" ("questionnaire_type");



CREATE INDEX "idx_patients_organization_id" ON "public"."patients" USING "btree" ("organization_id");



CREATE INDEX "idx_patients_user_id" ON "public"."patients" USING "btree" ("user_id");



CREATE UNIQUE INDEX "idx_provider_metrics_org_provider" ON "public"."analytics_provider_metrics" USING "btree" ("organization_id", "provider_id");



CREATE INDEX "idx_referrals_patient_id" ON "public"."referrals" USING "btree" ("patient_id");



CREATE INDEX "idx_referrals_priority" ON "public"."referrals" USING "btree" ("priority");



CREATE INDEX "idx_referrals_referred_to_provider_id" ON "public"."referrals" USING "btree" ("referred_to_provider_id");



CREATE INDEX "idx_referrals_referring_provider_id" ON "public"."referrals" USING "btree" ("referring_provider_id");



CREATE INDEX "idx_referrals_status" ON "public"."referrals" USING "btree" ("status");



CREATE INDEX "idx_role_permissions_role" ON "public"."role_permissions" USING "btree" ("role");



CREATE INDEX "idx_task_comments_task_id" ON "public"."task_comments" USING "btree" ("task_id");



CREATE INDEX "idx_task_comments_user_id" ON "public"."task_comments" USING "btree" ("user_id");



CREATE INDEX "idx_task_watchers_task_id" ON "public"."task_watchers" USING "btree" ("task_id");



CREATE INDEX "idx_task_watchers_user_id" ON "public"."task_watchers" USING "btree" ("user_id");



CREATE INDEX "idx_tasks_assigned_by" ON "public"."tasks" USING "btree" ("assigned_by");



CREATE INDEX "idx_tasks_assigned_to" ON "public"."tasks" USING "btree" ("assigned_to");



CREATE INDEX "idx_tasks_department_id" ON "public"."tasks" USING "btree" ("department_id");



CREATE INDEX "idx_tasks_due_date" ON "public"."tasks" USING "btree" ("due_date");



CREATE INDEX "idx_tasks_organization_id" ON "public"."tasks" USING "btree" ("organization_id");



CREATE INDEX "idx_tasks_priority" ON "public"."tasks" USING "btree" ("priority");



CREATE INDEX "idx_tasks_status" ON "public"."tasks" USING "btree" ("status");



CREATE INDEX "idx_teams_department_id" ON "public"."teams" USING "btree" ("department_id");



CREATE INDEX "idx_teams_leader_id" ON "public"."teams" USING "btree" ("leader_id");



CREATE INDEX "idx_templates_organization_id" ON "public"."templates" USING "btree" ("organization_id");



CREATE INDEX "idx_templates_type" ON "public"."templates" USING "btree" ("type");



CREATE INDEX "idx_user_roles_department_id" ON "public"."user_roles" USING "btree" ("department_id");



CREATE INDEX "idx_user_roles_organization_id" ON "public"."user_roles" USING "btree" ("organization_id");



CREATE INDEX "idx_user_roles_role" ON "public"."user_roles" USING "btree" ("role");



CREATE INDEX "idx_user_roles_user_id" ON "public"."user_roles" USING "btree" ("user_id");



CREATE INDEX "idx_user_roles_user_org" ON "public"."user_roles" USING "btree" ("user_id", "organization_id");



CREATE INDEX "idx_vital_signs_patient_id" ON "public"."vital_signs" USING "btree" ("patient_id");



CREATE INDEX "idx_vital_signs_recorded_at_brin" ON "public"."vital_signs" USING "brin" ("recorded_at");



CREATE INDEX "idx_vital_signs_recorded_by" ON "public"."vital_signs" USING "btree" ("recorded_by");



CREATE INDEX "idx_workflow_instances_workflow_id" ON "public"."workflow_instances" USING "btree" ("workflow_id");



CREATE INDEX "idx_workflow_logs_instance_id" ON "public"."workflow_logs" USING "btree" ("workflow_instance_id");



CREATE INDEX "idx_workflows_created_by" ON "public"."workflows" USING "btree" ("created_by");



CREATE INDEX "idx_workflows_organization_id" ON "public"."workflows" USING "btree" ("organization_id");



CREATE OR REPLACE TRIGGER "audit_log_changes_trigger" AFTER INSERT OR DELETE OR UPDATE ON "public"."healthcare_providers" FOR EACH ROW EXECUTE FUNCTION "public"."audit_log_changes"();



CREATE OR REPLACE TRIGGER "medical_record_search_vector_update" BEFORE INSERT OR UPDATE ON "public"."medical_records" FOR EACH ROW EXECUTE FUNCTION "public"."update_medical_record_search_vector"();



CREATE OR REPLACE TRIGGER "patient_search_vector_update" BEFORE INSERT OR UPDATE ON "public"."patients" FOR EACH ROW EXECUTE FUNCTION "public"."update_patient_search_vector"();



CREATE OR REPLACE TRIGGER "update_organization_invites_updated_at" BEFORE UPDATE ON "public"."organization_invites" FOR EACH ROW EXECUTE FUNCTION "public"."update_updated_at_column"();



CREATE OR REPLACE TRIGGER "update_organizations_updated_at" BEFORE UPDATE ON "public"."organizations" FOR EACH ROW EXECUTE FUNCTION "public"."update_updated_at_column"();



CREATE OR REPLACE TRIGGER "update_user_roles_updated_at" BEFORE UPDATE ON "public"."user_roles" FOR EACH ROW EXECUTE FUNCTION "public"."update_updated_at_column"();



ALTER TABLE ONLY "public"."activity_logs"
    ADD CONSTRAINT "activity_logs_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."allergies"
    ADD CONSTRAINT "allergies_patient_id_fkey" FOREIGN KEY ("patient_id") REFERENCES "public"."patients"("id");



ALTER TABLE ONLY "public"."allergies"
    ADD CONSTRAINT "allergies_reported_by_fkey" FOREIGN KEY ("reported_by") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."analytics_events"
    ADD CONSTRAINT "analytics_events_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id");



ALTER TABLE ONLY "public"."analytics_events"
    ADD CONSTRAINT "analytics_events_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."analytics_metrics"
    ADD CONSTRAINT "analytics_metrics_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id");



ALTER TABLE ONLY "public"."appointments"
    ADD CONSTRAINT "appointments_department_id_fkey" FOREIGN KEY ("department_id") REFERENCES "public"."departments"("id");



ALTER TABLE ONLY "public"."appointments"
    ADD CONSTRAINT "appointments_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id");



ALTER TABLE ONLY "public"."appointments"
    ADD CONSTRAINT "appointments_patient_id_fkey" FOREIGN KEY ("patient_id") REFERENCES "public"."patients"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."appointments"
    ADD CONSTRAINT "appointments_provider_id_fkey" FOREIGN KEY ("provider_id") REFERENCES "public"."healthcare_providers"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."audit_logs"
    ADD CONSTRAINT "audit_logs_changed_by_fkey" FOREIGN KEY ("changed_by") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."care_team_members"
    ADD CONSTRAINT "care_team_members_patient_id_fkey" FOREIGN KEY ("patient_id") REFERENCES "public"."patients"("id");



ALTER TABLE ONLY "public"."care_team_members"
    ADD CONSTRAINT "care_team_members_provider_id_fkey" FOREIGN KEY ("provider_id") REFERENCES "public"."healthcare_providers"("id");



ALTER TABLE ONLY "public"."claims"
    ADD CONSTRAINT "claims_insurance_provider_id_fkey" FOREIGN KEY ("insurance_provider_id") REFERENCES "public"."insurance_providers"("id");



ALTER TABLE ONLY "public"."claims"
    ADD CONSTRAINT "claims_patient_id_fkey" FOREIGN KEY ("patient_id") REFERENCES "public"."patients"("id");



ALTER TABLE ONLY "public"."claims"
    ADD CONSTRAINT "claims_provider_id_fkey" FOREIGN KEY ("provider_id") REFERENCES "public"."healthcare_providers"("id");



ALTER TABLE ONLY "public"."clinical_notes"
    ADD CONSTRAINT "clinical_notes_medical_record_id_fkey" FOREIGN KEY ("medical_record_id") REFERENCES "public"."medical_records"("id");



ALTER TABLE ONLY "public"."clinical_notes"
    ADD CONSTRAINT "clinical_notes_signed_by_fkey" FOREIGN KEY ("signed_by") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."conversation_participants"
    ADD CONSTRAINT "conversation_participants_conversation_id_fkey" FOREIGN KEY ("conversation_id") REFERENCES "public"."conversations"("id");



ALTER TABLE ONLY "public"."conversation_participants"
    ADD CONSTRAINT "conversation_participants_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."conversations"
    ADD CONSTRAINT "conversations_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."conversations"
    ADD CONSTRAINT "conversations_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id");



ALTER TABLE ONLY "public"."departments"
    ADD CONSTRAINT "departments_location_id_fkey" FOREIGN KEY ("location_id") REFERENCES "public"."locations"("id");



ALTER TABLE ONLY "public"."documents"
    ADD CONSTRAINT "documents_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."documents"
    ADD CONSTRAINT "documents_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id");



ALTER TABLE ONLY "public"."documents"
    ADD CONSTRAINT "documents_patient_id_fkey" FOREIGN KEY ("patient_id") REFERENCES "public"."patients"("id");



ALTER TABLE ONLY "public"."locations"
    ADD CONSTRAINT "locations_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id");



ALTER TABLE ONLY "public"."role_permissions"
    ADD CONSTRAINT "fk_role_permissions_role" FOREIGN KEY ("role") REFERENCES "public"."role_definitions"("role") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."user_roles"
    ADD CONSTRAINT "fk_user_roles_role" FOREIGN KEY ("role") REFERENCES "public"."role_definitions"("role") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."healthcare_providers"
    ADD CONSTRAINT "healthcare_providers_department_id_fkey" FOREIGN KEY ("department_id") REFERENCES "public"."departments"("id");



ALTER TABLE ONLY "public"."healthcare_providers"
    ADD CONSTRAINT "healthcare_providers_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id");



ALTER TABLE ONLY "public"."healthcare_providers"
    ADD CONSTRAINT "healthcare_providers_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."immunizations"
    ADD CONSTRAINT "immunizations_administered_by_fkey" FOREIGN KEY ("administered_by") REFERENCES "public"."healthcare_providers"("id");



ALTER TABLE ONLY "public"."immunizations"
    ADD CONSTRAINT "immunizations_patient_id_fkey" FOREIGN KEY ("patient_id") REFERENCES "public"."patients"("id");



ALTER TABLE ONLY "public"."inventory_items"
    ADD CONSTRAINT "inventory_items_location_id_fkey" FOREIGN KEY ("location_id") REFERENCES "public"."locations"("id");



ALTER TABLE ONLY "public"."inventory_items"
    ADD CONSTRAINT "inventory_items_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id");



ALTER TABLE ONLY "public"."inventory_transactions"
    ADD CONSTRAINT "inventory_transactions_item_id_fkey" FOREIGN KEY ("item_id") REFERENCES "public"."inventory_items"("id");



ALTER TABLE ONLY "public"."inventory_transactions"
    ADD CONSTRAINT "inventory_transactions_performed_by_fkey" FOREIGN KEY ("performed_by") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."lab_results"
    ADD CONSTRAINT "lab_results_patient_id_fkey" FOREIGN KEY ("patient_id") REFERENCES "public"."patients"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."lab_results"
    ADD CONSTRAINT "lab_results_provider_id_fkey" FOREIGN KEY ("provider_id") REFERENCES "public"."healthcare_providers"("id");



ALTER TABLE ONLY "public"."medical_records"
    ADD CONSTRAINT "medical_records_department_id_fkey" FOREIGN KEY ("department_id") REFERENCES "public"."departments"("id");



ALTER TABLE ONLY "public"."medical_records"
    ADD CONSTRAINT "medical_records_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id");



ALTER TABLE ONLY "public"."medical_records"
    ADD CONSTRAINT "medical_records_patient_id_fkey" FOREIGN KEY ("patient_id") REFERENCES "public"."patients"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."medical_records"
    ADD CONSTRAINT "medical_records_provider_id_fkey" FOREIGN KEY ("provider_id") REFERENCES "public"."healthcare_providers"("id");



ALTER TABLE ONLY "public"."medications"
    ADD CONSTRAINT "medications_patient_id_fkey" FOREIGN KEY ("patient_id") REFERENCES "public"."patients"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."medications"
    ADD CONSTRAINT "medications_provider_id_fkey" FOREIGN KEY ("provider_id") REFERENCES "public"."healthcare_providers"("id");



ALTER TABLE ONLY "public"."message_states"
    ADD CONSTRAINT "message_states_message_id_fkey" FOREIGN KEY ("message_id") REFERENCES "public"."messages"("id");



ALTER TABLE ONLY "public"."message_states"
    ADD CONSTRAINT "message_states_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."messages"
    ADD CONSTRAINT "messages_conversation_id_fkey" FOREIGN KEY ("conversation_id") REFERENCES "public"."conversations"("id");



ALTER TABLE ONLY "public"."messages"
    ADD CONSTRAINT "messages_sender_id_fkey" FOREIGN KEY ("sender_id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."notification_preferences"
    ADD CONSTRAINT "notification_preferences_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."notification_templates"
    ADD CONSTRAINT "notification_templates_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id");



ALTER TABLE ONLY "public"."notifications"
    ADD CONSTRAINT "notifications_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id");



ALTER TABLE ONLY "public"."notifications"
    ADD CONSTRAINT "notifications_recipient_id_fkey" FOREIGN KEY ("recipient_id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."notifications"
    ADD CONSTRAINT "notifications_sender_id_fkey" FOREIGN KEY ("sender_id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."orders"
    ADD CONSTRAINT "orders_ordering_provider_id_fkey" FOREIGN KEY ("ordering_provider_id") REFERENCES "public"."healthcare_providers"("id");



ALTER TABLE ONLY "public"."orders"
    ADD CONSTRAINT "orders_patient_id_fkey" FOREIGN KEY ("patient_id") REFERENCES "public"."patients"("id");



ALTER TABLE ONLY "public"."organization_invites"
    ADD CONSTRAINT "organization_invites_invited_by_fkey" FOREIGN KEY ("invited_by") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."organization_invites"
    ADD CONSTRAINT "organization_invites_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."organizations"
    ADD CONSTRAINT "organizations_owner_id_fkey" FOREIGN KEY ("owner_id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."patient_alerts"
    ADD CONSTRAINT "patient_alerts_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."patient_alerts"
    ADD CONSTRAINT "patient_alerts_patient_id_fkey" FOREIGN KEY ("patient_id") REFERENCES "public"."patients"("id");



ALTER TABLE ONLY "public"."patient_education_records"
    ADD CONSTRAINT "patient_education_records_material_id_fkey" FOREIGN KEY ("material_id") REFERENCES "public"."education_materials"("id");



ALTER TABLE ONLY "public"."patient_education_records"
    ADD CONSTRAINT "patient_education_records_patient_id_fkey" FOREIGN KEY ("patient_id") REFERENCES "public"."patients"("id");



ALTER TABLE ONLY "public"."patient_education_records"
    ADD CONSTRAINT "patient_education_records_provider_id_fkey" FOREIGN KEY ("provider_id") REFERENCES "public"."healthcare_providers"("id");



ALTER TABLE ONLY "public"."patient_portal_settings"
    ADD CONSTRAINT "patient_portal_settings_patient_id_fkey" FOREIGN KEY ("patient_id") REFERENCES "public"."patients"("id");



ALTER TABLE ONLY "public"."patient_questionnaires"
    ADD CONSTRAINT "patient_questionnaires_patient_id_fkey" FOREIGN KEY ("patient_id") REFERENCES "public"."patients"("id");



ALTER TABLE ONLY "public"."patients"
    ADD CONSTRAINT "patients_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id");



ALTER TABLE ONLY "public"."patients"
    ADD CONSTRAINT "patients_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."referrals"
    ADD CONSTRAINT "referrals_patient_id_fkey" FOREIGN KEY ("patient_id") REFERENCES "public"."patients"("id");



ALTER TABLE ONLY "public"."referrals"
    ADD CONSTRAINT "referrals_referred_to_provider_id_fkey" FOREIGN KEY ("referred_to_provider_id") REFERENCES "public"."healthcare_providers"("id");



ALTER TABLE ONLY "public"."referrals"
    ADD CONSTRAINT "referrals_referring_provider_id_fkey" FOREIGN KEY ("referring_provider_id") REFERENCES "public"."healthcare_providers"("id");



ALTER TABLE ONLY "public"."task_comments"
    ADD CONSTRAINT "task_comments_task_id_fkey" FOREIGN KEY ("task_id") REFERENCES "public"."tasks"("id");



ALTER TABLE ONLY "public"."task_comments"
    ADD CONSTRAINT "task_comments_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."task_watchers"
    ADD CONSTRAINT "task_watchers_task_id_fkey" FOREIGN KEY ("task_id") REFERENCES "public"."tasks"("id");



ALTER TABLE ONLY "public"."task_watchers"
    ADD CONSTRAINT "task_watchers_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."tasks"
    ADD CONSTRAINT "tasks_assigned_by_fkey" FOREIGN KEY ("assigned_by") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."tasks"
    ADD CONSTRAINT "tasks_assigned_to_fkey" FOREIGN KEY ("assigned_to") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."tasks"
    ADD CONSTRAINT "tasks_department_id_fkey" FOREIGN KEY ("department_id") REFERENCES "public"."departments"("id");



ALTER TABLE ONLY "public"."tasks"
    ADD CONSTRAINT "tasks_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id");



ALTER TABLE ONLY "public"."teams"
    ADD CONSTRAINT "teams_department_id_fkey" FOREIGN KEY ("department_id") REFERENCES "public"."departments"("id");



ALTER TABLE ONLY "public"."teams"
    ADD CONSTRAINT "teams_leader_id_fkey" FOREIGN KEY ("leader_id") REFERENCES "public"."healthcare_providers"("id");



ALTER TABLE ONLY "public"."templates"
    ADD CONSTRAINT "templates_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id");



ALTER TABLE ONLY "public"."user_roles"
    ADD CONSTRAINT "user_roles_department_id_fkey" FOREIGN KEY ("department_id") REFERENCES "public"."departments"("id");



ALTER TABLE ONLY "public"."user_roles"
    ADD CONSTRAINT "user_roles_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id");



ALTER TABLE ONLY "public"."vital_signs"
    ADD CONSTRAINT "vital_signs_patient_id_fkey" FOREIGN KEY ("patient_id") REFERENCES "public"."patients"("id");



ALTER TABLE ONLY "public"."vital_signs"
    ADD CONSTRAINT "vital_signs_recorded_by_fkey" FOREIGN KEY ("recorded_by") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."workflow_instances"
    ADD CONSTRAINT "workflow_instances_workflow_id_fkey" FOREIGN KEY ("workflow_id") REFERENCES "public"."workflows"("id");



ALTER TABLE ONLY "public"."workflow_logs"
    ADD CONSTRAINT "workflow_logs_workflow_instance_id_fkey" FOREIGN KEY ("workflow_instance_id") REFERENCES "public"."workflow_instances"("id");



ALTER TABLE ONLY "public"."workflows"
    ADD CONSTRAINT "workflows_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."workflows"
    ADD CONSTRAINT "workflows_organization_id_fkey" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id");



CREATE POLICY "Access control for allergies" ON "public"."allergies" TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM ("public"."user_roles" "ur"
     JOIN "public"."patients" "p" ON (("p"."organization_id" = "ur"."organization_id")))
  WHERE (("ur"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND ("allergies"."patient_id" = "p"."id"))))) WITH CHECK ((EXISTS ( SELECT 1
   FROM ("public"."user_roles" "ur"
     JOIN "public"."patients" "p" ON (("p"."organization_id" = "ur"."organization_id")))
  WHERE (("ur"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND ("allergies"."patient_id" = "p"."id")))));



CREATE POLICY "Activity logs access policy" ON "public"."activity_logs" USING (((EXISTS ( SELECT 1
   FROM "public"."user_roles"
  WHERE (("user_roles"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND ("user_roles"."role" = 'system_admin'::"public"."user_role")))) OR ("organization_id" IN ( SELECT "user_roles"."organization_id"
   FROM "public"."user_roles"
  WHERE ("user_roles"."user_id" = ( SELECT "auth"."uid"() AS "uid")))))) WITH CHECK (((EXISTS ( SELECT 1
   FROM "public"."user_roles"
  WHERE (("user_roles"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND ("user_roles"."role" = 'system_admin'::"public"."user_role")))) OR ("organization_id" IN ( SELECT "user_roles"."organization_id"
   FROM "public"."user_roles"
  WHERE ("user_roles"."user_id" = ( SELECT "auth"."uid"() AS "uid"))))));



CREATE POLICY "Admins can create invites" ON "public"."organization_invites" FOR INSERT WITH CHECK ((EXISTS ( SELECT 1
   FROM "public"."user_roles" "ur"
  WHERE (("ur"."organization_id" = "ur"."organization_id") AND ("ur"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND ("ur"."role" = ANY (ARRAY['system_admin'::"public"."user_role", 'org_admin'::"public"."user_role"]))))));



CREATE POLICY "Audit logs access policy" ON "public"."audit_logs" USING (((EXISTS ( SELECT 1
   FROM "public"."user_roles"
  WHERE (("user_roles"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND ("user_roles"."role" = 'system_admin'::"public"."user_role")))) OR (EXISTS ( SELECT 1
   FROM "public"."user_roles"
  WHERE (("user_roles"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND ("user_roles"."role" = ANY (ARRAY['org_admin'::"public"."user_role", 'clinical_admin'::"public"."user_role"]))))) OR (EXISTS ( SELECT 1
   FROM "public"."healthcare_providers"
  WHERE ("healthcare_providers"."user_id" = ( SELECT "auth"."uid"() AS "uid")))))) WITH CHECK ((EXISTS ( SELECT 1
   FROM "public"."user_roles"
  WHERE (("user_roles"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND ("user_roles"."role" = 'system_admin'::"public"."user_role")))));



CREATE POLICY "Consolidated access for conversation_participants" ON "public"."conversation_participants" USING (((EXISTS ( SELECT 1
   FROM "public"."user_roles"
  WHERE (("user_roles"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND (("user_roles"."role")::"text" = ANY (ARRAY['admin'::"text", 'staff'::"text"]))))) OR ("user_id" = ( SELECT "auth"."uid"() AS "uid")))) WITH CHECK (((EXISTS ( SELECT 1
   FROM "public"."user_roles"
  WHERE (("user_roles"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND (("user_roles"."role")::"text" = ANY (ARRAY['admin'::"text", 'staff'::"text"]))))) OR ("user_id" = ( SELECT "auth"."uid"() AS "uid"))));



CREATE POLICY "Consolidated access for immunizations" ON "public"."immunizations" USING (((EXISTS ( SELECT 1
   FROM "public"."patients"
  WHERE (("patients"."id" = "immunizations"."patient_id") AND ("patients"."user_id" = ( SELECT "auth"."uid"() AS "uid"))))) OR (EXISTS ( SELECT 1
   FROM ("public"."healthcare_providers" "hp"
     JOIN "public"."user_roles" "ur" ON (("hp"."user_id" = "ur"."user_id")))
  WHERE (("hp"."id" = "immunizations"."administered_by") AND ("ur"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND (("ur"."role")::"text" = ANY (ARRAY['provider'::"text", 'clinical_staff'::"text", 'admin'::"text", 'super_admin'::"text"]))))))) WITH CHECK ((EXISTS ( SELECT 1
   FROM ("public"."healthcare_providers" "hp"
     JOIN "public"."user_roles" "ur" ON (("hp"."user_id" = "ur"."user_id")))
  WHERE (("hp"."id" = "immunizations"."administered_by") AND ("ur"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND (("ur"."role")::"text" = ANY (ARRAY['provider'::"text", 'clinical_staff'::"text", 'admin'::"text", 'super_admin'::"text"]))))));



CREATE POLICY "Consolidated access for inventory_items" ON "public"."inventory_items" USING ((EXISTS ( SELECT 1
   FROM "public"."user_roles" "ur"
  WHERE (("ur"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND ("ur"."organization_id" = "inventory_items"."organization_id") AND (("ur"."role")::"text" = ANY (ARRAY['admin'::"text", 'staff'::"text", 'super_admin'::"text"])))))) WITH CHECK ((EXISTS ( SELECT 1
   FROM "public"."user_roles" "ur"
  WHERE (("ur"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND ("ur"."organization_id" = "inventory_items"."organization_id") AND (("ur"."role")::"text" = ANY (ARRAY['admin'::"text", 'super_admin'::"text"]))))));



CREATE POLICY "Consolidated access for inventory_transactions" ON "public"."inventory_transactions" USING ((EXISTS ( SELECT 1
   FROM ("public"."user_roles" "ur"
     JOIN "public"."inventory_items" "ii" ON (("ur"."organization_id" = "ii"."organization_id")))
  WHERE (("ur"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND ("ii"."id" = "inventory_transactions"."item_id") AND (("ur"."role")::"text" = ANY (ARRAY['admin'::"text", 'staff'::"text", 'super_admin'::"text"])))))) WITH CHECK ((EXISTS ( SELECT 1
   FROM ("public"."user_roles" "ur"
     JOIN "public"."inventory_items" "ii" ON (("ur"."organization_id" = "ii"."organization_id")))
  WHERE (("ur"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND ("ii"."id" = "inventory_transactions"."item_id") AND (("ur"."role")::"text" = ANY (ARRAY['admin'::"text", 'staff'::"text", 'super_admin'::"text"]))))));



CREATE POLICY "Consolidated access for orders" ON "public"."orders" USING (((EXISTS ( SELECT 1
   FROM "public"."healthcare_providers" "hp"
  WHERE (("hp"."id" = "orders"."ordering_provider_id") AND ("hp"."user_id" = ( SELECT "auth"."uid"() AS "uid"))))) OR (EXISTS ( SELECT 1
   FROM "public"."patients" "p"
  WHERE (("p"."id" = "orders"."patient_id") AND ("p"."user_id" = ( SELECT "auth"."uid"() AS "uid"))))) OR (EXISTS ( SELECT 1
   FROM ("public"."user_roles" "ur"
     JOIN "public"."patients" "p_org" ON (("ur"."organization_id" = "p_org"."organization_id")))
  WHERE (("ur"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND ("p_org"."id" = "orders"."patient_id") AND (("ur"."role")::"text" = ANY (ARRAY['admin'::"text", 'clinical_staff'::"text", 'staff'::"text", 'super_admin'::"text"]))))))) WITH CHECK (((EXISTS ( SELECT 1
   FROM "public"."healthcare_providers" "hp"
  WHERE (("hp"."id" = "orders"."ordering_provider_id") AND ("hp"."user_id" = ( SELECT "auth"."uid"() AS "uid"))))) OR (EXISTS ( SELECT 1
   FROM ("public"."user_roles" "ur"
     JOIN "public"."patients" "p_org" ON (("ur"."organization_id" = "p_org"."organization_id")))
  WHERE (("ur"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND ("p_org"."id" = "orders"."patient_id") AND (("ur"."role")::"text" = ANY (ARRAY['admin'::"text", 'clinical_staff'::"text", 'staff'::"text", 'super_admin'::"text"])))))));



CREATE POLICY "Consolidated access for patient_alerts" ON "public"."patient_alerts" USING ((("created_by" = ( SELECT "auth"."uid"() AS "uid")) OR (EXISTS ( SELECT 1
   FROM ("public"."user_roles" "ur"
     JOIN "public"."patients" "p" ON (("ur"."organization_id" = "p"."organization_id")))
  WHERE (("ur"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND ("p"."id" = "patient_alerts"."patient_id") AND (("ur"."role")::"text" = ANY (ARRAY['admin'::"text", 'clinical_staff'::"text", 'staff'::"text", 'super_admin'::"text"]))))))) WITH CHECK ((("created_by" = ( SELECT "auth"."uid"() AS "uid")) OR (EXISTS ( SELECT 1
   FROM ("public"."user_roles" "ur"
     JOIN "public"."patients" "p" ON (("ur"."organization_id" = "p"."organization_id")))
  WHERE (("ur"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND ("p"."id" = "patient_alerts"."patient_id") AND (("ur"."role")::"text" = ANY (ARRAY['admin'::"text", 'clinical_staff'::"text", 'staff'::"text", 'super_admin'::"text"])))))));



CREATE POLICY "Consolidated access for patient_education_records" ON "public"."patient_education_records" USING (((EXISTS ( SELECT 1
   FROM "public"."patients"
  WHERE (("patients"."id" = "patient_education_records"."patient_id") AND ("patients"."user_id" = ( SELECT "auth"."uid"() AS "uid"))))) OR (EXISTS ( SELECT 1
   FROM "public"."healthcare_providers" "hp"
  WHERE (("hp"."id" = "patient_education_records"."provider_id") AND ("hp"."user_id" = ( SELECT "auth"."uid"() AS "uid"))))) OR (EXISTS ( SELECT 1
   FROM ("public"."user_roles" "ur"
     JOIN "public"."patients" "p" ON (("ur"."organization_id" = "p"."organization_id")))
  WHERE (("ur"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND ("p"."id" = "patient_education_records"."patient_id") AND (("ur"."role")::"text" = ANY (ARRAY['admin'::"text", 'clinical_staff'::"text", 'staff'::"text", 'super_admin'::"text"]))))))) WITH CHECK (((EXISTS ( SELECT 1
   FROM "public"."healthcare_providers" "hp"
  WHERE (("hp"."id" = "patient_education_records"."provider_id") AND ("hp"."user_id" = ( SELECT "auth"."uid"() AS "uid"))))) OR (EXISTS ( SELECT 1
   FROM ("public"."user_roles" "ur"
     JOIN "public"."patients" "p" ON (("ur"."organization_id" = "p"."organization_id")))
  WHERE (("ur"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND ("p"."id" = "patient_education_records"."patient_id") AND (("ur"."role")::"text" = ANY (ARRAY['admin'::"text", 'clinical_staff'::"text", 'staff'::"text", 'super_admin'::"text"])))))));



CREATE POLICY "Consolidated access for patient_portal_settings" ON "public"."patient_portal_settings" USING (((EXISTS ( SELECT 1
   FROM "public"."patients"
  WHERE (("patients"."id" = "patient_portal_settings"."patient_id") AND ("patients"."user_id" = ( SELECT "auth"."uid"() AS "uid"))))) OR (EXISTS ( SELECT 1
   FROM ("public"."user_roles" "ur"
     JOIN "public"."patients" "p" ON (("ur"."organization_id" = "p"."organization_id")))
  WHERE (("ur"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND ("p"."id" = "patient_portal_settings"."patient_id") AND (("ur"."role")::"text" = ANY (ARRAY['admin'::"text", 'super_admin'::"text"]))))))) WITH CHECK (((EXISTS ( SELECT 1
   FROM "public"."patients"
  WHERE (("patients"."id" = "patient_portal_settings"."patient_id") AND ("patients"."user_id" = ( SELECT "auth"."uid"() AS "uid"))))) OR (EXISTS ( SELECT 1
   FROM ("public"."user_roles" "ur"
     JOIN "public"."patients" "p" ON (("ur"."organization_id" = "p"."organization_id")))
  WHERE (("ur"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND ("p"."id" = "patient_portal_settings"."patient_id") AND (("ur"."role")::"text" = ANY (ARRAY['admin'::"text", 'super_admin'::"text"])))))));



CREATE POLICY "Consolidated access for patient_questionnaires" ON "public"."patient_questionnaires" USING (((EXISTS ( SELECT 1
   FROM "public"."patients"
  WHERE (("patients"."id" = "patient_questionnaires"."patient_id") AND ("patients"."user_id" = ( SELECT "auth"."uid"() AS "uid"))))) OR (EXISTS ( SELECT 1
   FROM ("public"."healthcare_providers" "hp"
     JOIN "public"."care_team_members" "ctm" ON (("hp"."id" = "ctm"."provider_id")))
  WHERE (("ctm"."patient_id" = "patient_questionnaires"."patient_id") AND ("hp"."user_id" = ( SELECT "auth"."uid"() AS "uid"))))) OR (EXISTS ( SELECT 1
   FROM ("public"."user_roles" "ur"
     JOIN "public"."patients" "p" ON (("ur"."organization_id" = "p"."organization_id")))
  WHERE (("ur"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND ("p"."id" = "patient_questionnaires"."patient_id") AND (("ur"."role")::"text" = ANY (ARRAY['admin'::"text", 'clinical_staff'::"text", 'staff'::"text", 'super_admin'::"text"]))))))) WITH CHECK (((EXISTS ( SELECT 1
   FROM "public"."patients"
  WHERE (("patients"."id" = "patient_questionnaires"."patient_id") AND ("patients"."user_id" = ( SELECT "auth"."uid"() AS "uid"))))) OR (EXISTS ( SELECT 1
   FROM ("public"."healthcare_providers" "hp"
     JOIN "public"."care_team_members" "ctm" ON (("hp"."id" = "ctm"."provider_id")))
  WHERE (("ctm"."patient_id" = "patient_questionnaires"."patient_id") AND ("hp"."user_id" = ( SELECT "auth"."uid"() AS "uid"))))) OR (EXISTS ( SELECT 1
   FROM ("public"."user_roles" "ur"
     JOIN "public"."patients" "p" ON (("ur"."organization_id" = "p"."organization_id")))
  WHERE (("ur"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND ("p"."id" = "patient_questionnaires"."patient_id") AND (("ur"."role")::"text" = ANY (ARRAY['admin'::"text", 'clinical_staff'::"text", 'staff'::"text", 'super_admin'::"text"])))))));



CREATE POLICY "Consolidated access for teams" ON "public"."teams" USING ((EXISTS ( SELECT 1
   FROM (("public"."user_roles" "ur"
     JOIN "public"."locations" "f" ON (("ur"."organization_id" = "f"."organization_id")))
     JOIN "public"."departments" "d" ON (("f"."id" = "d"."location_id")))
  WHERE (("ur"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND ("d"."id" = "teams"."department_id") AND (("ur"."role")::"text" = ANY (ARRAY['admin'::"text", 'staff'::"text", 'super_admin'::"text"])))))) WITH CHECK ((EXISTS ( SELECT 1
   FROM (("public"."user_roles" "ur"
     JOIN "public"."locations" "f" ON (("ur"."organization_id" = "f"."organization_id")))
     JOIN "public"."departments" "d" ON (("f"."id" = "d"."location_id")))
  WHERE (("ur"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND ("d"."id" = "teams"."department_id") AND (("ur"."role")::"text" = ANY (ARRAY['admin'::"text", 'super_admin'::"text"]))))));



CREATE POLICY "Consolidated access for templates" ON "public"."templates" USING ((EXISTS ( SELECT 1
   FROM "public"."user_roles" "ur"
  WHERE (("ur"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND ("ur"."organization_id" = "templates"."organization_id") AND (("ur"."role")::"text" = ANY (ARRAY['admin'::"text", 'staff'::"text", 'super_admin'::"text"])))))) WITH CHECK ((EXISTS ( SELECT 1
   FROM "public"."user_roles" "ur"
  WHERE (("ur"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND ("ur"."organization_id" = "templates"."organization_id") AND (("ur"."role")::"text" = ANY (ARRAY['admin'::"text", 'staff'::"text", 'super_admin'::"text"]))))));



CREATE POLICY "Consolidated select access for education_materials" ON "public"."education_materials" FOR SELECT USING (true);



CREATE POLICY "Consolidated view access for insurance_providers" ON "public"."insurance_providers" FOR SELECT USING ((EXISTS ( SELECT 1
   FROM "public"."user_roles"
  WHERE (("user_roles"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND (("user_roles"."role")::"text" = ANY (ARRAY['admin'::"text", 'staff'::"text", 'billing'::"text", 'clinical_staff'::"text", 'provider'::"text", 'super_admin'::"text"]))))));



CREATE POLICY "Consolidated view for role_permissions" ON "public"."role_permissions" FOR SELECT USING (true);



CREATE POLICY "Create appointments" ON "public"."appointments" FOR INSERT TO "authenticated" WITH CHECK (((EXISTS ( SELECT 1
   FROM "public"."patients" "p"
  WHERE (("p"."id" = "appointments"."patient_id") AND ("p"."user_id" = ( SELECT "auth"."uid"() AS "uid"))))) OR (EXISTS ( SELECT 1
   FROM "public"."healthcare_providers" "hp"
  WHERE (("hp"."id" = "appointments"."provider_id") AND ("hp"."user_id" = ( SELECT "auth"."uid"() AS "uid")))))));



CREATE POLICY "Create lab results" ON "public"."lab_results" FOR INSERT TO "authenticated" WITH CHECK ((("provider_id" IN ( SELECT "healthcare_providers"."id"
   FROM "public"."healthcare_providers"
  WHERE ("healthcare_providers"."user_id" = ( SELECT "auth"."uid"() AS "uid")))) AND "public"."is_healthcare_provider"(( SELECT "auth"."uid"() AS "uid"))));



CREATE POLICY "Create medical records" ON "public"."medical_records" FOR INSERT TO "authenticated" WITH CHECK ((("provider_id" IN ( SELECT "healthcare_providers"."id"
   FROM "public"."healthcare_providers"
  WHERE ("healthcare_providers"."user_id" = ( SELECT "auth"."uid"() AS "uid")))) AND "public"."is_healthcare_provider"(( SELECT "auth"."uid"() AS "uid"))));



CREATE POLICY "Create medications" ON "public"."medications" FOR INSERT TO "authenticated" WITH CHECK ((("provider_id" IN ( SELECT "healthcare_providers"."id"
   FROM "public"."healthcare_providers"
  WHERE ("healthcare_providers"."user_id" = ( SELECT "auth"."uid"() AS "uid")))) AND "public"."is_healthcare_provider"(( SELECT "auth"."uid"() AS "uid"))));



CREATE POLICY "Delete access for education_materials by staff" ON "public"."education_materials" FOR DELETE TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."user_roles" "ur"
  WHERE (("ur"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND (("ur"."role")::"text" = ANY (ARRAY['admin'::"text", 'clinical_staff'::"text", 'super_admin'::"text"]))))));



CREATE POLICY "Delete access for insurance_providers by admin_billing" ON "public"."insurance_providers" FOR DELETE TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."user_roles"
  WHERE (("user_roles"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND (("user_roles"."role")::"text" = ANY (ARRAY['admin'::"text", 'billing'::"text", 'super_admin'::"text"]))))));



CREATE POLICY "Delete access for role_permissions by admin" ON "public"."role_permissions" FOR DELETE TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."user_roles"
  WHERE (("user_roles"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND (("user_roles"."role")::"text" = ANY (ARRAY['admin'::"text", 'super_admin'::"text"]))))));



CREATE POLICY "Insert access for education_materials by staff" ON "public"."education_materials" FOR INSERT TO "authenticated" WITH CHECK ((EXISTS ( SELECT 1
   FROM "public"."user_roles" "ur"
  WHERE (("ur"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND (("ur"."role")::"text" = ANY (ARRAY['admin'::"text", 'clinical_staff'::"text", 'super_admin'::"text"]))))));



CREATE POLICY "Insert access for insurance_providers by admin_billing" ON "public"."insurance_providers" FOR INSERT TO "authenticated" WITH CHECK ((EXISTS ( SELECT 1
   FROM "public"."user_roles"
  WHERE (("user_roles"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND (("user_roles"."role")::"text" = ANY (ARRAY['admin'::"text", 'billing'::"text", 'super_admin'::"text"]))))));



CREATE POLICY "Insert access for role_permissions by admin" ON "public"."role_permissions" FOR INSERT TO "authenticated" WITH CHECK ((EXISTS ( SELECT 1
   FROM "public"."user_roles"
  WHERE (("user_roles"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND (("user_roles"."role")::"text" = ANY (ARRAY['admin'::"text", 'super_admin'::"text"]))))));



CREATE POLICY "Organization admins can manage notification templates" ON "public"."notification_templates" TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."user_roles"
  WHERE (("user_roles"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND ("user_roles"."organization_id" = "notification_templates"."organization_id") AND (("user_roles"."role")::"text" = ANY (ARRAY['super_admin'::"text", 'admin'::"text"])))))) WITH CHECK ((EXISTS ( SELECT 1
   FROM "public"."user_roles"
  WHERE (("user_roles"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND ("user_roles"."organization_id" = "notification_templates"."organization_id") AND (("user_roles"."role")::"text" = ANY (ARRAY['super_admin'::"text", 'admin'::"text"]))))));



CREATE POLICY "Organization admins can manage workflows" ON "public"."workflows" TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."user_roles"
  WHERE (("user_roles"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND ("user_roles"."organization_id" = "workflows"."organization_id") AND (("user_roles"."role")::"text" = ANY (ARRAY['super_admin'::"text", 'admin'::"text"])))))) WITH CHECK ((EXISTS ( SELECT 1
   FROM "public"."user_roles"
  WHERE (("user_roles"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND ("user_roles"."organization_id" = "workflows"."organization_id") AND (("user_roles"."role")::"text" = ANY (ARRAY['super_admin'::"text", 'admin'::"text"]))))));



CREATE POLICY "Patients can update their own profile" ON "public"."patients" FOR UPDATE TO "authenticated" USING ((( SELECT "auth"."uid"() AS "uid") = "user_id")) WITH CHECK ((( SELECT "auth"."uid"() AS "uid") = "user_id"));



CREATE POLICY "Providers can update their own profile" ON "public"."healthcare_providers" FOR UPDATE TO "authenticated" USING ((( SELECT "auth"."uid"() AS "uid") = "user_id")) WITH CHECK ((( SELECT "auth"."uid"() AS "uid") = "user_id"));



CREATE POLICY "Update access for education_materials by staff" ON "public"."education_materials" FOR UPDATE TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."user_roles" "ur"
  WHERE (("ur"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND (("ur"."role")::"text" = ANY (ARRAY['admin'::"text", 'clinical_staff'::"text", 'super_admin'::"text"])))))) WITH CHECK ((EXISTS ( SELECT 1
   FROM "public"."user_roles" "ur"
  WHERE (("ur"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND (("ur"."role")::"text" = ANY (ARRAY['admin'::"text", 'clinical_staff'::"text", 'super_admin'::"text"]))))));



CREATE POLICY "Update access for insurance_providers by admin_billing" ON "public"."insurance_providers" FOR UPDATE TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."user_roles"
  WHERE (("user_roles"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND (("user_roles"."role")::"text" = ANY (ARRAY['admin'::"text", 'billing'::"text", 'super_admin'::"text"])))))) WITH CHECK ((EXISTS ( SELECT 1
   FROM "public"."user_roles"
  WHERE (("user_roles"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND (("user_roles"."role")::"text" = ANY (ARRAY['admin'::"text", 'billing'::"text", 'super_admin'::"text"]))))));



CREATE POLICY "Update access for role_permissions by admin" ON "public"."role_permissions" FOR UPDATE TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."user_roles"
  WHERE (("user_roles"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND (("user_roles"."role")::"text" = ANY (ARRAY['admin'::"text", 'super_admin'::"text"])))))) WITH CHECK ((EXISTS ( SELECT 1
   FROM "public"."user_roles"
  WHERE (("user_roles"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND (("user_roles"."role")::"text" = ANY (ARRAY['admin'::"text", 'super_admin'::"text"]))))));



CREATE POLICY "Update lab results" ON "public"."lab_results" FOR UPDATE TO "authenticated" USING ((("provider_id" IN ( SELECT "healthcare_providers"."id"
   FROM "public"."healthcare_providers"
  WHERE ("healthcare_providers"."user_id" = ( SELECT "auth"."uid"() AS "uid")))) AND "public"."is_healthcare_provider"(( SELECT "auth"."uid"() AS "uid")))) WITH CHECK ((("provider_id" IN ( SELECT "healthcare_providers"."id"
   FROM "public"."healthcare_providers"
  WHERE ("healthcare_providers"."user_id" = ( SELECT "auth"."uid"() AS "uid")))) AND "public"."is_healthcare_provider"(( SELECT "auth"."uid"() AS "uid"))));



CREATE POLICY "Update medical records" ON "public"."medical_records" FOR UPDATE TO "authenticated" USING ((("provider_id" IN ( SELECT "healthcare_providers"."id"
   FROM "public"."healthcare_providers"
  WHERE ("healthcare_providers"."user_id" = ( SELECT "auth"."uid"() AS "uid")))) AND "public"."is_healthcare_provider"(( SELECT "auth"."uid"() AS "uid")))) WITH CHECK ((("provider_id" IN ( SELECT "healthcare_providers"."id"
   FROM "public"."healthcare_providers"
  WHERE ("healthcare_providers"."user_id" = ( SELECT "auth"."uid"() AS "uid")))) AND "public"."is_healthcare_provider"(( SELECT "auth"."uid"() AS "uid"))));



CREATE POLICY "Update medications" ON "public"."medications" FOR UPDATE TO "authenticated" USING ((("provider_id" IN ( SELECT "healthcare_providers"."id"
   FROM "public"."healthcare_providers"
  WHERE ("healthcare_providers"."user_id" = ( SELECT "auth"."uid"() AS "uid")))) AND "public"."is_healthcare_provider"(( SELECT "auth"."uid"() AS "uid")))) WITH CHECK ((("provider_id" IN ( SELECT "healthcare_providers"."id"
   FROM "public"."healthcare_providers"
  WHERE ("healthcare_providers"."user_id" = ( SELECT "auth"."uid"() AS "uid")))) AND "public"."is_healthcare_provider"(( SELECT "auth"."uid"() AS "uid"))));



CREATE POLICY "Users can access task watchers for their tasks" ON "public"."task_watchers" TO "authenticated" USING ((("user_id" = "auth"."uid"()) OR ("task_id" IN ( SELECT "t"."id"
   FROM ("public"."tasks" "t"
     JOIN "public"."user_roles" "ur" ON (("t"."organization_id" = "ur"."organization_id")))
  WHERE ("ur"."user_id" = "auth"."uid"()))))) WITH CHECK ((("user_id" = "auth"."uid"()) OR ("task_id" IN ( SELECT "t"."id"
   FROM ("public"."tasks" "t"
     JOIN "public"."user_roles" "ur" ON (("t"."organization_id" = "ur"."organization_id")))
  WHERE ("ur"."user_id" = "auth"."uid"())))));



CREATE POLICY "Users can add comments to tasks they have access to" ON "public"."task_comments" FOR INSERT TO "authenticated" WITH CHECK ((EXISTS ( SELECT 1
   FROM "public"."tasks"
  WHERE (("tasks"."id" = "task_comments"."task_id") AND (("tasks"."assigned_to" = ( SELECT "auth"."uid"() AS "uid")) OR ("tasks"."assigned_by" = ( SELECT "auth"."uid"() AS "uid")) OR (EXISTS ( SELECT 1
           FROM "public"."task_watchers"
          WHERE (("task_watchers"."task_id" = "tasks"."id") AND ("task_watchers"."user_id" = ( SELECT "auth"."uid"() AS "uid"))))))))));



CREATE POLICY "Users can manage their notification preferences" ON "public"."notification_preferences" TO "authenticated" USING (("user_id" = ( SELECT "auth"."uid"() AS "uid"))) WITH CHECK (("user_id" = ( SELECT "auth"."uid"() AS "uid")));



CREATE POLICY "Users can send messages to their conversations" ON "public"."messages" FOR INSERT TO "authenticated" WITH CHECK ((EXISTS ( SELECT 1
   FROM "public"."conversation_participants"
  WHERE (("conversation_participants"."conversation_id" = "messages"."conversation_id") AND ("conversation_participants"."user_id" = ( SELECT "auth"."uid"() AS "uid"))))));



CREATE POLICY "Users can update tasks they're assigned to" ON "public"."tasks" FOR UPDATE TO "authenticated" USING (("assigned_to" = ( SELECT "auth"."uid"() AS "uid"))) WITH CHECK (("assigned_to" = ( SELECT "auth"."uid"() AS "uid")));



CREATE POLICY "Users can update their message states" ON "public"."message_states" FOR UPDATE TO "authenticated" USING (("user_id" = ( SELECT "auth"."uid"() AS "uid"))) WITH CHECK (("user_id" = ( SELECT "auth"."uid"() AS "uid")));



CREATE POLICY "Users can update their own invites or admins can update any" ON "public"."organization_invites" FOR UPDATE USING ((("email" = (( SELECT "auth"."jwt"() AS "jwt") ->> 'email'::"text")) OR (EXISTS ( SELECT 1
   FROM "public"."user_roles" "ur"
  WHERE (("ur"."organization_id" = "organization_invites"."organization_id") AND ("ur"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND ("ur"."role" = ANY (ARRAY['system_admin'::"public"."user_role", 'org_admin'::"public"."user_role"])))))));



CREATE POLICY "Users can update their own notifications" ON "public"."notifications" FOR UPDATE TO "authenticated" USING (("recipient_id" = ( SELECT "auth"."uid"() AS "uid"))) WITH CHECK (("recipient_id" = ( SELECT "auth"."uid"() AS "uid")));



CREATE POLICY "Users can view analytics for their organization" ON "public"."analytics_metrics" FOR SELECT TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."user_roles"
  WHERE (("user_roles"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND ("user_roles"."organization_id" = "analytics_metrics"."organization_id") AND (("user_roles"."role")::"text" = ANY (ARRAY['super_admin'::"text", 'admin'::"text", 'staff'::"text"]))))));



CREATE POLICY "Users can view conversations they're part of" ON "public"."conversations" FOR SELECT TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."conversation_participants"
  WHERE (("conversation_participants"."conversation_id" = "conversations"."id") AND ("conversation_participants"."user_id" = ( SELECT "auth"."uid"() AS "uid"))))));



CREATE POLICY "Users can view events for their organization" ON "public"."analytics_events" FOR SELECT TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."user_roles"
  WHERE (("user_roles"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND ("user_roles"."organization_id" = "analytics_events"."organization_id") AND (("user_roles"."role")::"text" = ANY (ARRAY['super_admin'::"text", 'admin'::"text"]))))));



CREATE POLICY "Users can view messages in their conversations" ON "public"."messages" FOR SELECT TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."conversation_participants"
  WHERE (("conversation_participants"."conversation_id" = "messages"."conversation_id") AND ("conversation_participants"."user_id" = ( SELECT "auth"."uid"() AS "uid"))))));



CREATE POLICY "Users can view task comments they have access to" ON "public"."task_comments" FOR SELECT TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM "public"."tasks"
  WHERE (("tasks"."id" = "task_comments"."task_id") AND (("tasks"."assigned_to" = ( SELECT "auth"."uid"() AS "uid")) OR ("tasks"."assigned_by" = ( SELECT "auth"."uid"() AS "uid")) OR (EXISTS ( SELECT 1
           FROM "public"."task_watchers"
          WHERE (("task_watchers"."task_id" = "tasks"."id") AND ("task_watchers"."user_id" = ( SELECT "auth"."uid"() AS "uid"))))))))));



CREATE POLICY "Users can view tasks for their organization" ON "public"."tasks" FOR SELECT TO "authenticated" USING ((("assigned_to" = "auth"."uid"()) OR ("assigned_by" = "auth"."uid"()) OR ("organization_id" IN ( SELECT "user_roles"."organization_id"
   FROM "public"."user_roles"
  WHERE ("user_roles"."user_id" = "auth"."uid"())))));



CREATE POLICY "Users can view their message states" ON "public"."message_states" FOR SELECT TO "authenticated" USING (("user_id" = ( SELECT "auth"."uid"() AS "uid")));



CREATE POLICY "Users can view their own invites" ON "public"."organization_invites" FOR SELECT USING (((( SELECT "auth"."jwt"() AS "jwt") IS NOT NULL) AND (("email" = (( SELECT "auth"."jwt"() AS "jwt") ->> 'email'::"text")) OR ("invited_by" = ( SELECT "auth"."uid"() AS "uid")) OR (EXISTS ( SELECT 1
   FROM "public"."user_roles" "ur"
  WHERE (("ur"."organization_id" = "organization_invites"."organization_id") AND ("ur"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND ("ur"."role" = ANY (ARRAY['system_admin'::"public"."user_role", 'org_admin'::"public"."user_role"]))))))));



CREATE POLICY "Users can view their own notifications" ON "public"."notifications" FOR SELECT TO "authenticated" USING (("recipient_id" = ( SELECT "auth"."uid"() AS "uid")));



CREATE POLICY "Users can view workflow instances for their organization" ON "public"."workflow_instances" FOR SELECT TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM ("public"."workflows" "w"
     JOIN "public"."user_roles" "ur" ON (("ur"."organization_id" = "w"."organization_id")))
  WHERE (("w"."id" = "workflow_instances"."workflow_id") AND ("ur"."user_id" = ( SELECT "auth"."uid"() AS "uid"))))));



CREATE POLICY "Users can view workflow logs for their organization" ON "public"."workflow_logs" FOR SELECT TO "authenticated" USING ((EXISTS ( SELECT 1
   FROM (("public"."workflow_instances" "wi"
     JOIN "public"."workflows" "w" ON (("w"."id" = "wi"."workflow_id")))
     JOIN "public"."user_roles" "ur" ON (("ur"."organization_id" = "w"."organization_id")))
  WHERE (("wi"."id" = "workflow_logs"."workflow_instance_id") AND ("ur"."user_id" = ( SELECT "auth"."uid"() AS "uid"))))));



ALTER TABLE "public"."activity_logs" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."allergies" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."analytics_events" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."analytics_metrics" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."appointments" ENABLE ROW LEVEL SECURITY;


CREATE POLICY "appointments_access" ON "public"."appointments" FOR SELECT USING (
CASE
    WHEN (( SELECT "auth"."role"() AS "role") = 'patient'::"text") THEN ("patient_id" = ( SELECT "auth"."uid"() AS "uid"))
    WHEN (( SELECT "auth"."role"() AS "role") = 'provider'::"text") THEN ("provider_id" = ( SELECT "auth"."uid"() AS "uid"))
    WHEN (( SELECT "auth"."role"() AS "role") = ANY (ARRAY['admin'::"text", 'clinical_staff'::"text"])) THEN true
    ELSE false
END);



ALTER TABLE "public"."audit_logs" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."billing_codes" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."care_team_members" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."claims" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."clinical_notes" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."conversation_participants" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."conversations" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."departments" ENABLE ROW LEVEL SECURITY;


CREATE POLICY "departments_access_fixed" ON "public"."departments" TO "authenticated" USING (((EXISTS ( SELECT 1
   FROM "public"."user_roles"
  WHERE (("user_roles"."user_id" = "auth"."uid"()) AND ("user_roles"."role" = 'system_admin'::"public"."user_role")))) OR (EXISTS ( SELECT 1
   FROM "public"."user_roles"
  WHERE (("user_roles"."user_id" = "auth"."uid"()) AND ("user_roles"."department_id" = "departments"."id")))) OR (EXISTS ( SELECT 1
   FROM ("public"."user_roles" "ur"
     JOIN "public"."locations" "f" ON (("f"."id" = "departments"."location_id")))
  WHERE (("ur"."user_id" = "auth"."uid"()) AND ("ur"."organization_id" = "f"."organization_id") AND ("ur"."role" = 'org_admin'::"public"."user_role")))))) WITH CHECK (((EXISTS ( SELECT 1
   FROM "public"."user_roles"
  WHERE (("user_roles"."user_id" = "auth"."uid"()) AND ("user_roles"."role" = 'system_admin'::"public"."user_role")))) OR (EXISTS ( SELECT 1
   FROM ("public"."user_roles" "ur"
     JOIN "public"."locations" "f" ON (("f"."id" = "departments"."location_id")))
  WHERE (("ur"."user_id" = "auth"."uid"()) AND ("ur"."organization_id" = "f"."organization_id") AND ("ur"."role" = 'org_admin'::"public"."user_role"))))));



ALTER TABLE "public"."documents" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."education_materials" ENABLE ROW LEVEL SECURITY;


CREATE POLICY "locations_access_fixed" ON "public"."locations" TO "authenticated" USING (((EXISTS ( SELECT 1
   FROM "public"."user_roles"
  WHERE (("user_roles"."user_id" = "auth"."uid"()) AND ("user_roles"."role" = 'system_admin'::"public"."user_role")))) OR (EXISTS ( SELECT 1
   FROM "public"."user_roles"
  WHERE (("user_roles"."user_id" = "auth"."uid"()) AND ("user_roles"."organization_id" = "locations"."organization_id")))))) WITH CHECK (((EXISTS ( SELECT 1
   FROM "public"."user_roles"
  WHERE (("user_roles"."user_id" = "auth"."uid"()) AND ("user_roles"."role" = 'system_admin'::"public"."user_role")))) OR (EXISTS ( SELECT 1
   FROM "public"."user_roles"
  WHERE (("user_roles"."user_id" = "auth"."uid"()) AND ("user_roles"."organization_id" = "locations"."organization_id") AND ("user_roles"."role" = 'org_admin'::"public"."user_role"))))));



ALTER TABLE "public"."healthcare_providers" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."immunizations" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."insurance_providers" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."inventory_items" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."inventory_transactions" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."lab_results" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."locations" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."medical_records" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."medications" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."message_states" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."messages" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."notification_preferences" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."notification_templates" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."notifications" ENABLE ROW LEVEL SECURITY;


CREATE POLICY "optimized_appointments_update" ON "public"."appointments" FOR UPDATE USING (
CASE
    WHEN (( SELECT "auth"."role"() AS "role") = 'patient'::"text") THEN ("patient_id" = ( SELECT "auth"."uid"() AS "uid"))
    WHEN (( SELECT "auth"."role"() AS "role") = 'provider'::"text") THEN ("provider_id" = ( SELECT "auth"."uid"() AS "uid"))
    WHEN (( SELECT "auth"."role"() AS "role") = ANY (ARRAY['admin'::"text", 'clinical_staff'::"text"])) THEN true
    ELSE false
END);



CREATE POLICY "optimized_billing_codes_access" ON "public"."billing_codes" FOR SELECT USING (
CASE
    WHEN (( SELECT "auth"."role"() AS "role") = 'admin'::"text") THEN true
    WHEN (( SELECT "auth"."role"() AS "role") = ANY (ARRAY['provider'::"text", 'clinical_staff'::"text", 'billing_staff'::"text"])) THEN true
    ELSE false
END);



CREATE POLICY "optimized_billing_codes_delete" ON "public"."billing_codes" FOR DELETE USING ((( SELECT "auth"."role"() AS "role") = 'admin'::"text"));



CREATE POLICY "optimized_billing_codes_manage" ON "public"."billing_codes" FOR INSERT WITH CHECK ((( SELECT "auth"."role"() AS "role") = 'admin'::"text"));



CREATE POLICY "optimized_billing_codes_update" ON "public"."billing_codes" FOR UPDATE USING ((( SELECT "auth"."role"() AS "role") = 'admin'::"text"));



CREATE POLICY "optimized_care_team_access" ON "public"."care_team_members" FOR SELECT USING (
CASE
    WHEN (( SELECT "auth"."role"() AS "role") = 'patient'::"text") THEN ("patient_id" = ( SELECT "auth"."uid"() AS "uid"))
    WHEN (( SELECT "auth"."role"() AS "role") = 'provider'::"text") THEN ("provider_id" = ( SELECT "auth"."uid"() AS "uid"))
    WHEN (( SELECT "auth"."role"() AS "role") = 'clinical_staff'::"text") THEN true
    ELSE false
END);



CREATE POLICY "optimized_care_team_delete" ON "public"."care_team_members" FOR DELETE USING ((( SELECT "auth"."role"() AS "role") = 'clinical_staff'::"text"));



CREATE POLICY "optimized_care_team_insert" ON "public"."care_team_members" FOR INSERT WITH CHECK ((( SELECT "auth"."role"() AS "role") = 'clinical_staff'::"text"));



CREATE POLICY "optimized_care_team_update" ON "public"."care_team_members" FOR UPDATE USING ((( SELECT "auth"."role"() AS "role") = 'clinical_staff'::"text"));



CREATE POLICY "optimized_claims_access" ON "public"."claims" FOR SELECT USING ((("patient_id" IN ( SELECT "patients"."id"
   FROM "public"."patients"
  WHERE ("patients"."user_id" = ( SELECT "auth"."uid"() AS "uid")))) OR (EXISTS ( SELECT 1
   FROM "public"."user_roles"
  WHERE (("user_roles"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND ("user_roles"."role" = ANY (ARRAY['system_admin'::"public"."user_role", 'org_admin'::"public"."user_role", 'billing_staff'::"public"."user_role"])))))));



CREATE POLICY "optimized_claims_delete" ON "public"."claims" FOR DELETE USING ((EXISTS ( SELECT 1
   FROM "public"."user_roles"
  WHERE (("user_roles"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND ("user_roles"."role" = ANY (ARRAY['system_admin'::"public"."user_role", 'org_admin'::"public"."user_role", 'billing_staff'::"public"."user_role"]))))));



CREATE POLICY "optimized_claims_manage" ON "public"."claims" FOR INSERT WITH CHECK ((EXISTS ( SELECT 1
   FROM "public"."user_roles"
  WHERE (("user_roles"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND ("user_roles"."role" = ANY (ARRAY['system_admin'::"public"."user_role", 'org_admin'::"public"."user_role", 'billing_staff'::"public"."user_role"]))))));



CREATE POLICY "optimized_claims_update" ON "public"."claims" FOR UPDATE USING ((EXISTS ( SELECT 1
   FROM "public"."user_roles"
  WHERE (("user_roles"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND ("user_roles"."role" = ANY (ARRAY['system_admin'::"public"."user_role", 'org_admin'::"public"."user_role", 'billing_staff'::"public"."user_role"]))))));



CREATE POLICY "optimized_clinical_notes_access" ON "public"."clinical_notes" FOR SELECT USING ((("signed_by" IN ( SELECT "healthcare_providers"."id"
   FROM "public"."healthcare_providers"
  WHERE ("healthcare_providers"."user_id" = ( SELECT "auth"."uid"() AS "uid")))) OR (EXISTS ( SELECT 1
   FROM "public"."medical_records" "mr"
  WHERE (("mr"."id" = "clinical_notes"."medical_record_id") AND (("mr"."patient_id" IN ( SELECT "patients"."id"
           FROM "public"."patients"
          WHERE ("patients"."user_id" = ( SELECT "auth"."uid"() AS "uid")))) OR ("mr"."provider_id" IN ( SELECT "healthcare_providers"."id"
           FROM "public"."healthcare_providers"
          WHERE ("healthcare_providers"."user_id" = ( SELECT "auth"."uid"() AS "uid")))) OR (EXISTS ( SELECT 1
           FROM "public"."user_roles"
          WHERE (("user_roles"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND ("user_roles"."role" = ANY (ARRAY['system_admin'::"public"."user_role", 'org_admin'::"public"."user_role", 'clinical_admin'::"public"."user_role"])))))))))));



CREATE POLICY "optimized_clinical_notes_delete" ON "public"."clinical_notes" FOR DELETE USING (("signed_by" IN ( SELECT "healthcare_providers"."id"
   FROM "public"."healthcare_providers"
  WHERE ("healthcare_providers"."user_id" = ( SELECT "auth"."uid"() AS "uid")))));



CREATE POLICY "optimized_clinical_notes_insert" ON "public"."clinical_notes" FOR INSERT WITH CHECK (("signed_by" IN ( SELECT "healthcare_providers"."id"
   FROM "public"."healthcare_providers"
  WHERE ("healthcare_providers"."user_id" = ( SELECT "auth"."uid"() AS "uid")))));



CREATE POLICY "optimized_clinical_notes_update" ON "public"."clinical_notes" FOR UPDATE USING (("signed_by" IN ( SELECT "healthcare_providers"."id"
   FROM "public"."healthcare_providers"
  WHERE ("healthcare_providers"."user_id" = ( SELECT "auth"."uid"() AS "uid")))));



CREATE POLICY "optimized_documents_access" ON "public"."documents" FOR SELECT USING ((("patient_id" IN ( SELECT "patients"."id"
   FROM "public"."patients"
  WHERE ("patients"."user_id" = ( SELECT "auth"."uid"() AS "uid")))) OR (EXISTS ( SELECT 1
   FROM "public"."user_roles"
  WHERE (("user_roles"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND ("user_roles"."organization_id" = "documents"."organization_id") AND ("user_roles"."role" <> 'patient'::"public"."user_role"))))));



CREATE POLICY "optimized_documents_delete" ON "public"."documents" FOR DELETE USING ((("created_by" IN ( SELECT "healthcare_providers"."id"
   FROM "public"."healthcare_providers"
  WHERE ("healthcare_providers"."user_id" = ( SELECT "auth"."uid"() AS "uid")))) OR (EXISTS ( SELECT 1
   FROM "public"."user_roles"
  WHERE (("user_roles"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND ("user_roles"."organization_id" = "documents"."organization_id") AND ("user_roles"."role" = ANY (ARRAY['system_admin'::"public"."user_role", 'org_admin'::"public"."user_role", 'clinical_admin'::"public"."user_role"])))))));



CREATE POLICY "optimized_documents_insert" ON "public"."documents" FOR INSERT WITH CHECK ((("created_by" IN ( SELECT "healthcare_providers"."id"
   FROM "public"."healthcare_providers"
  WHERE ("healthcare_providers"."user_id" = ( SELECT "auth"."uid"() AS "uid")))) OR (EXISTS ( SELECT 1
   FROM "public"."user_roles"
  WHERE (("user_roles"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND ("user_roles"."organization_id" = "documents"."organization_id") AND ("user_roles"."role" = ANY (ARRAY['system_admin'::"public"."user_role", 'org_admin'::"public"."user_role", 'clinical_admin'::"public"."user_role"])))))));



CREATE POLICY "optimized_documents_update" ON "public"."documents" FOR UPDATE USING ((("created_by" IN ( SELECT "healthcare_providers"."id"
   FROM "public"."healthcare_providers"
  WHERE ("healthcare_providers"."user_id" = ( SELECT "auth"."uid"() AS "uid")))) OR (EXISTS ( SELECT 1
   FROM "public"."user_roles"
  WHERE (("user_roles"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND ("user_roles"."organization_id" = "documents"."organization_id") AND ("user_roles"."role" = ANY (ARRAY['system_admin'::"public"."user_role", 'org_admin'::"public"."user_role", 'clinical_admin'::"public"."user_role"])))))));



CREATE POLICY "optimized_healthcare_providers_access" ON "public"."healthcare_providers" FOR SELECT USING ((("user_id" = ( SELECT "auth"."uid"() AS "uid")) OR (EXISTS ( SELECT 1
   FROM "public"."user_roles"
  WHERE (("user_roles"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND ("user_roles"."organization_id" = "healthcare_providers"."organization_id") AND ("user_roles"."role" = ANY (ARRAY['system_admin'::"public"."user_role", 'org_admin'::"public"."user_role", 'clinical_admin'::"public"."user_role"])))))));



CREATE POLICY "optimized_lab_results_access" ON "public"."lab_results" FOR SELECT USING ((("patient_id" IN ( SELECT "patients"."id"
   FROM "public"."patients"
  WHERE ("patients"."user_id" = ( SELECT "auth"."uid"() AS "uid")))) OR ("provider_id" IN ( SELECT "healthcare_providers"."id"
   FROM "public"."healthcare_providers"
  WHERE ("healthcare_providers"."user_id" = ( SELECT "auth"."uid"() AS "uid")))) OR (EXISTS ( SELECT 1
   FROM ("public"."healthcare_providers" "hp"
     JOIN "public"."care_team_members" "ctm" ON (("hp"."id" = "ctm"."provider_id")))
  WHERE (("hp"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND ("ctm"."patient_id" = "lab_results"."patient_id"))))));



CREATE POLICY "optimized_medical_records_access" ON "public"."medical_records" FOR SELECT USING ((("patient_id" IN ( SELECT "patients"."id"
   FROM "public"."patients"
  WHERE ("patients"."user_id" = ( SELECT "auth"."uid"() AS "uid")))) OR ("provider_id" IN ( SELECT "healthcare_providers"."id"
   FROM "public"."healthcare_providers"
  WHERE ("healthcare_providers"."user_id" = ( SELECT "auth"."uid"() AS "uid")))) OR (EXISTS ( SELECT 1
   FROM "public"."user_roles"
  WHERE (("user_roles"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND ("user_roles"."role" = ANY (ARRAY['system_admin'::"public"."user_role", 'org_admin'::"public"."user_role", 'clinical_admin'::"public"."user_role"])))))));



CREATE POLICY "optimized_medications_access" ON "public"."medications" FOR SELECT USING ((("patient_id" IN ( SELECT "patients"."id"
   FROM "public"."patients"
  WHERE ("patients"."user_id" = ( SELECT "auth"."uid"() AS "uid")))) OR ("provider_id" IN ( SELECT "healthcare_providers"."id"
   FROM "public"."healthcare_providers"
  WHERE ("healthcare_providers"."user_id" = ( SELECT "auth"."uid"() AS "uid")))) OR (EXISTS ( SELECT 1
   FROM ("public"."healthcare_providers" "hp"
     JOIN "public"."care_team_members" "ctm" ON (("hp"."id" = "ctm"."provider_id")))
  WHERE (("hp"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND ("ctm"."patient_id" = "medications"."patient_id"))))));



CREATE POLICY "optimized_patients_access" ON "public"."patients" FOR SELECT USING ((("user_id" = ( SELECT "auth"."uid"() AS "uid")) OR (EXISTS ( SELECT 1
   FROM ("public"."healthcare_providers" "hp"
     JOIN "public"."care_team_members" "ctm" ON (("hp"."id" = "ctm"."provider_id")))
  WHERE (("hp"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND ("ctm"."patient_id" = "patients"."id")))) OR (EXISTS ( SELECT 1
   FROM ("public"."appointments" "a"
     JOIN "public"."healthcare_providers" "hp" ON (("a"."provider_id" = "hp"."id")))
  WHERE (("hp"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND ("a"."patient_id" = "patients"."id")))) OR (EXISTS ( SELECT 1
   FROM "public"."user_roles"
  WHERE (("user_roles"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND (("user_roles"."role")::"text" = 'system_admin'::"text"))))));



CREATE POLICY "optimized_referrals_access" ON "public"."referrals" FOR SELECT USING ((("patient_id" IN ( SELECT "patients"."id"
   FROM "public"."patients"
  WHERE ("patients"."user_id" = ( SELECT "auth"."uid"() AS "uid")))) OR ("referring_provider_id" IN ( SELECT "healthcare_providers"."id"
   FROM "public"."healthcare_providers"
  WHERE ("healthcare_providers"."user_id" = ( SELECT "auth"."uid"() AS "uid")))) OR ("referred_to_provider_id" IN ( SELECT "healthcare_providers"."id"
   FROM "public"."healthcare_providers"
  WHERE ("healthcare_providers"."user_id" = ( SELECT "auth"."uid"() AS "uid"))))));



CREATE POLICY "optimized_referrals_delete" ON "public"."referrals" FOR DELETE USING (("referring_provider_id" IN ( SELECT "healthcare_providers"."id"
   FROM "public"."healthcare_providers"
  WHERE ("healthcare_providers"."user_id" = ( SELECT "auth"."uid"() AS "uid")))));



CREATE POLICY "optimized_referrals_manage" ON "public"."referrals" FOR INSERT WITH CHECK (("referring_provider_id" IN ( SELECT "healthcare_providers"."id"
   FROM "public"."healthcare_providers"
  WHERE ("healthcare_providers"."user_id" = ( SELECT "auth"."uid"() AS "uid")))));



CREATE POLICY "optimized_referrals_update" ON "public"."referrals" FOR UPDATE USING ((("referring_provider_id" IN ( SELECT "healthcare_providers"."id"
   FROM "public"."healthcare_providers"
  WHERE ("healthcare_providers"."user_id" = ( SELECT "auth"."uid"() AS "uid")))) OR ("referred_to_provider_id" IN ( SELECT "healthcare_providers"."id"
   FROM "public"."healthcare_providers"
  WHERE ("healthcare_providers"."user_id" = ( SELECT "auth"."uid"() AS "uid"))))));



CREATE POLICY "optimized_vital_signs_insert" ON "public"."vital_signs" FOR INSERT WITH CHECK ((EXISTS ( SELECT 1
   FROM "public"."user_roles"
  WHERE (("user_roles"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND ("user_roles"."role" = ANY (ARRAY['system_admin'::"public"."user_role", 'org_admin'::"public"."user_role", 'clinical_admin'::"public"."user_role", 'physician'::"public"."user_role", 'nurse_practitioner'::"public"."user_role", 'registered_nurse'::"public"."user_role", 'medical_assistant'::"public"."user_role"]))))));



CREATE POLICY "optimized_vital_signs_select_access" ON "public"."vital_signs" FOR SELECT USING ((("patient_id" IN ( SELECT "patients"."id"
   FROM "public"."patients"
  WHERE ("patients"."user_id" = ( SELECT "auth"."uid"() AS "uid")))) OR (EXISTS ( SELECT 1
   FROM ("public"."healthcare_providers" "hp"
     JOIN "public"."care_team_members" "ctm" ON (("hp"."id" = "ctm"."provider_id")))
  WHERE (("hp"."user_id" = ( SELECT "auth"."uid"() AS "uid")) AND ("ctm"."patient_id" = "vital_signs"."patient_id"))))));



CREATE POLICY "optimized_vital_signs_update" ON "public"."vital_signs" FOR UPDATE USING (("recorded_by" IN ( SELECT "healthcare_providers"."id"
   FROM "public"."healthcare_providers"
  WHERE ("healthcare_providers"."user_id" = ( SELECT "auth"."uid"() AS "uid")))));



ALTER TABLE "public"."orders" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."organization_invites" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."organizations" ENABLE ROW LEVEL SECURITY;


CREATE POLICY "organizations_policy" ON "public"."organizations" TO "authenticated" USING (true) WITH CHECK (true);



ALTER TABLE "public"."patient_alerts" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."patient_education_records" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."patient_portal_settings" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."patient_questionnaires" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."patients" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."referrals" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."role_permissions" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."task_comments" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."task_watchers" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."tasks" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."teams" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."templates" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."user_roles" ENABLE ROW LEVEL SECURITY;


CREATE POLICY "user_roles_policy" ON "public"."user_roles" TO "authenticated" USING (true) WITH CHECK (true);



ALTER TABLE "public"."vital_signs" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."workflow_instances" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."workflow_logs" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."workflows" ENABLE ROW LEVEL SECURITY;




ALTER PUBLICATION "supabase_realtime" OWNER TO "postgres";





GRANT USAGE ON SCHEMA "public" TO "postgres";
GRANT USAGE ON SCHEMA "public" TO "anon";
GRANT USAGE ON SCHEMA "public" TO "authenticated";
GRANT USAGE ON SCHEMA "public" TO "service_role";

















































































































































































GRANT ALL ON FUNCTION "public"."accept_organization_invite"("invite_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."accept_organization_invite"("invite_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."accept_organization_invite"("invite_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."add_lab_results"("p_patient_id" "uuid", "p_provider_id" "uuid", "p_test_name" "text", "p_results" "jsonb", "p_normal_range" "jsonb", "p_notes" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."add_lab_results"("p_patient_id" "uuid", "p_provider_id" "uuid", "p_test_name" "text", "p_results" "jsonb", "p_normal_range" "jsonb", "p_notes" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."add_lab_results"("p_patient_id" "uuid", "p_provider_id" "uuid", "p_test_name" "text", "p_results" "jsonb", "p_normal_range" "jsonb", "p_notes" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."add_medical_record"("p_patient_id" "uuid", "p_provider_id" "uuid", "p_chief_complaint" "text", "p_diagnosis" "text"[], "p_treatment_plan" "text", "p_notes" "text", "p_attachments" "jsonb") TO "anon";
GRANT ALL ON FUNCTION "public"."add_medical_record"("p_patient_id" "uuid", "p_provider_id" "uuid", "p_chief_complaint" "text", "p_diagnosis" "text"[], "p_treatment_plan" "text", "p_notes" "text", "p_attachments" "jsonb") TO "authenticated";
GRANT ALL ON FUNCTION "public"."add_medical_record"("p_patient_id" "uuid", "p_provider_id" "uuid", "p_chief_complaint" "text", "p_diagnosis" "text"[], "p_treatment_plan" "text", "p_notes" "text", "p_attachments" "jsonb") TO "service_role";



GRANT ALL ON FUNCTION "public"."assign_task"("p_title" "text", "p_description" "text", "p_assigned_to" "uuid", "p_priority" "public"."task_priority", "p_due_date" timestamp with time zone, "p_related_to" "jsonb", "p_metadata" "jsonb") TO "anon";
GRANT ALL ON FUNCTION "public"."assign_task"("p_title" "text", "p_description" "text", "p_assigned_to" "uuid", "p_priority" "public"."task_priority", "p_due_date" timestamp with time zone, "p_related_to" "jsonb", "p_metadata" "jsonb") TO "authenticated";
GRANT ALL ON FUNCTION "public"."assign_task"("p_title" "text", "p_description" "text", "p_assigned_to" "uuid", "p_priority" "public"."task_priority", "p_due_date" timestamp with time zone, "p_related_to" "jsonb", "p_metadata" "jsonb") TO "service_role";



GRANT ALL ON FUNCTION "public"."audit_log_changes"() TO "anon";
GRANT ALL ON FUNCTION "public"."audit_log_changes"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."audit_log_changes"() TO "service_role";



GRANT ALL ON FUNCTION "public"."can_access_analytics"("org_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."can_access_analytics"("org_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."can_access_analytics"("org_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."check_user_permission"("p_user_id" "uuid", "p_resource" "text", "p_action" "text", "p_context" "jsonb") TO "anon";
GRANT ALL ON FUNCTION "public"."check_user_permission"("p_user_id" "uuid", "p_resource" "text", "p_action" "text", "p_context" "jsonb") TO "authenticated";
GRANT ALL ON FUNCTION "public"."check_user_permission"("p_user_id" "uuid", "p_resource" "text", "p_action" "text", "p_context" "jsonb") TO "service_role";



GRANT ALL ON FUNCTION "public"."clean_expired_invites"() TO "anon";
GRANT ALL ON FUNCTION "public"."clean_expired_invites"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."clean_expired_invites"() TO "service_role";



GRANT ALL ON FUNCTION "public"."decline_organization_invite"("invite_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."decline_organization_invite"("invite_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."decline_organization_invite"("invite_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."disable_rls"() TO "anon";
GRANT ALL ON FUNCTION "public"."disable_rls"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."disable_rls"() TO "service_role";



GRANT ALL ON FUNCTION "public"."get_appointment_statistics"("p_provider_id" "uuid", "p_start_date" "date", "p_end_date" "date") TO "anon";
GRANT ALL ON FUNCTION "public"."get_appointment_statistics"("p_provider_id" "uuid", "p_start_date" "date", "p_end_date" "date") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_appointment_statistics"("p_provider_id" "uuid", "p_start_date" "date", "p_end_date" "date") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_medication_statistics"("p_provider_id" "uuid", "p_start_date" "date") TO "anon";
GRANT ALL ON FUNCTION "public"."get_medication_statistics"("p_provider_id" "uuid", "p_start_date" "date") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_medication_statistics"("p_provider_id" "uuid", "p_start_date" "date") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_organization_metrics"("p_organization_id" "uuid", "p_start_date" timestamp with time zone, "p_end_date" timestamp with time zone) TO "anon";
GRANT ALL ON FUNCTION "public"."get_organization_metrics"("p_organization_id" "uuid", "p_start_date" timestamp with time zone, "p_end_date" timestamp with time zone) TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_organization_metrics"("p_organization_id" "uuid", "p_start_date" timestamp with time zone, "p_end_date" timestamp with time zone) TO "service_role";



GRANT ALL ON FUNCTION "public"."get_patient_demographics"("p_provider_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_patient_demographics"("p_provider_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_patient_demographics"("p_provider_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_patient_summary"("p_patient_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_patient_summary"("p_patient_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_patient_summary"("p_patient_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_patient_visit_history"("p_patient_id" "uuid", "p_start_date" "date", "p_end_date" "date") TO "anon";
GRANT ALL ON FUNCTION "public"."get_patient_visit_history"("p_patient_id" "uuid", "p_start_date" "date", "p_end_date" "date") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_patient_visit_history"("p_patient_id" "uuid", "p_start_date" "date", "p_end_date" "date") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_provider_workload"("p_start_date" "date", "p_end_date" "date") TO "anon";
GRANT ALL ON FUNCTION "public"."get_provider_workload"("p_start_date" "date", "p_end_date" "date") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_provider_workload"("p_start_date" "date", "p_end_date" "date") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_user_departments"("p_user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_user_departments"("p_user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_user_departments"("p_user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."has_patient_access"("provider_id" "uuid", "patient_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."has_patient_access"("provider_id" "uuid", "patient_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."has_patient_access"("provider_id" "uuid", "patient_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."is_healthcare_provider"("user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."is_healthcare_provider"("user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."is_healthcare_provider"("user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."is_patient"("user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."is_patient"("user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."is_patient"("user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."mark_message_read"("p_message_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."mark_message_read"("p_message_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."mark_message_read"("p_message_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."mark_notification_read"("p_notification_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."mark_notification_read"("p_notification_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."mark_notification_read"("p_notification_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."prescribe_medication"("p_patient_id" "uuid", "p_provider_id" "uuid", "p_medication_name" "text", "p_dosage" "text", "p_frequency" "text", "p_start_date" "date", "p_end_date" "date", "p_instructions" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."prescribe_medication"("p_patient_id" "uuid", "p_provider_id" "uuid", "p_medication_name" "text", "p_dosage" "text", "p_frequency" "text", "p_start_date" "date", "p_end_date" "date", "p_instructions" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."prescribe_medication"("p_patient_id" "uuid", "p_provider_id" "uuid", "p_medication_name" "text", "p_dosage" "text", "p_frequency" "text", "p_start_date" "date", "p_end_date" "date", "p_instructions" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."random_date"("start_date" "date", "end_date" "date") TO "anon";
GRANT ALL ON FUNCTION "public"."random_date"("start_date" "date", "end_date" "date") TO "authenticated";
GRANT ALL ON FUNCTION "public"."random_date"("start_date" "date", "end_date" "date") TO "service_role";



GRANT ALL ON FUNCTION "public"."random_timestamp"("start_timestamp" timestamp without time zone, "end_timestamp" timestamp without time zone) TO "anon";
GRANT ALL ON FUNCTION "public"."random_timestamp"("start_timestamp" timestamp without time zone, "end_timestamp" timestamp without time zone) TO "authenticated";
GRANT ALL ON FUNCTION "public"."random_timestamp"("start_timestamp" timestamp without time zone, "end_timestamp" timestamp without time zone) TO "service_role";



GRANT ALL ON FUNCTION "public"."record_analytics_event"("p_event_type" "text", "p_event_data" "jsonb") TO "anon";
GRANT ALL ON FUNCTION "public"."record_analytics_event"("p_event_type" "text", "p_event_data" "jsonb") TO "authenticated";
GRANT ALL ON FUNCTION "public"."record_analytics_event"("p_event_type" "text", "p_event_data" "jsonb") TO "service_role";



GRANT ALL ON FUNCTION "public"."record_analytics_event"("p_event_type" "text", "p_user_id" "uuid", "p_data" "jsonb") TO "anon";
GRANT ALL ON FUNCTION "public"."record_analytics_event"("p_event_type" "text", "p_user_id" "uuid", "p_data" "jsonb") TO "authenticated";
GRANT ALL ON FUNCTION "public"."record_analytics_event"("p_event_type" "text", "p_user_id" "uuid", "p_data" "jsonb") TO "service_role";



GRANT ALL ON FUNCTION "public"."record_analytics_metric"("p_metric_name" "text", "p_metric_value" numeric, "p_dimensions" "jsonb") TO "anon";
GRANT ALL ON FUNCTION "public"."record_analytics_metric"("p_metric_name" "text", "p_metric_value" numeric, "p_dimensions" "jsonb") TO "authenticated";
GRANT ALL ON FUNCTION "public"."record_analytics_metric"("p_metric_name" "text", "p_metric_value" numeric, "p_dimensions" "jsonb") TO "service_role";



GRANT ALL ON FUNCTION "public"."refresh_analytics_views"() TO "anon";
GRANT ALL ON FUNCTION "public"."refresh_analytics_views"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."refresh_analytics_views"() TO "service_role";



GRANT ALL ON FUNCTION "public"."refresh_materialized_views"() TO "anon";
GRANT ALL ON FUNCTION "public"."refresh_materialized_views"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."refresh_materialized_views"() TO "service_role";



GRANT ALL ON FUNCTION "public"."schedule_appointment"("p_patient_id" "uuid", "p_provider_id" "uuid", "p_appointment_date" timestamp without time zone, "p_duration_minutes" integer, "p_reason" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."schedule_appointment"("p_patient_id" "uuid", "p_provider_id" "uuid", "p_appointment_date" timestamp without time zone, "p_duration_minutes" integer, "p_reason" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."schedule_appointment"("p_patient_id" "uuid", "p_provider_id" "uuid", "p_appointment_date" timestamp without time zone, "p_duration_minutes" integer, "p_reason" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."search_medical_records"("search_query" "text", "p_patient_id" "uuid", "p_provider_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."search_medical_records"("search_query" "text", "p_patient_id" "uuid", "p_provider_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."search_medical_records"("search_query" "text", "p_patient_id" "uuid", "p_provider_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."search_patients"("search_query" "text", "p_provider_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."search_patients"("search_query" "text", "p_provider_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."search_patients"("search_query" "text", "p_provider_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."send_message"("p_conversation_id" "uuid", "p_content" "text", "p_attachments" "jsonb", "p_metadata" "jsonb") TO "anon";
GRANT ALL ON FUNCTION "public"."send_message"("p_conversation_id" "uuid", "p_content" "text", "p_attachments" "jsonb", "p_metadata" "jsonb") TO "authenticated";
GRANT ALL ON FUNCTION "public"."send_message"("p_conversation_id" "uuid", "p_content" "text", "p_attachments" "jsonb", "p_metadata" "jsonb") TO "service_role";



GRANT ALL ON FUNCTION "public"."send_notification"("p_type" "public"."notification_type", "p_recipient_id" "uuid", "p_title" "text", "p_content" "text", "p_metadata" "jsonb", "p_priority" "public"."notification_priority", "p_scheduled_for" timestamp with time zone) TO "anon";
GRANT ALL ON FUNCTION "public"."send_notification"("p_type" "public"."notification_type", "p_recipient_id" "uuid", "p_title" "text", "p_content" "text", "p_metadata" "jsonb", "p_priority" "public"."notification_priority", "p_scheduled_for" timestamp with time zone) TO "authenticated";
GRANT ALL ON FUNCTION "public"."send_notification"("p_type" "public"."notification_type", "p_recipient_id" "uuid", "p_title" "text", "p_content" "text", "p_metadata" "jsonb", "p_priority" "public"."notification_priority", "p_scheduled_for" timestamp with time zone) TO "service_role";



GRANT ALL ON FUNCTION "public"."start_conversation"("p_participants" "uuid"[], "p_type" "public"."conversation_type", "p_title" "text", "p_initial_message" "text", "p_metadata" "jsonb") TO "anon";
GRANT ALL ON FUNCTION "public"."start_conversation"("p_participants" "uuid"[], "p_type" "public"."conversation_type", "p_title" "text", "p_initial_message" "text", "p_metadata" "jsonb") TO "authenticated";
GRANT ALL ON FUNCTION "public"."start_conversation"("p_participants" "uuid"[], "p_type" "public"."conversation_type", "p_title" "text", "p_initial_message" "text", "p_metadata" "jsonb") TO "service_role";



GRANT ALL ON FUNCTION "public"."start_workflow"("p_workflow_id" "uuid", "p_context" "jsonb") TO "anon";
GRANT ALL ON FUNCTION "public"."start_workflow"("p_workflow_id" "uuid", "p_context" "jsonb") TO "authenticated";
GRANT ALL ON FUNCTION "public"."start_workflow"("p_workflow_id" "uuid", "p_context" "jsonb") TO "service_role";



GRANT ALL ON FUNCTION "public"."update_medical_record_search_vector"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_medical_record_search_vector"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_medical_record_search_vector"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_patient_search_vector"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_patient_search_vector"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_patient_search_vector"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_task_status"("p_task_id" "uuid", "p_status" "public"."task_status", "p_comment" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."update_task_status"("p_task_id" "uuid", "p_status" "public"."task_status", "p_comment" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_task_status"("p_task_id" "uuid", "p_status" "public"."task_status", "p_comment" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."update_updated_at_column"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_updated_at_column"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_updated_at_column"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_workflow_step"("p_instance_id" "uuid", "p_step_number" integer, "p_status" "public"."workflow_status", "p_result" "jsonb", "p_message" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."update_workflow_step"("p_instance_id" "uuid", "p_step_number" integer, "p_status" "public"."workflow_status", "p_result" "jsonb", "p_message" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_workflow_step"("p_instance_id" "uuid", "p_step_number" integer, "p_status" "public"."workflow_status", "p_result" "jsonb", "p_message" "text") TO "service_role";


















GRANT ALL ON TABLE "public"."activity_logs" TO "anon";
GRANT ALL ON TABLE "public"."activity_logs" TO "authenticated";
GRANT ALL ON TABLE "public"."activity_logs" TO "service_role";



GRANT ALL ON TABLE "public"."allergies" TO "anon";
GRANT ALL ON TABLE "public"."allergies" TO "authenticated";
GRANT ALL ON TABLE "public"."allergies" TO "service_role";



GRANT ALL ON TABLE "public"."appointments" TO "anon";
GRANT ALL ON TABLE "public"."appointments" TO "authenticated";
GRANT ALL ON TABLE "public"."appointments" TO "service_role";



GRANT ALL ON TABLE "public"."analytics_daily_appointments" TO "anon";
GRANT ALL ON TABLE "public"."analytics_daily_appointments" TO "authenticated";
GRANT ALL ON TABLE "public"."analytics_daily_appointments" TO "service_role";



GRANT ALL ON TABLE "public"."analytics_events" TO "anon";
GRANT ALL ON TABLE "public"."analytics_events" TO "authenticated";
GRANT ALL ON TABLE "public"."analytics_events" TO "service_role";



GRANT ALL ON TABLE "public"."analytics_metrics" TO "anon";
GRANT ALL ON TABLE "public"."analytics_metrics" TO "authenticated";
GRANT ALL ON TABLE "public"."analytics_metrics" TO "service_role";



GRANT ALL ON TABLE "public"."medical_records" TO "anon";
GRANT ALL ON TABLE "public"."medical_records" TO "authenticated";
GRANT ALL ON TABLE "public"."medical_records" TO "service_role";



GRANT ALL ON TABLE "public"."medications" TO "anon";
GRANT ALL ON TABLE "public"."medications" TO "authenticated";
GRANT ALL ON TABLE "public"."medications" TO "service_role";



GRANT ALL ON TABLE "public"."patients" TO "anon";
GRANT ALL ON TABLE "public"."patients" TO "authenticated";
GRANT ALL ON TABLE "public"."patients" TO "service_role";



GRANT ALL ON TABLE "public"."analytics_patient_metrics" TO "anon";
GRANT ALL ON TABLE "public"."analytics_patient_metrics" TO "authenticated";
GRANT ALL ON TABLE "public"."analytics_patient_metrics" TO "service_role";



GRANT ALL ON TABLE "public"."healthcare_providers" TO "anon";
GRANT ALL ON TABLE "public"."healthcare_providers" TO "authenticated";
GRANT ALL ON TABLE "public"."healthcare_providers" TO "service_role";



GRANT ALL ON TABLE "public"."analytics_provider_metrics" TO "anon";
GRANT ALL ON TABLE "public"."analytics_provider_metrics" TO "authenticated";
GRANT ALL ON TABLE "public"."analytics_provider_metrics" TO "service_role";



GRANT ALL ON TABLE "public"."audit_logs" TO "anon";
GRANT ALL ON TABLE "public"."audit_logs" TO "authenticated";
GRANT ALL ON TABLE "public"."audit_logs" TO "service_role";



GRANT ALL ON TABLE "public"."billing_codes" TO "anon";
GRANT ALL ON TABLE "public"."billing_codes" TO "authenticated";
GRANT ALL ON TABLE "public"."billing_codes" TO "service_role";



GRANT ALL ON TABLE "public"."care_team_members" TO "anon";
GRANT ALL ON TABLE "public"."care_team_members" TO "authenticated";
GRANT ALL ON TABLE "public"."care_team_members" TO "service_role";



GRANT ALL ON TABLE "public"."claims" TO "anon";
GRANT ALL ON TABLE "public"."claims" TO "authenticated";
GRANT ALL ON TABLE "public"."claims" TO "service_role";



GRANT ALL ON TABLE "public"."clinical_notes" TO "anon";
GRANT ALL ON TABLE "public"."clinical_notes" TO "authenticated";
GRANT ALL ON TABLE "public"."clinical_notes" TO "service_role";



GRANT ALL ON TABLE "public"."conversation_participants" TO "anon";
GRANT ALL ON TABLE "public"."conversation_participants" TO "authenticated";
GRANT ALL ON TABLE "public"."conversation_participants" TO "service_role";



GRANT ALL ON TABLE "public"."conversations" TO "anon";
GRANT ALL ON TABLE "public"."conversations" TO "authenticated";
GRANT ALL ON TABLE "public"."conversations" TO "service_role";



GRANT ALL ON TABLE "public"."departments" TO "anon";
GRANT ALL ON TABLE "public"."departments" TO "authenticated";
GRANT ALL ON TABLE "public"."departments" TO "service_role";



GRANT ALL ON TABLE "public"."documents" TO "anon";
GRANT ALL ON TABLE "public"."documents" TO "authenticated";
GRANT ALL ON TABLE "public"."documents" TO "service_role";



GRANT ALL ON TABLE "public"."education_materials" TO "anon";
GRANT ALL ON TABLE "public"."education_materials" TO "authenticated";
GRANT ALL ON TABLE "public"."education_materials" TO "service_role";



GRANT ALL ON TABLE "public"."immunizations" TO "anon";
GRANT ALL ON TABLE "public"."immunizations" TO "authenticated";
GRANT ALL ON TABLE "public"."immunizations" TO "service_role";



GRANT ALL ON TABLE "public"."insurance_providers" TO "anon";
GRANT ALL ON TABLE "public"."insurance_providers" TO "authenticated";
GRANT ALL ON TABLE "public"."insurance_providers" TO "service_role";



GRANT ALL ON TABLE "public"."inventory_items" TO "anon";
GRANT ALL ON TABLE "public"."inventory_items" TO "authenticated";
GRANT ALL ON TABLE "public"."inventory_items" TO "service_role";



GRANT ALL ON TABLE "public"."inventory_transactions" TO "anon";
GRANT ALL ON TABLE "public"."inventory_transactions" TO "authenticated";
GRANT ALL ON TABLE "public"."inventory_transactions" TO "service_role";



GRANT ALL ON TABLE "public"."lab_results" TO "anon";
GRANT ALL ON TABLE "public"."lab_results" TO "authenticated";
GRANT ALL ON TABLE "public"."lab_results" TO "service_role";



GRANT ALL ON TABLE "public"."locations" TO "anon";
GRANT ALL ON TABLE "public"."locations" TO "authenticated";
GRANT ALL ON TABLE "public"."locations" TO "service_role";



GRANT ALL ON TABLE "public"."message_states" TO "anon";
GRANT ALL ON TABLE "public"."message_states" TO "authenticated";
GRANT ALL ON TABLE "public"."message_states" TO "service_role";



GRANT ALL ON TABLE "public"."messages" TO "anon";
GRANT ALL ON TABLE "public"."messages" TO "authenticated";
GRANT ALL ON TABLE "public"."messages" TO "service_role";



GRANT ALL ON TABLE "public"."notification_preferences" TO "anon";
GRANT ALL ON TABLE "public"."notification_preferences" TO "authenticated";
GRANT ALL ON TABLE "public"."notification_preferences" TO "service_role";



GRANT ALL ON TABLE "public"."notification_templates" TO "anon";
GRANT ALL ON TABLE "public"."notification_templates" TO "authenticated";
GRANT ALL ON TABLE "public"."notification_templates" TO "service_role";



GRANT ALL ON TABLE "public"."notifications" TO "anon";
GRANT ALL ON TABLE "public"."notifications" TO "authenticated";
GRANT ALL ON TABLE "public"."notifications" TO "service_role";



GRANT ALL ON TABLE "public"."orders" TO "anon";
GRANT ALL ON TABLE "public"."orders" TO "authenticated";
GRANT ALL ON TABLE "public"."orders" TO "service_role";



GRANT ALL ON TABLE "public"."organization_invites" TO "anon";
GRANT ALL ON TABLE "public"."organization_invites" TO "authenticated";
GRANT ALL ON TABLE "public"."organization_invites" TO "service_role";



GRANT ALL ON TABLE "public"."organizations" TO "anon";
GRANT ALL ON TABLE "public"."organizations" TO "authenticated";
GRANT ALL ON TABLE "public"."organizations" TO "service_role";



GRANT ALL ON TABLE "public"."patient_alerts" TO "anon";
GRANT ALL ON TABLE "public"."patient_alerts" TO "authenticated";
GRANT ALL ON TABLE "public"."patient_alerts" TO "service_role";



GRANT ALL ON TABLE "public"."patient_education_records" TO "anon";
GRANT ALL ON TABLE "public"."patient_education_records" TO "authenticated";
GRANT ALL ON TABLE "public"."patient_education_records" TO "service_role";



GRANT ALL ON TABLE "public"."patient_portal_settings" TO "anon";
GRANT ALL ON TABLE "public"."patient_portal_settings" TO "authenticated";
GRANT ALL ON TABLE "public"."patient_portal_settings" TO "service_role";



GRANT ALL ON TABLE "public"."patient_questionnaires" TO "anon";
GRANT ALL ON TABLE "public"."patient_questionnaires" TO "authenticated";
GRANT ALL ON TABLE "public"."patient_questionnaires" TO "service_role";



GRANT ALL ON TABLE "public"."referrals" TO "anon";
GRANT ALL ON TABLE "public"."referrals" TO "authenticated";
GRANT ALL ON TABLE "public"."referrals" TO "service_role";



GRANT ALL ON TABLE "public"."role_definitions" TO "anon";
GRANT ALL ON TABLE "public"."role_definitions" TO "authenticated";
GRANT ALL ON TABLE "public"."role_definitions" TO "service_role";



GRANT ALL ON TABLE "public"."role_permissions" TO "anon";
GRANT ALL ON TABLE "public"."role_permissions" TO "authenticated";
GRANT ALL ON TABLE "public"."role_permissions" TO "service_role";



GRANT ALL ON TABLE "public"."secure_analytics_daily_appointments" TO "anon";
GRANT ALL ON TABLE "public"."secure_analytics_daily_appointments" TO "authenticated";
GRANT ALL ON TABLE "public"."secure_analytics_daily_appointments" TO "service_role";



GRANT ALL ON TABLE "public"."secure_analytics_patient_metrics" TO "anon";
GRANT ALL ON TABLE "public"."secure_analytics_patient_metrics" TO "authenticated";
GRANT ALL ON TABLE "public"."secure_analytics_patient_metrics" TO "service_role";



GRANT ALL ON TABLE "public"."secure_analytics_provider_metrics" TO "anon";
GRANT ALL ON TABLE "public"."secure_analytics_provider_metrics" TO "authenticated";
GRANT ALL ON TABLE "public"."secure_analytics_provider_metrics" TO "service_role";



GRANT ALL ON TABLE "public"."task_comments" TO "anon";
GRANT ALL ON TABLE "public"."task_comments" TO "authenticated";
GRANT ALL ON TABLE "public"."task_comments" TO "service_role";



GRANT ALL ON TABLE "public"."task_watchers" TO "anon";
GRANT ALL ON TABLE "public"."task_watchers" TO "authenticated";
GRANT ALL ON TABLE "public"."task_watchers" TO "service_role";



GRANT ALL ON TABLE "public"."tasks" TO "anon";
GRANT ALL ON TABLE "public"."tasks" TO "authenticated";
GRANT ALL ON TABLE "public"."tasks" TO "service_role";



GRANT ALL ON TABLE "public"."teams" TO "anon";
GRANT ALL ON TABLE "public"."teams" TO "authenticated";
GRANT ALL ON TABLE "public"."teams" TO "service_role";



GRANT ALL ON TABLE "public"."templates" TO "anon";
GRANT ALL ON TABLE "public"."templates" TO "authenticated";
GRANT ALL ON TABLE "public"."templates" TO "service_role";



GRANT ALL ON TABLE "public"."user_roles" TO "anon";
GRANT ALL ON TABLE "public"."user_roles" TO "authenticated";
GRANT ALL ON TABLE "public"."user_roles" TO "service_role";



GRANT ALL ON TABLE "public"."vital_signs" TO "anon";
GRANT ALL ON TABLE "public"."vital_signs" TO "authenticated";
GRANT ALL ON TABLE "public"."vital_signs" TO "service_role";



GRANT ALL ON TABLE "public"."workflow_instances" TO "anon";
GRANT ALL ON TABLE "public"."workflow_instances" TO "authenticated";
GRANT ALL ON TABLE "public"."workflow_instances" TO "service_role";



GRANT ALL ON TABLE "public"."workflow_logs" TO "anon";
GRANT ALL ON TABLE "public"."workflow_logs" TO "authenticated";
GRANT ALL ON TABLE "public"."workflow_logs" TO "service_role";



GRANT ALL ON TABLE "public"."workflows" TO "anon";
GRANT ALL ON TABLE "public"."workflows" TO "authenticated";
GRANT ALL ON TABLE "public"."workflows" TO "service_role";









ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "service_role";






























RESET ALL;
