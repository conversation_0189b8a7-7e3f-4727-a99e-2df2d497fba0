-- User roles seed data
BEGIN;

-- Map each seeded user to their appropriate organization and role
-- This ensures all seeded users are part of pre-existing organizations
INSERT INTO public.user_roles (
  id,
  user_id,
  organization_id,
  role,
  custom_permissions,
  created_at,
  updated_at
)
WITH org_data AS (
  SELECT id, name FROM public.organizations WHERE name IN ('Dev Hospital', 'Dev Clinic', 'Dev Practice')
)
SELECT
  uuid_generate_v4(),
  user_id,
  org_id,
  role::user_role,
  permissions,
  NOW(),
  NOW()
FROM (
  -- Admin user - make them a super admin with access to all organizations
  -- This is a special role that can manage multiple organizations
  SELECT
    '00000000-0000-0000-0000-**********01'::uuid as user_id,
    NULL as org_id, -- No specific organization for super admin
    'system_admin'::user_role as role,
    '{"all": true, "multi_org": true}'::jsonb as permissions

  UNION ALL

  -- Provider 1 - assign to Dev Hospital as org admin
  SELECT
    '11111111-1111-1111-1111-111111111111'::uuid as user_id,
    (SELECT id FROM org_data WHERE name = 'Dev Hospital') as org_id,
    'physician'::user_role as role,
    '{"all": true, "patients": {"read": true, "write": true}, "appointments": {"read": true, "write": true}, "medical_records": {"read": true, "write": true}, "orders": {"read": true, "write": true}, "prescriptions": {"read": true, "write": true}}'::jsonb as permissions

  UNION ALL

  -- Provider 2 - assign to Dev Clinic as org admin
  SELECT
    '*************-2222-2222-************'::uuid as user_id,
    (SELECT id FROM org_data WHERE name = 'Dev Clinic') as org_id,
    'physician'::user_role as role,
    '{"all": true, "patients": {"read": true, "write": true}, "appointments": {"read": true, "write": true}, "medical_records": {"read": true, "write": true}, "orders": {"read": true, "write": true}, "prescriptions": {"read": true, "write": true}}'::jsonb as permissions

  UNION ALL

  -- Nurse 1 - assign to Dev Hospital
  SELECT
    '*************-3333-3333-************'::uuid as user_id,
    (SELECT id FROM org_data WHERE name = 'Dev Hospital') as org_id,
    'registered_nurse'::user_role as role,
    '{"patients": {"read": true, "write": true}, "appointments": {"read": true, "write": true}, "medical_records": {"read": true, "write": true}, "orders": {"read": true}, "prescriptions": {"read": true}}'::jsonb as permissions

  UNION ALL

  -- Nurse 2 - assign to Dev Clinic
  SELECT
    '*************-4444-4444-************'::uuid as user_id,
    (SELECT id FROM org_data WHERE name = 'Dev Clinic') as org_id,
    'registered_nurse'::user_role as role,
    '{"patients": {"read": true, "write": true}, "appointments": {"read": true, "write": true}, "medical_records": {"read": true, "write": true}, "orders": {"read": true}, "prescriptions": {"read": true}}'::jsonb as permissions

  UNION ALL

  -- Front Desk - assign to Dev Hospital
  SELECT
    '*************-5555-5555-************'::uuid as user_id,
    (SELECT id FROM org_data WHERE name = 'Dev Hospital') as org_id,
    'front_desk'::user_role as role,
    '{"patients": {"read": true, "write": true}, "appointments": {"read": true, "write": true}, "medical_records": {"read": false, "write": false}, "orders": {"read": false}, "prescriptions": {"read": false}}'::jsonb as permissions

  UNION ALL

  -- Billing - assign to Dev Hospital
  SELECT
    '*************-6666-6666-************'::uuid as user_id,
    (SELECT id FROM org_data WHERE name = 'Dev Hospital') as org_id,
    'billing_staff'::user_role as role,
    '{"patients": {"read": true, "write": false}, "appointments": {"read": true, "write": false}, "medical_records": {"read": false, "write": false}, "orders": {"read": true}, "prescriptions": {"read": false}, "billing": {"read": true, "write": true}}'::jsonb as permissions

  -- Add the admin user to all organizations to demonstrate multi-org access
  UNION ALL

  -- Admin user - also add to Dev Hospital
  SELECT
    '00000000-0000-0000-0000-**********01'::uuid as user_id,
    (SELECT id FROM org_data WHERE name = 'Dev Hospital') as org_id,
    'system_admin'::user_role as role,
    '{"all": true}'::jsonb as permissions

  UNION ALL

  -- Admin user - also add to Dev Clinic
  SELECT
    '00000000-0000-0000-0000-**********01'::uuid as user_id,
    (SELECT id FROM org_data WHERE name = 'Dev Clinic') as org_id,
    'system_admin'::user_role as role,
    '{"all": true}'::jsonb as permissions

  UNION ALL

  -- Admin user - also add to Dev Practice
  SELECT
    '00000000-0000-0000-0000-**********01'::uuid as user_id,
    (SELECT id FROM org_data WHERE name = 'Dev Practice') as org_id,
    'system_admin'::user_role as role,
    '{"all": true}'::jsonb as permissions

) AS user_mappings(user_id, org_id, role, permissions)
WHERE NOT EXISTS (
  SELECT 1 FROM public.user_roles
  WHERE user_id = user_mappings.user_id
  AND (
    (organization_id IS NULL AND user_mappings.org_id IS NULL) OR
    organization_id = user_mappings.org_id
  )
  AND role = user_mappings.role::user_role
);

COMMIT;
