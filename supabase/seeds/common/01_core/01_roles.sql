-- Define base roles and permissions
BEGIN;

-- Create enum for role types if not exists
DO $$ BEGIN
    CREATE TYPE user_role AS ENUM (
        'system_admin',
        'org_admin',
        'clinical_admin',
        'physician',
        'nurse_practitioner',
        'registered_nurse',
        'medical_assistant',
        'front_desk',
        'billing_staff',
        'pharmacist',
        'lab_technician',
        'patient'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Create base role definitions
CREATE TABLE IF NOT EXISTS public.role_definitions (
    role user_role PRIMARY KEY,
    description TEXT NOT NULL,
    base_permissions JSONB NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Insert base role definitions
INSERT INTO public.role_definitions (role, description, base_permissions)
VALUES
    ('system_admin', 'System-wide administrator with full access', 
     '{
        "all": true,
        "multi_org": true,
        "system": {
            "read": true,
            "write": true,
            "admin": true
        }
     }'::jsonb),
    
    ('org_admin', 'Organization administrator',
     '{
        "org": {
            "read": true,
            "write": true,
            "admin": true
        },
        "users": {
            "read": true,
            "write": true
        },
        "patients": {
            "read": true,
            "write": true
        },
        "billing": {
            "read": true,
            "write": true
        }
     }'::jsonb),
    
    ('clinical_admin', 'Clinical administrator',
     '{
        "clinical": {
            "read": true,
            "write": true,
            "admin": true
        },
        "patients": {
            "read": true,
            "write": true
        },
        "medical_records": {
            "read": true,
            "write": true
        }
     }'::jsonb),
    
    ('physician', 'Physician',
     '{
        "patients": {
            "read": true,
            "write": true
        },
        "medical_records": {
            "read": true,
            "write": true
        },
        "prescriptions": {
            "read": true,
            "write": true
        }
     }'::jsonb),
    
    ('nurse_practitioner', 'Nurse Practitioner',
     '{
        "patients": {
            "read": true,
            "write": true
        },
        "medical_records": {
            "read": true,
            "write": true
        },
        "prescriptions": {
            "read": true,
            "write": true
        }
     }'::jsonb),
    
    ('registered_nurse', 'Registered nurse',
     '{
        "patients": {
            "read": true,
            "write": true
        },
        "medical_records": {
            "read": true,
            "write": true
        },
        "prescriptions": {
            "read": true
        }
     }'::jsonb),
    
    ('medical_assistant', 'Medical Assistant',
     '{
        "patients": {
            "read": true,
            "write": true
        },
        "medical_records": {
            "read": true,
            "write": true
        },
        "vitals": {
            "read": true,
            "write": true
        }
     }'::jsonb),
    
    ('front_desk', 'Front desk staff',
     '{
        "appointments": {
            "read": true,
            "write": true
        },
        "patients": {
            "read": true,
            "write": true,
            "medical": false
        }
     }'::jsonb),
    
    ('billing_staff', 'Billing department staff',
     '{
        "billing": {
            "read": true,
            "write": true
        },
        "patients": {
            "read": true,
            "demographics": true
        }
     }'::jsonb),
    
    ('pharmacist', 'Pharmacist',
     '{
        "prescriptions": {
            "read": true,
            "write": true,
            "verify": true
        },
        "patients": {
            "read": true,
            "medications": true
        }
     }'::jsonb),
    
    ('lab_technician', 'Laboratory Technician',
     '{
        "lab_orders": {
            "read": true,
            "write": true
        },
        "lab_results": {
            "read": true,
            "write": true
        },
        "patients": {
            "read": true,
            "lab": true
        }
     }'::jsonb),
    
    ('patient', 'Patient portal user',
     '{
        "self": {
            "read": true,
            "write": true
        },
        "appointments": {
            "read": true,
            "schedule": true
        },
        "medical_records": {
            "read": true
        }
     }'::jsonb)
ON CONFLICT (role) 
DO UPDATE SET
    description = EXCLUDED.description,
    base_permissions = EXCLUDED.base_permissions,
    updated_at = NOW();

COMMIT; 