-- Start transaction
BEGIN;

-- Insert Role Permissions
INSERT INTO public.role_permissions (
  id,
  role,
  resource,
  actions,
  conditions,
  created_at,
  updated_at
)
VALUES
  (uuid_generate_v4(), 'system_admin'::user_role, 'all', ARRAY['create', 'read', 'update', 'delete'], '{}'::jsonb, NOW(), NOW()),
  (uuid_generate_v4(), 'physician'::user_role, 'patients', ARRAY['create', 'read', 'update'], '{}'::jsonb, NOW(), NOW()),
  (uuid_generate_v4(), 'physician'::user_role, 'medical_records', ARRAY['create', 'read', 'update'], '{}'::jsonb, NOW(), NOW()),
  (uuid_generate_v4(), 'physician'::user_role, 'prescriptions', ARRAY['create', 'read', 'update', 'delete'], '{}'::jsonb, NOW(), NOW()),
  (uuid_generate_v4(), 'physician'::user_role, 'lab_results', ARRAY['create', 'read', 'update'], '{}'::jsonb, NOW(), NOW()),
  (uuid_generate_v4(), 'registered_nurse'::user_role, 'patients', ARRAY['read', 'update'], '{}'::jsonb, NOW(), NOW()),
  (uuid_generate_v4(), 'registered_nurse'::user_role, 'medical_records', ARRAY['read', 'update'], '{}'::jsonb, NOW(), NOW()),
  (uuid_generate_v4(), 'registered_nurse'::user_role, 'vital_signs', ARRAY['create', 'read', 'update'], '{}'::jsonb, NOW(), NOW()),
  (uuid_generate_v4(), 'front_desk'::user_role, 'patients', ARRAY['create', 'read', 'update'], '{}'::jsonb, NOW(), NOW()),
  (uuid_generate_v4(), 'front_desk'::user_role, 'appointments', ARRAY['create', 'read', 'update', 'delete'], '{}'::jsonb, NOW(), NOW()),
  (uuid_generate_v4(), 'billing_staff'::user_role, 'patients', ARRAY['read'], '{}'::jsonb, NOW(), NOW()),
  (uuid_generate_v4(), 'billing_staff'::user_role, 'claims', ARRAY['create', 'read', 'update'], '{}'::jsonb, NOW(), NOW()),
  (uuid_generate_v4(), 'billing_staff'::user_role, 'insurance_providers', ARRAY['create', 'read', 'update'], '{}'::jsonb, NOW(), NOW());

COMMIT;
