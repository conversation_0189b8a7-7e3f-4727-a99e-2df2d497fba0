-- Start transaction
BEGIN;

-- Insert Education Materials
INSERT INTO public.education_materials (
  id,
  title,
  content,
  category,
  language,
  format,
  metadata,
  created_at,
  updated_at
)
VALUES
  (uuid_generate_v4(), 'Understanding Hypertension', 'https://example.com/education/hypertension.pdf', 'hypertension', 'en', 'pdf', '{"audience": "adult", "reading_level": "general"}'::jsonb, NOW(), NOW()),
  (uuid_generate_v4(), 'Diabetes Management', 'https://example.com/education/diabetes.pdf', 'diabetes', 'en', 'pdf', '{"audience": "adult", "reading_level": "general"}'::jsonb, NOW(), NOW()),
  (uuid_generate_v4(), 'Asthma Action Plan', 'https://example.com/education/asthma.pdf', 'asthma', 'en', 'pdf', '{"audience": "pediatric", "reading_level": "general"}'::jsonb, NOW(), NOW()),
  (uuid_generate_v4(), 'Heart Health Guidelines', 'https://example.com/education/heart_health.pdf', 'cardiology', 'en', 'pdf', '{"audience": "adult", "reading_level": "general"}'::jsonb, NOW(), NOW()),
  (uuid_generate_v4(), 'Childhood Vaccination Schedule', 'https://example.com/education/vaccinations.pdf', 'immunizations', 'en', 'pdf', '{"audience": "pediatric", "reading_level": "general"}'::jsonb, NOW(), NOW()),
  (uuid_generate_v4(), 'Healthy Eating Guidelines', 'https://example.com/education/healthy_eating.mp4', 'nutrition', 'en', 'video', '{"audience": "all", "duration": "15 minutes"}'::jsonb, NOW(), NOW()),
  (uuid_generate_v4(), 'Managing Seasonal Allergies', '<h1>Managing Seasonal Allergies</h1><p>Tips and strategies for managing seasonal allergies...</p>', 'allergies', 'en', 'text', '{"audience": "all", "reading_level": "general"}'::jsonb, NOW(), NOW()),
  (uuid_generate_v4(), 'Medication Adherence', '<h1>Medication Adherence</h1><p>The importance of taking your medications as prescribed...</p>', 'medications', 'en', 'text', '{"audience": "adult", "reading_level": "general"}'::jsonb, NOW(), NOW()),
  (uuid_generate_v4(), 'Exercise for Heart Health', 'https://example.com/education/exercise.mp4', 'cardiology', 'en', 'video', '{"audience": "adult", "duration": "20 minutes"}'::jsonb, NOW(), NOW());

COMMIT;
