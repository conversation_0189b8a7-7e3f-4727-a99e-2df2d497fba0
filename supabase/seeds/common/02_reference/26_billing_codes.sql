-- Start transaction
BEGIN;

-- Create comprehensive billing codes
DO $BODY$
DECLARE
  i INTEGER;
  cpt_codes TEXT[] := ARRAY[
    '99213', '99214', '99215', '99202', '99203', '99204', '99205', '99211', '99212',
    '80053', '80048', '80061', '85025', '85027', '86900', '86901', '87070', '87086',
    '90471', '90472', '90715', '90716', '90734', '90736', '90746', '90792', '90834',
    '93000', '93005', '93010', '93015', '93018', '93025', '93040', '93041', '93042',
    '70450', '70460', '70470', '70480', '70486', '70490', '71045', '71046', '71047',
    '36415', '36416', '36430', '36440', '36450', '36460', '36468', '36469', '36470'
  ];
  icd_codes TEXT[] := ARRAY[
    'I10', 'E11.9', 'Z00.00', 'M79.3', 'J06.9', 'K21.9', 'N39.0', 'M25.50',
    'F41.9', 'G43.909', 'K59.00', 'R06.02', 'R50.9', 'R51', 'R53.83', 'Z23',
    'Z01.411', 'Z01.419', 'Z02.3', 'Z02.5', 'Z51.11', 'Z71.3', 'Z76.2'
  ];
  
BEGIN
  -- Insert CPT codes
  FOR i IN 1..array_length(cpt_codes, 1) LOOP
    INSERT INTO public.billing_codes (
      id,
      code,
      description,
      type,
      effective_date,
      created_at,
      updated_at
    ) VALUES (
      uuid_generate_v4(),
      cpt_codes[i],
      CASE cpt_codes[i]
        WHEN '99213' THEN 'Office/outpatient visit est patient level 3'
        WHEN '99214' THEN 'Office/outpatient visit est patient level 4'
        WHEN '99215' THEN 'Office/outpatient visit est patient level 5'
        WHEN '99202' THEN 'Office/outpatient visit new patient level 2'
        WHEN '99203' THEN 'Office/outpatient visit new patient level 3'
        WHEN '99204' THEN 'Office/outpatient visit new patient level 4'
        WHEN '99205' THEN 'Office/outpatient visit new patient level 5'
        WHEN '80053' THEN 'Comprehensive metabolic panel'
        WHEN '85025' THEN 'Blood count; complete (CBC), automated'
        WHEN '90471' THEN 'Immunization administration'
        WHEN '93000' THEN 'Electrocardiogram, routine ECG'
        WHEN '70450' THEN 'Computed tomography, head or brain'
        WHEN '36415' THEN 'Collection of venous blood by venipuncture'
        ELSE 'Medical procedure - ' || cpt_codes[i]
      END,
      'cpt',
      CURRENT_DATE - make_interval(days => (random() * 365)::int),
      NOW() - make_interval(days => (random() * 365)::int),
      NOW()
    );
  END LOOP;
  
  -- Insert ICD-10 codes
  FOR i IN 1..array_length(icd_codes, 1) LOOP
    INSERT INTO public.billing_codes (
      id,
      code,
      description,
      type,
      effective_date,
      created_at,
      updated_at
    ) VALUES (
      uuid_generate_v4(),
      icd_codes[i],
      CASE icd_codes[i]
        WHEN 'I10' THEN 'Essential hypertension'
        WHEN 'E11.9' THEN 'Type 2 diabetes mellitus without complications'
        WHEN 'Z00.00' THEN 'Encounter for general adult medical examination'
        WHEN 'M79.3' THEN 'Panniculitis, unspecified'
        WHEN 'J06.9' THEN 'Acute upper respiratory infection, unspecified'
        WHEN 'K21.9' THEN 'Gastro-esophageal reflux disease without esophagitis'
        WHEN 'N39.0' THEN 'Urinary tract infection, site not specified'
        WHEN 'F41.9' THEN 'Anxiety disorder, unspecified'
        WHEN 'G43.909' THEN 'Migraine, unspecified, not intractable, without status migrainosus'
        WHEN 'R50.9' THEN 'Fever, unspecified'
        WHEN 'Z23' THEN 'Encounter for immunization'
        ELSE 'Medical diagnosis - ' || icd_codes[i]
      END,
      'icd-10',
      CURRENT_DATE - make_interval(days => (random() * 365)::int),
      NOW() - make_interval(days => (random() * 365)::int),
      NOW()
    );
  END LOOP;
  
  -- Add more procedure codes
  FOR i IN 1..100 LOOP
    INSERT INTO public.billing_codes (
      id,
      code,
      description,
      type,
      effective_date,
      created_at,
      updated_at
    ) VALUES (
      uuid_generate_v4(),
      'PROC' || LPAD(i::text, 4, '0'),
      'Healthcare procedure ' || i,
      CASE WHEN i % 4 = 0 THEN 'laboratory' 
           WHEN i % 4 = 1 THEN 'radiology'
           WHEN i % 4 = 2 THEN 'procedures'
           ELSE 'evaluation_management' END,
      CURRENT_DATE - make_interval(days => (random() * 365)::int),
      NOW() - make_interval(days => (random() * 365)::int),
      NOW()
    );
  END LOOP;
END
$BODY$;

COMMIT;
