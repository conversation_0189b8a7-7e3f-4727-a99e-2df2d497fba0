-- Start transaction
BEGIN;

-- Create notification templates for all organizations
DO $BODY$
DECLARE
  org_record RECORD;
  template_type notification_type;
  template_types notification_type[] := ARRAY['appointment_reminder', 'lab_result', 'prescription_update', 'medical_record_update', 'task_assignment', 'message', 'alert', 'system_update'];
  
BEGIN
  -- Create templates for each organization and each notification type
  FOR org_record IN
    SELECT id, name FROM organizations
  LOOP
    FOREACH template_type IN ARRAY template_types
    LOOP
      INSERT INTO public.notification_templates (
        id,
        organization_id,
        type,
        name,
        subject_template,
        content_template,
        metadata_template,
        created_at,
        updated_at
      ) VALUES (
        uuid_generate_v4(),
        org_record.id,
        template_type,
        CASE template_type
          WHEN 'appointment_reminder' THEN 'Appointment Reminder - ' || org_record.name
          WHEN 'lab_result' THEN 'Lab Results Available - ' || org_record.name
          WHEN 'prescription_update' THEN 'Prescription Update - ' || org_record.name
          WHEN 'medical_record_update' THEN 'Medical Record Updated - ' || org_record.name
          WHEN 'task_assignment' THEN 'New Task Assignment - ' || org_record.name
          WHEN 'message' THEN 'New Message - ' || org_record.name
          WHEN 'alert' THEN 'Important Alert - ' || org_record.name
          WHEN 'system_update' THEN 'System Update - ' || org_record.name
        END,
        CASE template_type
          WHEN 'appointment_reminder' THEN 'Appointment Reminder: {{appointment_date}} with {{provider_name}}'
          WHEN 'lab_result' THEN 'Lab Results Available for Review'
          WHEN 'prescription_update' THEN 'Prescription Update: {{medication_name}}'
          WHEN 'medical_record_update' THEN 'Medical Record Updated by {{provider_name}}'
          WHEN 'task_assignment' THEN 'New Task: {{task_title}}'
          WHEN 'message' THEN 'New Message from {{sender_name}}'
          WHEN 'alert' THEN 'Alert: {{alert_type}}'
          WHEN 'system_update' THEN 'System Update: {{update_title}}'
        END,
        CASE template_type
          WHEN 'appointment_reminder' THEN 'Dear {{patient_name}}, this is a reminder for your upcoming appointment on {{appointment_date}} at {{appointment_time}} with {{provider_name}} at {{facility_name}}. Please arrive 15 minutes early for check-in. If you need to reschedule, please call us at least 24 hours in advance.'
          WHEN 'lab_result' THEN 'Dear {{patient_name}}, your lab results from {{test_date}} are now available for review. Please log into your patient portal or contact our office to discuss the results with your healthcare provider.'
          WHEN 'prescription_update' THEN 'Dear {{patient_name}}, there has been an update to your prescription for {{medication_name}}. New instructions: {{instructions}}. Please contact your pharmacy for the updated prescription.'
          WHEN 'medical_record_update' THEN 'Dear {{patient_name}}, your medical record has been updated by {{provider_name}} on {{update_date}}. The update includes: {{update_summary}}. You can view the complete record in your patient portal.'
          WHEN 'task_assignment' THEN 'You have been assigned a new task: {{task_title}}. Description: {{task_description}}. Due date: {{due_date}}. Priority: {{priority}}. Please review and take appropriate action.'
          WHEN 'message' THEN 'You have received a new message from {{sender_name}}. Subject: {{message_subject}}. Please log into your account to read the full message and respond if needed.'
          WHEN 'alert' THEN 'IMPORTANT ALERT: {{alert_message}}. This requires your immediate attention. Please review the details and take any necessary action promptly.'
          WHEN 'system_update' THEN 'System Update Notice: {{update_description}}. Scheduled for: {{update_date}}. Expected duration: {{duration}}. We apologize for any inconvenience this may cause.'
        END,
        jsonb_build_object(
          'priority', CASE template_type 
            WHEN 'alert' THEN 'high'
            WHEN 'appointment_reminder' THEN 'medium'
            ELSE 'normal'
          END,
          'delivery_method', CASE template_type
            WHEN 'alert' THEN ARRAY['push', 'email', 'sms']
            WHEN 'appointment_reminder' THEN ARRAY['push', 'email']
            ELSE ARRAY['push']
          END,
          'auto_send', CASE template_type
            WHEN 'appointment_reminder' THEN true
            WHEN 'lab_result' THEN true
            ELSE false
          END
        ),
        NOW() - make_interval(days => (random() * 30)::int),
        NOW()
      );
    END LOOP;
  END LOOP;
END
$BODY$;

COMMIT;
