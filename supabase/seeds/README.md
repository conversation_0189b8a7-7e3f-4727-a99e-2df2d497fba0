# Supabase Seed Files

This directory contains SQL seed files for populating the Supabase database with test data.

## Directory Structure

```
seeds/
├── common/                 # Common seed files used across all environments
│   ├── 01_auth.sql         # Authentication users and identities
│   ├── 02_organizations.sql # Organizations
│   └── ...
│
├── environments/           # Environment-specific seed files
│   ├── development/        # Development environment seeds
│   │   ├── 01_test_users.sql
│   │   └── ...
│   ├── staging/            # Staging environment seeds
│   │   ├── 01_test_users.sql
│   │   └── ...
│   └── production/         # Production environment seeds
│       ├── 01_admin_users.sql
│       └── ...
│
└── README.md               # This file
```

## Usage

The seed files are applied automatically when running:

```bash
# For local development
npm run supabase:setup

# For remote environments
npm run supabase:push
```

## File Naming Convention

Files are executed in alphabetical order, so we use a numeric prefix to control the execution order:

- `00_` - Setup and configuration
- `01_` - Core data (users, organizations)
- `02_` - Secondary data (facilities, departments)
- `03_` - Tertiary data (providers, patients)
- etc.

## Environment-Specific Seeds

Each environment has its own set of seed files:

- **Development**: Contains a full set of test data for development purposes
- **Staging**: Contains a reduced set of test data for testing
- **Production**: Contains only essential data needed for production

## Security Considerations

- **DO NOT** include real patient data in any seed files
- **DO NOT** commit sensitive credentials to version control
- Use the 1Password CLI to manage sensitive information
- For production, use secure password generation and store credentials in 1Password

## Adding New Seed Files

1. Determine if the seed data should be common or environment-specific
2. Create the SQL file in the appropriate directory
3. Follow the naming convention to ensure proper execution order
4. Update the `config.toml` file if necessary to include the new seed file

## Refreshing Seed Data

To refresh the seed data in a remote environment:

```bash
# Link to the appropriate environment
npm run supabase:link:development  # or staging, production

# Push the seed data
npm run supabase:push
```
