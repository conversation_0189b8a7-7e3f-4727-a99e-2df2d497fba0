-- Start transaction
BEGIN;

-- Create referrals for patients
DO $BODY$
DECLARE
  patient_record RECORD;
  ref_date DATE;
  
  referral_reasons TEXT[] := ARRAY[
    'Specialist consultation needed',
    'Complex case requiring expert opinion',
    'Second opinion requested',
    'Advanced imaging required',
    'Surgical evaluation needed',
    'Chronic condition management',
    'Treatment not responding as expected'
  ];
  
BEGIN
  -- Create referrals for random patients
  FOR patient_record IN
    SELECT p.id as patient_id
    FROM patients p
    ORDER BY RANDOM()
    LIMIT 50  -- Create referrals for 50 patients
  LOOP
    -- Calculate referral date first
    ref_date := (NOW() - make_interval(days => (random() * 90)::int))::date;
    
    INSERT INTO public.referrals (
      id,
      patient_id,
      referring_provider_id,
      referred_to_provider_id,
      reason,
      priority,
      status,
      notes,
      referral_date,
      scheduled_date,
      completed_date,
      metadata,
      created_at,
      updated_at
    ) VALUES (
      uuid_generate_v4(),
      patient_record.patient_id,
      (SELECT hp.id FROM healthcare_providers hp ORDER BY RANDOM() LIMIT 1),
      (SELECT hp.id FROM healthcare_providers hp ORDER BY RANDOM() LIMIT 1),
      referral_reasons[1 + (random() * (array_length(referral_reasons, 1) - 1))::int],
      CASE floor(random() * 3)::int
        WHEN 0 THEN 'routine'
        WHEN 1 THEN 'urgent'
        ELSE 'emergency'
      END::referral_priority,
      CASE floor(random() * 4)::int
        WHEN 0 THEN 'pending'
        WHEN 1 THEN 'scheduled'
        WHEN 2 THEN 'completed'
        ELSE 'cancelled'
      END::referral_status,
      'Patient referred for specialized care and evaluation',
      ref_date,
      CASE WHEN random() < 0.6 THEN ref_date + make_interval(days => (1 + floor(random() * 30)::int)) ELSE NULL END,
      CASE WHEN random() < 0.3 THEN ref_date + make_interval(days => (1 + floor(random() * 60)::int)) ELSE NULL END,
      jsonb_build_object('specialty', 'General Medicine'),
      NOW() - make_interval(days => (random() * 30)::int),
      NOW()
    );
  END LOOP;
END
$BODY$;

COMMIT;
