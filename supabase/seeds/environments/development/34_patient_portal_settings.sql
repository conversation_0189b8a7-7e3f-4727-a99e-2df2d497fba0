-- Start transaction
BEGIN;

-- Create patient portal settings for all patients
DO $BODY$
DECLARE
  patient_record RECORD;
  
BEGIN
  -- Create portal settings for all patients
  FOR patient_record IN
    SELECT id, organization_id
    FROM patients
  LOOP
    INSERT INTO public.patient_portal_settings (
      id,
      patient_id,
      preferences,
      communication_settings,
      created_at,
      updated_at
    ) VALUES (
      uuid_generate_v4(),
      patient_record.id,
      jsonb_build_object(
        'notifications_enabled', random() < 0.8,
        'two_factor_enabled', random() < 0.3,
        'share_with_family', random() < 0.6,
        'marketing_emails', random() < 0.4,
        'data_sharing', random() < 0.2,
        'appointment_reminders', random() < 0.9,
        'last_login', CASE 
          WHEN random() < 0.7 THEN (NOW() - make_interval(days => (random() * 30)::int))::text
          ELSE NULL
        END
      ),
      jsonb_build_object(
        'email_notifications', random() < 0.8,
        'sms_notifications', random() < 0.5,
        'push_notifications', random() < 0.6,
        'frequency', (ARRAY['immediate', 'daily', 'weekly'])[floor(random() * 3)::int + 1],
        'preferred_time', (ARRAY['morning', 'afternoon', 'evening'])[floor(random() * 3)::int + 1]
      ),
      NOW() - make_interval(days => (random() * 365)::int),
      NOW() - make_interval(days => (random() * 30)::int)
    );
  END LOOP;
END
$BODY$;

COMMIT;
