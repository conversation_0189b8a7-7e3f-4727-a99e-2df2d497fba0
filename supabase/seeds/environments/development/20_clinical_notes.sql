-- Start transaction
BEGIN;

-- Create clinical notes for medical records
DO $BODY$
DECLARE
  medical_record RECORD;
  note_count INTEGER;
  i INTEGER;
  note_types TEXT[] := ARRAY[
    'Progress Note',
    'SOAP Note', 
    'Consultation Note',
    'Discharge Summary',
    'Procedure Note',
    'Follow-up Note',
    'Assessment Note',
    'Treatment Plan',
    'Lab Review Note'
  ];
  note_content TEXT[] := ARRAY[
    'Patient presented with chief complaint of ongoing symptoms. Physical examination reveals normal vital signs.',
    'Reviewed recent test results with patient. Discussed treatment options and next steps.',
    'Patient tolerated procedure well. No immediate complications noted. Follow-up scheduled.',
    'Medication compliance reviewed. Patient reports good adherence to current regimen.',
    'Assessment shows improvement in condition. Plan to continue current treatment approach.',
    'Patient education provided regarding condition management and preventive measures.',
    'Comprehensive review of systems completed. All systems within normal limits.',
    'Discussed lifestyle modifications and dietary recommendations with patient.',
    'Laboratory results reviewed. Values within expected ranges for current treatment.',
    'Patient reports symptomatic improvement since last visit. Plan to monitor progress.'
  ];
BEGIN
  -- Create notes for medical records
  FOR medical_record IN
    SELECT mr.id as record_id, mr.provider_id, mr.created_at as record_date
    FROM medical_records mr
    ORDER BY RANDOM()
    LIMIT 100  -- Create notes for 100 medical records
  LOOP
    -- Each medical record gets 1-3 clinical notes
    note_count := 1 + floor(random() * 3)::int;
    
    FOR i IN 1..note_count LOOP
      INSERT INTO public.clinical_notes (
        id,
        medical_record_id,
        note_type,
        content,
        signed_by,
        signed_at,
        created_at,
        updated_at
      ) VALUES (
        uuid_generate_v4(),
        medical_record.record_id,
        note_types[1 + (random() * (array_length(note_types, 1) - 1))::int],
        note_content[1 + (random() * (array_length(note_content, 1) - 1))::int],
        (SELECT hp.user_id FROM healthcare_providers hp WHERE hp.id = medical_record.provider_id),
        medical_record.record_date + make_interval(hours => (random() * 24)::int),
        medical_record.record_date,
        medical_record.record_date + make_interval(hours => (random() * 48)::int)
      );
    END LOOP;
  END LOOP;
END
$BODY$;

COMMIT;
