-- Start transaction
BEGIN;

-- Create patient alerts
DO $BODY$
DECLARE
  patient_record RECORD;
  
  alert_types TEXT[] := ARRAY['allergy', 'medication', 'condition', 'emergency', 'follow_up'];
  alert_descriptions TEXT[] := ARRAY[
    'Patient has documented allergy - verify before prescribing',
    'Critical lab values require immediate attention',
    'Patient medication refill is due',
    'Upcoming appointment needs confirmation',
    'Emergency contact information needs updating',
    'Insurance authorization required for procedure'
  ];

BEGIN
  -- Create alerts for random patients
  FOR patient_record IN
    SELECT p.id as patient_id
    FROM patients p
    ORDER BY RANDOM()
    LIMIT 80  -- Create alerts for 80 patients
  LOOP
    INSERT INTO public.patient_alerts (
      id,
      patient_id,
      alert_type,
      description,
      severity,
      status,
      start_date,
      end_date,
      created_by,
      metadata,
      created_at,
      updated_at
    ) VALUES (
      uuid_generate_v4(),
      patient_record.patient_id,
      alert_types[1 + (random() * (array_length(alert_types, 1) - 1))::int],
      alert_descriptions[1 + (random() * (array_length(alert_descriptions, 1) - 1))::int],
      CASE floor(random() * 3)::int
        WHEN 0 THEN 'low'
        WHEN 1 THEN 'medium'
        ELSE 'high'
      END::alert_severity,
      CASE WHEN random() < 0.8 THEN 'active' ELSE 'resolved' END::alert_status,
      (NOW() - make_interval(days => (random() * 60)::int))::date,
      CASE WHEN random() < 0.4 THEN (NOW() + make_interval(days => (30 + floor(random() * 90)::int)))::date ELSE NULL END,
      (SELECT u.id FROM auth.users u ORDER BY RANDOM() LIMIT 1),
      '{"auto_generated": true}'::jsonb,
      NOW() - make_interval(days => (random() * 60)::int),
      NOW()
    );
  END LOOP;
END
$BODY$;

COMMIT;
