-- Start transaction
BEGIN;

-- Create conversations between patients and providers
DO $BODY$
DECLARE
  patient_record RECORD;
  conversation_count INTEGER;
  i INTEGER;
  conversation_id uuid;
  provider_user_id uuid;
  patient_user_id uuid;
  
  topics TEXT[] := ARRAY['Medication Question', 'Appointment Scheduling', 'Lab Results', 'Symptom Discussion', 'Follow-up Care', 'Insurance Question', 'Prescription Refill', 'Test Results'];
  types conversation_type[] := ARRAY['direct'::conversation_type, 'group'::conversation_type];
  
BEGIN
  -- Create conversations for random patients
  FOR patient_record IN
    SELECT p.id as patient_id, p.user_id as patient_user_id, p.first_name, p.last_name, p.organization_id
    FROM patients p
    WHERE random() < 0.6  -- 60% of patients have conversations
    AND p.user_id IS NOT NULL  -- Only patients with user accounts
  LOOP
    -- Each patient gets 1-3 conversations
    conversation_count := 1 + floor(random() * 3)::int;
    
    FOR i IN 1..conversation_count LOOP
      conversation_id := uuid_generate_v4();
      
      -- Get a random provider from same organization
      SELECT user_id INTO provider_user_id
      FROM healthcare_providers 
      WHERE organization_id = patient_record.organization_id 
      ORDER BY RANDOM() 
      LIMIT 1;
      
      INSERT INTO public.conversations (
        id,
        title,
        type,
        created_by,
        created_at,
        updated_at
      ) VALUES (
        conversation_id,
        topics[1 + (random() * (array_length(topics, 1) - 1))::int] || ' - ' || patient_record.first_name || ' ' || patient_record.last_name,
        types[1 + (random() * (array_length(types, 1) - 1))::int],
        provider_user_id,
        NOW() - make_interval(days => (random() * 90)::int),
        NOW() - make_interval(days => (random() * 30)::int)
      );
      
      -- Add patient as participant
      INSERT INTO public.conversation_participants (
        id,
        conversation_id,
        user_id,
        role,
        created_at
      ) VALUES (
        uuid_generate_v4(),
        conversation_id,
        patient_record.patient_user_id,
        'member',
        NOW() - make_interval(days => (random() * 90)::int)
      );
      
      -- Add the provider as participant
      INSERT INTO public.conversation_participants (
        id,
        conversation_id,
        user_id,
        role,
        created_at
      ) VALUES (
        uuid_generate_v4(),
        conversation_id,
        provider_user_id,
        'member',
        NOW() - make_interval(days => (random() * 90)::int)
      );
    END LOOP;
  END LOOP;
END
$BODY$;

COMMIT;
