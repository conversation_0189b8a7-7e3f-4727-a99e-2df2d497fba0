-- Start transaction
BEGIN;

-- Create conversation participants for existing conversations
DO $BODY$
DECLARE
  conversation_record RECORD;
  participant_count INTEGER;
  i INTEGER;
  
BEGIN
  -- Add participants to existing conversations
  FOR conversation_record IN
    SELECT c.id as conversation_id, c.created_by, c.type
    FROM conversations c
  LOOP
    -- Each conversation gets 2-4 participants
    participant_count := 2 + floor(random() * 3)::int;
    
    -- Always add the conversation creator as first participant
    INSERT INTO public.conversation_participants (
      id,
      conversation_id,
      user_id,
      role,
      last_read_at,
      created_at
    ) VALUES (
      uuid_generate_v4(),
      conversation_record.conversation_id,
      conversation_record.created_by,
      'admin',
      NOW() - make_interval(hours => (floor(random() * 48)::int)),
      NOW() - make_interval(days => (random() * 30)::int)
    )
    ON CONFLICT (conversation_id, user_id) DO NOTHING;
    
    -- Add additional random participants
    FOR i IN 2..participant_count LOOP
      INSERT INTO public.conversation_participants (
        id,
        conversation_id,
        user_id,
        role,
        last_read_at,
        created_at
      ) VALUES (
        uuid_generate_v4(),
        conversation_record.conversation_id,
        (SELECT u.id FROM auth.users u WHERE u.id != conversation_record.created_by ORDER BY RANDOM() LIMIT 1),
        CASE WHEN random() < 0.8 THEN 'member' ELSE 'moderator' END,
        CASE WHEN random() < 0.7 THEN NOW() - make_interval(hours => (floor(random() * 72)::int)) ELSE NULL END,
        NOW() - make_interval(days => (random() * 30)::int)
      )
      ON CONFLICT (conversation_id, user_id) DO NOTHING;
    END LOOP;
  END LOOP;
END
$BODY$;

COMMIT; 