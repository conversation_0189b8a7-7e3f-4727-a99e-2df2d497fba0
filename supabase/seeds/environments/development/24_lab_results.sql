-- Start transaction
BEGIN;

-- Generate comprehensive lab results for patients
DO $$
DECLARE
  patient_rec RECORD;
  provider_rec RECORD;
  test_date_val TIMESTAMP;
  i INTEGER;
  lab_tests TEXT[] := ARRAY[
    'Complete Blood Count (CBC)',
    'Basic Metabolic Panel (BMP)',
    'Comprehensive Metabolic Panel (CMP)',
    'Lipid Panel',
    'Thyroid Function Tests',
    'Hemoglobin A1C',
    'Urinalysis',
    'Liver Function Tests',
    'Coagulation Studies (PT/INR)',
    'Vitamin D Level',
    'Vitamin B12 Level',
    'Iron Studies',
    'Inflammatory Markers (ESR/CRP)',
    'Cardiac Enzymes',
    'Tumor Markers',
    'Allergy Panel',
    'Strep Test',
    'Flu Test',
    'COVID-19 Test',
    'Blood Culture',
    'Urine Culture',
    'Stool Culture',
    'Pregnancy Test',
    'PSA (Prostate Specific Antigen)',
    'Mammography',
    'Pap Smear',
    'Colonoscopy Biopsy',
    'Skin Biopsy',
    'Bone Density Scan',
    'Echocardiogram'
  ];
  test_name_val TEXT;
  results_val JSONB;
  normal_range_val JSONB;
  notes_val TEXT;
BEGIN
  -- Create lab results for patients (about 30% of patients get lab work)
  FOR patient_rec IN
    SELECT p.id, p.first_name || ' ' || p.last_name AS name, p.organization_id, p.date_of_birth, p.medical_history
    FROM public.patients p
    WHERE random() < 0.3  -- 30% of patients get lab results
  LOOP
    -- Find a provider in the same organization
    SELECT hp.id, hp.first_name || ' ' || hp.last_name AS name
    INTO provider_rec
    FROM public.healthcare_providers hp
    WHERE hp.organization_id = patient_rec.organization_id
    ORDER BY random()
    LIMIT 1;

    -- Skip if no provider found
    CONTINUE WHEN provider_rec IS NULL;

    -- Create 1-4 lab results per patient
    FOR i IN 1..(1 + floor(random() * 4)::int) LOOP
      -- Select random test
      test_name_val := lab_tests[1 + (random() * (array_length(lab_tests, 1) - 1))::int];

      -- Generate test date (within last 2 years)
      test_date_val := NOW() - (random() * 730)::int * INTERVAL '1 day';

      -- Generate realistic results based on test type
      CASE
        WHEN test_name_val LIKE '%CBC%' THEN
          results_val := jsonb_build_object(
            'WBC', ROUND((4.5 + random() * 6.5)::numeric, 1),
            'RBC', ROUND((4.2 + random() * 1.2)::numeric, 1),
            'Hemoglobin', ROUND((12.0 + random() * 4.0)::numeric, 1),
            'Hematocrit', ROUND((37 + random() * 10)::numeric, 0),
            'Platelets', ROUND((150 + random() * 250)::numeric, 0)
          );
          normal_range_val := jsonb_build_object(
            'WBC', '4.5-11.0 K/uL',
            'RBC', '4.2-5.4 M/uL',
            'Hemoglobin', '12.0-16.0 g/dL',
            'Hematocrit', '37-47%',
            'Platelets', '150-400 K/uL'
          );
          notes_val := 'Complete blood count with differential. ' ||
                      CASE WHEN random() < 0.8 THEN 'All values within normal limits.'
                           ELSE 'Some values slightly outside normal range.' END;

        WHEN test_name_val LIKE '%Metabolic%' THEN
          results_val := jsonb_build_object(
            'Glucose', ROUND((70 + random() * 60)::numeric, 0),
            'BUN', ROUND((7 + random() * 20)::numeric, 0),
            'Creatinine', ROUND((0.6 + random() * 0.8)::numeric, 1),
            'Sodium', ROUND((135 + random() * 10)::numeric, 0),
            'Potassium', ROUND((3.5 + random() * 1.5)::numeric, 1),
            'Chloride', ROUND((98 + random() * 10)::numeric, 0)
          );
          normal_range_val := jsonb_build_object(
            'Glucose', '70-99 mg/dL',
            'BUN', '7-20 mg/dL',
            'Creatinine', '0.6-1.2 mg/dL',
            'Sodium', '135-145 mEq/L',
            'Potassium', '3.5-5.0 mEq/L',
            'Chloride', '98-108 mEq/L'
          );
          notes_val := 'Basic metabolic panel. ' ||
                      CASE WHEN random() < 0.7 THEN 'Kidney function normal.'
                           ELSE 'Monitor kidney function.' END;

        WHEN test_name_val LIKE '%Lipid%' THEN
          results_val := jsonb_build_object(
            'Total_Cholesterol', ROUND((150 + random() * 100)::numeric, 0),
            'LDL', ROUND((70 + random() * 80)::numeric, 0),
            'HDL', ROUND((35 + random() * 45)::numeric, 0),
            'Triglycerides', ROUND((50 + random() * 200)::numeric, 0)
          );
          normal_range_val := jsonb_build_object(
            'Total_Cholesterol', '<200 mg/dL',
            'LDL', '<100 mg/dL',
            'HDL', '>40 mg/dL (M), >50 mg/dL (F)',
            'Triglycerides', '<150 mg/dL'
          );
          notes_val := 'Lipid panel for cardiovascular risk assessment. ' ||
                      CASE WHEN random() < 0.6 THEN 'Lipid levels acceptable.'
                           ELSE 'Consider lifestyle modifications.' END;

        WHEN test_name_val LIKE '%A1C%' THEN
          results_val := jsonb_build_object(
            'HbA1c', ROUND((5.0 + random() * 4.0)::numeric, 1)
          );
          normal_range_val := jsonb_build_object(
            'HbA1c', '<5.7% (normal), 5.7-6.4% (prediabetes), ≥6.5% (diabetes)'
          );
          notes_val := 'Hemoglobin A1C for diabetes monitoring. ' ||
                      CASE
                        WHEN (results_val->>'HbA1c')::numeric < 5.7 THEN 'Normal glucose control.'
                        WHEN (results_val->>'HbA1c')::numeric < 6.5 THEN 'Prediabetic range.'
                        ELSE 'Diabetic range - continue monitoring.'
                      END;

        WHEN test_name_val LIKE '%Thyroid%' THEN
          results_val := jsonb_build_object(
            'TSH', ROUND((0.5 + random() * 4.0)::numeric, 2),
            'T4', ROUND((4.5 + random() * 7.0)::numeric, 1),
            'T3', ROUND((80 + random() * 120)::numeric, 0)
          );
          normal_range_val := jsonb_build_object(
            'TSH', '0.5-4.5 mIU/L',
            'T4', '4.5-11.5 ug/dL',
            'T3', '80-200 ng/dL'
          );
          notes_val := 'Thyroid function tests. ' ||
                      CASE WHEN random() < 0.8 THEN 'Thyroid function normal.'
                           ELSE 'Follow up recommended.' END;

        WHEN test_name_val LIKE '%Strep%' THEN
          results_val := jsonb_build_object(
            'Result', CASE WHEN random() < 0.3 THEN 'Positive' ELSE 'Negative' END
          );
          normal_range_val := jsonb_build_object(
            'Result', 'Negative'
          );
          notes_val := CASE WHEN results_val->>'Result' = 'Positive'
                           THEN 'Positive for Group A Streptococcus. Antibiotic treatment recommended.'
                           ELSE 'Negative for strep throat.' END;

        WHEN test_name_val LIKE '%Allergy%' THEN
          results_val := jsonb_build_object(
            'Peanut', CASE WHEN random() < 0.1 THEN 'Positive' ELSE 'Negative' END,
            'Shellfish', CASE WHEN random() < 0.08 THEN 'Positive' ELSE 'Negative' END,
            'Egg', CASE WHEN random() < 0.05 THEN 'Positive' ELSE 'Negative' END,
            'Milk', CASE WHEN random() < 0.03 THEN 'Positive' ELSE 'Negative' END,
            'Wheat', CASE WHEN random() < 0.02 THEN 'Positive' ELSE 'Negative' END
          );
          normal_range_val := jsonb_build_object(
            'All_Allergens', 'Negative'
          );
          notes_val := 'Comprehensive allergy panel. ' ||
                      CASE WHEN results_val ? 'Positive' THEN 'Positive reactions found - avoid allergens.'
                           ELSE 'No significant allergies detected.' END;

        ELSE
          -- Generic lab result
          results_val := jsonb_build_object(
            'Result', CASE WHEN random() < 0.8 THEN 'Normal' ELSE 'Abnormal' END,
            'Value', ROUND((random() * 100)::numeric, 1)
          );
          normal_range_val := jsonb_build_object(
            'Reference', 'See lab reference ranges'
          );
          notes_val := 'Laboratory test completed. ' ||
                      CASE WHEN results_val->>'Result' = 'Normal'
                           THEN 'Results within expected range.'
                           ELSE 'Results require clinical correlation.' END;
      END CASE;

      -- Insert the lab result
      INSERT INTO public.lab_results (
        id,
        patient_id,
        provider_id,
        test_name,
        test_date,
        results,
        normal_range,
        notes,
        created_at,
        updated_at
      ) VALUES (
        uuid_generate_v4(),
        patient_rec.id,
        provider_rec.id,
        test_name_val,
        test_date_val,
        results_val,
        normal_range_val,
        notes_val,
        test_date_val + INTERVAL '1 day',
        test_date_val + INTERVAL '2 days'
      );
    END LOOP;
  END LOOP;
END
$$;

COMMIT;
