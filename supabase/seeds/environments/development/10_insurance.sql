-- Start transaction
BEGIN;

-- Insert Insurance Providers using CTE
WITH insurance_data (name, id) AS (
  VALUES
    ('Blue Cross Blue Shield', uuid_generate_v4()),
    ('Aetna', uuid_generate_v4()),
    ('United Healthcare', uuid_generate_v4()),
    ('Cigna', uuid_generate_v4()),
    ('Humana', uuid_generate_v4())
)
INSERT INTO public.insurance_providers (
  id,
  name,
  contact_info,
  settings,
  created_at,
  updated_at
)
SELECT
  i.id,
  i.name,
  CASE i.name
    WHEN 'Blue Cross Blue Shield' THEN '{"phone": "**************", "email": "<EMAIL>", "website": "www.bcbs.com"}'::jsonb
    WHEN 'Aetna' THEN '{"phone": "**************", "email": "<EMAIL>", "website": "www.aetna.com"}'::jsonb
    WHEN 'United Healthcare' THEN '{"phone": "**************", "email": "<EMAIL>", "website": "www.uhc.com"}'::jsonb
    WHEN 'Cigna' THEN '{"phone": "**************", "email": "<EMAIL>", "website": "www.cigna.com"}'::jsonb
    WHEN 'Humana' THEN '{"phone": "**************", "email": "<EMAIL>", "website": "www.humana.com"}'::jsonb
  END as contact_info,
  CASE i.name
    WHEN 'Blue Cross Blue Shield' THEN '{"type": "ppo", "copay": {"primary": 20, "specialist": 40}, "deductible": 1000, "out_of_pocket_max": 5000}'::jsonb
    WHEN 'Aetna' THEN '{"type": "hmo", "copay": {"primary": 15, "specialist": 30}, "deductible": 1500, "out_of_pocket_max": 4000}'::jsonb
    WHEN 'United Healthcare' THEN '{"type": "ppo", "copay": {"primary": 25, "specialist": 45}, "deductible": 2000, "out_of_pocket_max": 6000}'::jsonb
    WHEN 'Cigna' THEN '{"type": "epo", "copay": {"primary": 30, "specialist": 50}, "deductible": 1200, "out_of_pocket_max": 4500}'::jsonb
    WHEN 'Humana' THEN '{"type": "ppo", "copay": {"primary": 20, "specialist": 35}, "deductible": 1800, "out_of_pocket_max": 5500}'::jsonb
  END as settings,
  NOW(),
  NOW()
FROM insurance_data i;

COMMIT;