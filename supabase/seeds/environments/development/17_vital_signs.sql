-- Start transaction
BEGIN;

-- Create vital signs for patients
DO $BODY$
DECLARE
  patient_record RECORD;
  vitals_count INTEGER;
  i INTEGER;
  
  age_group INTEGER;
  height_cm INTEGER;
  weight_kg INTEGER;
  bmi DECIMAL(5,2);
  
BEGIN
  -- Create vital signs for patients (multiple readings per patient)
  FOR patient_record IN
    SELECT p.id, p.date_of_birth, p.organization_id
    FROM patients p
  LOOP
    -- Each patient gets 2-8 vital sign readings over time
    vitals_count := 2 + floor(random() * 7)::int;
    
    -- Calculate patient age
    age_group := EXTRACT(YEARS FROM age(NOW(), patient_record.date_of_birth));
    
    -- Generate realistic height and weight based on age
    IF age_group < 18 THEN
      height_cm := 120 + floor(random() * 60)::int; -- 120-180cm for children
      weight_kg := 30 + floor(random() * 40)::int;  -- 30-70kg for children
    ELSE
      height_cm := 150 + floor(random() * 50)::int; -- 150-200cm for adults
      weight_kg := 50 + floor(random() * 50)::int;  -- 50-100kg for adults
    END IF;
    
    bmi := ROUND((weight_kg::decimal / ((height_cm / 100.0) * (height_cm / 100.0))), 2);
    
    FOR i IN 1..vitals_count LOOP
      INSERT INTO public.vital_signs (
        id,
        patient_id,
        recorded_at,
        blood_pressure_systolic,
        blood_pressure_diastolic,
        heart_rate,
        respiratory_rate,
        temperature,
        oxygen_saturation,
        height,
        weight,
        bmi,
        pain_level,
        notes,
        created_at,
        updated_at
      ) VALUES (
        uuid_generate_v4(),
        patient_record.id,
        NOW() - make_interval(days => (i * 15 + random() * 10)::int), -- Spread over time
        -- Normal vital ranges with some variation
        100 + floor(random() * 40)::int, -- systolic 100-140
        60 + floor(random() * 30)::int,  -- diastolic 60-90
        60 + floor(random() * 40)::int,  -- heart rate 60-100
        12 + floor(random() * 8)::int,   -- respiratory rate 12-20
        36.0 + (random() * 2.5),        -- temperature 36.0-38.5°C
        95 + floor(random() * 6)::int,   -- oxygen sat 95-100%
        height_cm,
        weight_kg + (random() * 4 - 2)::int, -- slight weight variation
        bmi + (random() * 1 - 0.5),
        floor(random() * 11)::int,       -- pain scale 0-10
        CASE
          WHEN random() < 0.3 THEN 'Patient appears comfortable'
          WHEN random() < 0.6 THEN 'Normal vital signs'
          WHEN random() < 0.8 THEN 'Patient alert and responsive'
          ELSE 'Routine check, all normal'
        END,
        NOW() - make_interval(days => (random() * 365)::int),
        NOW() - make_interval(days => (random() * 30)::int)
      );
    END LOOP;
  END LOOP;
END
$BODY$;

COMMIT;
