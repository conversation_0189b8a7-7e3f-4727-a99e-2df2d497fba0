-- Start transaction
BEGIN;

-- Insert Inventory Items
WITH facility_data AS (
  SELECT id, name, organization_id
  FROM public.locations
)
INSERT INTO public.inventory_items (
  id,
      location_id,
  organization_id,
  name,
  type,
  quantity,
  unit,
  minimum_quantity,
  location,
  metadata,
  created_at,
  updated_at
)
SELECT
  uuid_generate_v4(),
      f.id as location_id,
  f.organization_id,
  item_name,
  item_type,
  quantity,
  'each',
  minimum_quantity,
  location,
  jsonb_build_object(
    'description', description,
    'sku', sku,
    'unit_cost', unit_cost
  ),
  NOW(),
  NOW()
FROM facility_data f
CROSS JOIN (
  VALUES
    ('Examination Gloves', 'supplies', 'Disposable nitrile examination gloves, medium', 'GLV-NIT-MED', 500, 0.15, 100, 'Supply Room A'),
    ('Syringes 5ml', 'supplies', '5ml disposable syringes', 'SYR-5ML', 200, 0.25, 50, 'Supply Room A'),
    ('Gauze Pads', 'supplies', '4x4 sterile gauze pads', 'GZE-4X4', 300, 0.10, 75, 'Supply Room A'),
    ('Alcohol Swabs', 'supplies', 'Isopropyl alcohol prep pads', 'ALC-SWAB', 1000, 0.05, 200, 'Supply Room A'),
    ('Influenza Vaccine', 'medication', 'Seasonal influenza vaccine', 'VAC-FLU', 50, 15.00, 10, 'Medication Refrigerator'),
    ('Amoxicillin 500mg', 'medication', 'Amoxicillin 500mg capsules', 'MED-AMOX-500', 100, 0.50, 20, 'Pharmacy Cabinet'),
    ('Blood Pressure Cuffs', 'equipment', 'Reusable adult blood pressure cuffs', 'EQ-BP-CUFF', 15, 25.00, 3, 'Equipment Room'),
    ('Otoscopes', 'equipment', 'Diagnostic otoscopes', 'EQ-OTOSCOPE', 8, 150.00, 2, 'Equipment Room'),
    ('Stethoscopes', 'equipment', 'Adult stethoscopes', 'EQ-STETH', 10, 75.00, 2, 'Equipment Room')
) AS inventory_data(item_name, item_type, description, sku, quantity, unit_cost, minimum_quantity, location)
WHERE (f.name = 'Spritely Main Hospital' AND (item_name LIKE '%Gloves%' OR item_name LIKE '%Syringes%' OR item_name LIKE '%Gauze%' OR item_name LIKE '%Alcohol%' OR item_name LIKE '%Vaccine%')) OR
      (f.name = 'Spritely Community Clinic' AND (item_name LIKE '%Amoxicillin%' OR item_name LIKE '%Blood Pressure%')) OR
      (f.name = 'Spritely Pediatric Center' AND (item_name LIKE '%Otoscopes%' OR item_name LIKE '%Stethoscopes%'));

-- Insert Inventory Transactions
WITH inventory_data AS (
  SELECT id, name, location_id
  FROM public.inventory_items
),
provider_data AS (
  SELECT id, user_id, first_name || ' ' || last_name AS name
  FROM public.healthcare_providers
)
INSERT INTO public.inventory_transactions (
  id,
  item_id,
  transaction_type,
  quantity,
  performed_by,
  reason,
  metadata,
  created_at
)
SELECT
  uuid_generate_v4(),
  i.id as item_id,
  CASE
    WHEN i.name = 'Examination Gloves' THEN 'received'
    WHEN i.name = 'Syringes 5ml' THEN 'used'
    WHEN i.name = 'Gauze Pads' THEN 'used'
    WHEN i.name = 'Alcohol Swabs' THEN 'received'
    WHEN i.name = 'Influenza Vaccine' THEN 'used'
    WHEN i.name = 'Amoxicillin 500mg' THEN 'received'
    WHEN i.name = 'Blood Pressure Cuffs' THEN 'adjusted'
    WHEN i.name = 'Otoscopes' THEN 'received'
    WHEN i.name = 'Stethoscopes' THEN 'used'
  END as transaction_type,
  CASE
    WHEN i.name = 'Examination Gloves' THEN 500
    WHEN i.name = 'Syringes 5ml' THEN -25
    WHEN i.name = 'Gauze Pads' THEN -50
    WHEN i.name = 'Alcohol Swabs' THEN 1000
    WHEN i.name = 'Influenza Vaccine' THEN -10
    WHEN i.name = 'Amoxicillin 500mg' THEN 100
    WHEN i.name = 'Blood Pressure Cuffs' THEN 2
    WHEN i.name = 'Otoscopes' THEN 3
    WHEN i.name = 'Stethoscopes' THEN -1
  END as quantity,
  p.user_id as performed_by,
  CASE
    WHEN i.name = 'Examination Gloves' THEN 'Regular inventory restock'
    WHEN i.name = 'Syringes 5ml' THEN 'Used for vaccinations'
    WHEN i.name = 'Gauze Pads' THEN 'Used for wound care'
    WHEN i.name = 'Alcohol Swabs' THEN 'Regular inventory restock'
    WHEN i.name = 'Influenza Vaccine' THEN 'Administered to patients'
    WHEN i.name = 'Amoxicillin 500mg' THEN 'Regular pharmacy restock'
    WHEN i.name = 'Blood Pressure Cuffs' THEN 'Inventory count adjustment'
    WHEN i.name = 'Otoscopes' THEN 'New equipment received'
    WHEN i.name = 'Stethoscopes' THEN 'Assigned to new provider'
  END as reason,
  jsonb_build_object(
    'transaction_date', (NOW() - INTERVAL '1 week')::text
  ),
  NOW()
FROM inventory_data i
JOIN provider_data p ON
  CASE
    WHEN i.name IN ('Examination Gloves', 'Syringes 5ml', 'Gauze Pads') THEN p.name = 'Dr. Sarah'
    WHEN i.name IN ('Alcohol Swabs', 'Influenza Vaccine') THEN p.name = 'Dr. Michael'
    WHEN i.name IN ('Amoxicillin 500mg', 'Blood Pressure Cuffs') THEN p.name = 'Dr. James'
    WHEN i.name IN ('Otoscopes', 'Stethoscopes') THEN p.name = 'Dr. Robert'
  END;

COMMIT;
