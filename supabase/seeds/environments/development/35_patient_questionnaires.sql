-- Start transaction
BEGIN;

-- Create patient questionnaires for patients
DO $BODY$
DECLARE
  patient_record RECORD;
  questionnaire_count INTEGER;
  i INTEGER;
  
  questionnaire_types TEXT[] := ARRAY['health_history', 'symptom_assessment', 'medication_review', 'lifestyle_assessment', 'pain_scale', 'mental_health_screening'];
  
BEGIN
  -- Create questionnaires for random patients
  FOR patient_record IN
    SELECT p.id, p.first_name, p.last_name, p.organization_id
    FROM patients p
    WHERE random() < 0.5  -- 50% of patients have questionnaires
  LOOP
    -- Each patient gets 1-2 questionnaires
    questionnaire_count := 1 + floor(random() * 2)::int;
    
    FOR i IN 1..questionnaire_count LOOP
      INSERT INTO public.patient_questionnaires (
        id,
        patient_id,
        questionnaire_type,
        responses,
        completed_at,
        created_at,
        updated_at
      ) VALUES (
        uuid_generate_v4(),
        patient_record.id,
        questionnaire_types[1 + (random() * (array_length(questionnaire_types, 1) - 1))::int],
        CASE 
          WHEN random() < 0.3 THEN jsonb_build_object(
            'pain_level', floor(random() * 10)::int + 1,
            'pain_location', (ARRAY['head', 'back', 'chest', 'abdomen', 'joints'])[floor(random() * 5)::int + 1],
            'duration', (ARRAY['acute', 'chronic', 'intermittent'])[floor(random() * 3)::int + 1]
          )
          WHEN random() < 0.6 THEN jsonb_build_object(
            'allergies', (ARRAY['none', 'seasonal', 'food', 'medication'])[floor(random() * 4)::int + 1],
            'medications', (ARRAY['none', 'prescription', 'over_counter', 'supplements'])[floor(random() * 4)::int + 1],
            'previous_surgeries', random() < 0.3
          )
          ELSE jsonb_build_object(
            'smoking_status', (ARRAY['never', 'former', 'current'])[floor(random() * 3)::int + 1],
            'alcohol_use', (ARRAY['none', 'occasional', 'moderate', 'frequent'])[floor(random() * 4)::int + 1],
            'exercise_frequency', (ARRAY['never', 'weekly', 'daily'])[floor(random() * 3)::int + 1]
          )
        END,
        NOW() - make_interval(days => (random() * 180)::int),
        NOW() - make_interval(days => (random() * 200)::int),
        NOW() - make_interval(days => (random() * 30)::int)
      );
    END LOOP;
  END LOOP;
END
$BODY$;

COMMIT;
