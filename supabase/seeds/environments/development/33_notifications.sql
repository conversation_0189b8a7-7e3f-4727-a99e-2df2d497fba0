-- Start transaction
BEGIN;

-- Create notifications for users
DO $BODY$
DECLARE
  user_record RECORD;
  notif_count INTEGER;
  i INTEGER;
  
  notification_types TEXT[] := ARRAY['appointment_reminder', 'lab_result', 'prescription_update', 'medical_record_update', 'task_assignment', 'message', 'alert', 'system_update'];
  notification_titles TEXT[] := ARRAY[
    'Appointment Reminder', 'Lab Results Available', 'New Message Received',
    'Patient Alert', 'Medication Reminder', 'System Update', 'Insurance Update',
    'Test Results', 'Appointment Confirmed', 'Prescription Ready'
  ];
  notification_contents TEXT[] := ARRAY[
    'You have an upcoming appointment in 24 hours',
    'New lab results are available for review',
    'You have received a new secure message',
    'Patient alert requires your attention',
    'Medication refill is due for your patient',
    'System maintenance scheduled for tonight',
    'Insurance verification completed',
    'Test results are ready for patient review',
    'Your appointment has been confirmed',
    'Prescription is ready for pickup'
  ];

BEGIN
  -- Create notifications for healthcare providers and other users
  FOR user_record IN
    SELECT u.id as user_id
    FROM (
      SELECT DISTINCT u.id
      FROM auth.users u
      WHERE u.id IN (
        SELECT user_id FROM healthcare_providers
        UNION
        SELECT user_id FROM patients WHERE user_id IS NOT NULL
      )
    ) u
    ORDER BY RANDOM()
    LIMIT 50  -- Create notifications for 50 users
  LOOP
    -- Each user gets 2-6 notifications
    notif_count := 2 + floor(random() * 5)::int;
    
    FOR i IN 1..notif_count LOOP
      INSERT INTO public.notifications (
        id,
        organization_id,
        type,
        priority,
        status,
        sender_id,
        recipient_id,
        title,
        content,
        metadata,
        scheduled_for,
        sent_at,
        read_at,
        created_at,
        updated_at
      ) VALUES (
        uuid_generate_v4(),
        (SELECT o.id FROM organizations o ORDER BY RANDOM() LIMIT 1),
        notification_types[1 + (random() * (array_length(notification_types, 1) - 1))::int]::notification_type,
        CASE floor(random() * 3)::int
          WHEN 0 THEN 'low'
          WHEN 1 THEN 'medium'
          ELSE 'high'
        END::notification_priority,
        CASE floor(random() * 3)::int
          WHEN 0 THEN 'pending'
          WHEN 1 THEN 'sent'
          ELSE 'delivered'
        END::notification_status,
        (SELECT u.id FROM auth.users u ORDER BY RANDOM() LIMIT 1),
        user_record.user_id,
        notification_titles[1 + (random() * (array_length(notification_titles, 1) - 1))::int],
        notification_contents[1 + (random() * (array_length(notification_contents, 1) - 1))::int],
        '{"source": "system", "auto_generated": true}'::jsonb,
        NOW() - make_interval(days => (random() * 7)::int),
        NOW() - make_interval(days => (random() * 7)::int),
        CASE WHEN random() < 0.6 THEN NOW() - make_interval(hours => (floor(random() * 24)::int)) ELSE NULL END,
        NOW() - make_interval(days => (random() * 14)::int, hours => (random() * 24)::int),
        NOW()
      );
    END LOOP;
  END LOOP;
END
$BODY$;

COMMIT; 