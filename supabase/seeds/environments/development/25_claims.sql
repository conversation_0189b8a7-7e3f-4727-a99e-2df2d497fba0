-- Start transaction
BEGIN;

-- Generate comprehensive claims data for completed appointments
DO $$
DECLARE
  appointment_rec RECORD;
  insurance_rec UUID;
  service_date_val DATE;
  billing_codes_val JSONB;
  status_val TEXT;
  amount_val DECIMAL;
  submitted_at_val TIMESTAMP;
  processed_at_val TIMESTAMP;
  metadata_val JSONB;

  -- Common ICD-10 diagnosis codes
  diagnosis_codes TEXT[] := ARRAY[
    'I10',      -- Essential hypertension
    'E11.9',    -- Type 2 diabetes without complications
    'J45.9',    -- Asthma, unspecified
    'M79.3',    -- Panniculitis, unspecified
    'Z00.00',   -- Encounter for general adult medical examination
    'I25.10',   -- Atherosclerotic heart disease
    'J02.9',    -- Acute pharyngitis, unspecified
    'J18.9',    -- Pneumonia, unspecified organism
    'Z01.82',   -- Encounter for hearing examination
    'M25.50',   -- Pain in unspecified joint
    'R50.9',    -- Fever, unspecified
    'K21.9',    -- Gastro-esophageal reflux disease
    'F32.9',    -- Major depressive disorder, single episode
    'G43.909',  -- <PERSON><PERSON><PERSON>, unspecified
    'L30.9',    -- Dermatitis, unspecified
    'N39.0',    -- Urinary tract infection
    'R06.02',   -- Shortness of breath
    'R51',      -- Headache
    'M54.5',    -- Low back pain
    'Z23'       -- Encounter for immunization
  ];

  -- Common CPT procedure codes
  procedure_codes TEXT[] := ARRAY[
    '99213',    -- Office visit, established patient, low complexity
    '99214',    -- Office visit, established patient, moderate complexity
    '99215',    -- Office visit, established patient, high complexity
    '99203',    -- Office visit, new patient, low complexity
    '99204',    -- Office visit, new patient, moderate complexity
    '99205',    -- Office visit, new patient, high complexity
    '85025',    -- Blood count; complete (CBC)
    '80053',    -- Comprehensive metabolic panel
    '80061',    -- Lipid panel
    '93306',    -- Echocardiography
    '71045',    -- Chest X-ray
    '87880',    -- Strep test
    '95004',    -- Allergy skin test
    '90471',    -- Immunization administration
    '90715',    -- Tetanus, diphtheria toxoids vaccine
    '36415',    -- Collection of venous blood by venipuncture
    '81001',    -- Urinalysis
    '73060',    -- Knee X-ray
    '76700',    -- Abdominal ultrasound
    '93000'     -- Electrocardiogram
  ];

  claim_statuses TEXT[] := ARRAY['submitted', 'pending', 'processed', 'paid', 'denied', 'appealed'];
BEGIN
  -- Create claims for completed appointments (about 80% get claims)
  FOR appointment_rec IN
    SELECT
      a.id as appointment_id,
      a.patient_id,
      a.provider_id,
      a.organization_id,
      a.appointment_date,
      a.reason,
      a.duration_minutes,
      p.insurance_info
    FROM public.appointments a
    JOIN public.patients p ON a.patient_id = p.id
    WHERE a.status = 'completed'
    AND random() < 0.8  -- 80% of completed appointments get claims
  LOOP
    -- Find insurance provider
    SELECT ip.id INTO insurance_rec
    FROM public.insurance_providers ip
    WHERE ip.name = appointment_rec.insurance_info->>'provider'
    OR ip.name LIKE '%' || split_part(appointment_rec.insurance_info->>'provider', ' ', 1) || '%'
    ORDER BY random()
    LIMIT 1;

    -- If no matching insurance found, pick a random one
    IF insurance_rec IS NULL THEN
      SELECT ip.id INTO insurance_rec
      FROM public.insurance_providers ip
      ORDER BY random()
      LIMIT 1;
    END IF;

    service_date_val := appointment_rec.appointment_date::date;

    -- Generate realistic billing codes based on appointment reason
    CASE
      WHEN appointment_rec.reason LIKE '%physical%' OR appointment_rec.reason LIKE '%examination%' THEN
        billing_codes_val := jsonb_build_object(
          'diagnosis', ARRAY['Z00.00'],
          'procedures', ARRAY['99204', '85025', '80053']
        );
        amount_val := 350.00 + (random() * 150);

      WHEN appointment_rec.reason LIKE '%follow-up%' THEN
        billing_codes_val := jsonb_build_object(
          'diagnosis', ARRAY[diagnosis_codes[1 + (random() * (array_length(diagnosis_codes, 1) - 1))::int]],
          'procedures', ARRAY['99213']
        );
        amount_val := 150.00 + (random() * 100);

      WHEN appointment_rec.reason LIKE '%consultation%' THEN
        billing_codes_val := jsonb_build_object(
          'diagnosis', ARRAY[
            diagnosis_codes[1 + (random() * (array_length(diagnosis_codes, 1) - 1))::int],
            diagnosis_codes[1 + (random() * (array_length(diagnosis_codes, 1) - 1))::int]
          ],
          'procedures', ARRAY['99214', '93306']
        );
        amount_val := 450.00 + (random() * 300);

      WHEN appointment_rec.reason LIKE '%sick%' OR appointment_rec.reason LIKE '%cold%' OR appointment_rec.reason LIKE '%flu%' THEN
        billing_codes_val := jsonb_build_object(
          'diagnosis', ARRAY['J02.9', 'R50.9'],
          'procedures', ARRAY['99213', '87880']
        );
        amount_val := 180.00 + (random() * 70);

      WHEN appointment_rec.reason LIKE '%vaccination%' THEN
        billing_codes_val := jsonb_build_object(
          'diagnosis', ARRAY['Z23'],
          'procedures', ARRAY['90471', '90715']
        );
        amount_val := 75.00 + (random() * 25);

      WHEN appointment_rec.reason LIKE '%diabetes%' THEN
        billing_codes_val := jsonb_build_object(
          'diagnosis', ARRAY['E11.9'],
          'procedures', ARRAY['99214', '80053']
        );
        amount_val := 220.00 + (random() * 80);

      WHEN appointment_rec.reason LIKE '%cardiology%' OR appointment_rec.reason LIKE '%heart%' THEN
        billing_codes_val := jsonb_build_object(
          'diagnosis', ARRAY['I25.10', 'I10'],
          'procedures', ARRAY['99214', '93306', '93000']
        );
        amount_val := 650.00 + (random() * 350);

      WHEN appointment_rec.reason LIKE '%allergy%' THEN
        billing_codes_val := jsonb_build_object(
          'diagnosis', ARRAY['Z01.82'],
          'procedures', ARRAY['99213', '95004']
        );
        amount_val := 200.00 + (random() * 100);

      ELSE
        -- Generic appointment
        billing_codes_val := jsonb_build_object(
          'diagnosis', ARRAY[diagnosis_codes[1 + (random() * (array_length(diagnosis_codes, 1) - 1))::int]],
          'procedures', ARRAY[procedure_codes[1 + (random() * (array_length(procedure_codes, 1) - 1))::int]]
        );
        amount_val := 150.00 + (random() * 200);
    END CASE;

    -- Determine claim status and dates
    CASE floor(random() * 10)::int
      WHEN 0, 1, 2, 3, 4 THEN  -- 50% processed/paid
        status_val := CASE WHEN random() < 0.8 THEN 'paid' ELSE 'processed' END;
        submitted_at_val := service_date_val + INTERVAL '1 day' + (random() * 7)::int * INTERVAL '1 day';
        processed_at_val := submitted_at_val + INTERVAL '5 days' + (random() * 10)::int * INTERVAL '1 day';

      WHEN 5, 6, 7 THEN  -- 30% submitted/pending
        status_val := CASE WHEN random() < 0.6 THEN 'submitted' ELSE 'pending' END;
        submitted_at_val := service_date_val + INTERVAL '1 day' + (random() * 14)::int * INTERVAL '1 day';
        processed_at_val := NULL;

      WHEN 8 THEN  -- 10% denied
        status_val := 'denied';
        submitted_at_val := service_date_val + INTERVAL '1 day' + (random() * 7)::int * INTERVAL '1 day';
        processed_at_val := submitted_at_val + INTERVAL '7 days' + (random() * 14)::int * INTERVAL '1 day';

      ELSE  -- 10% appealed
        status_val := 'appealed';
        submitted_at_val := service_date_val + INTERVAL '1 day' + (random() * 7)::int * INTERVAL '1 day';
        processed_at_val := submitted_at_val + INTERVAL '14 days' + (random() * 21)::int * INTERVAL '1 day';
    END CASE;

    -- Generate metadata
    metadata_val := jsonb_build_object(
      'payment_method', 'insurance',
      'appointment_id', appointment_rec.appointment_id
    );

    IF status_val = 'paid' THEN
      metadata_val := metadata_val || jsonb_build_object(
        'copay', CASE WHEN random() < 0.7 THEN (10 + random() * 40)::decimal(5,2) ELSE 0 END,
        'insurance_payment', (amount_val * (0.7 + random() * 0.25))::decimal(8,2)
      );
    ELSIF status_val = 'denied' THEN
      metadata_val := metadata_val || jsonb_build_object(
        'denial_reason',
        CASE floor(random() * 4)::int
          WHEN 0 THEN 'Service not covered'
          WHEN 1 THEN 'Prior authorization required'
          WHEN 2 THEN 'Duplicate claim'
          ELSE 'Insufficient documentation'
        END
      );
    ELSIF status_val = 'appealed' THEN
      metadata_val := metadata_val || jsonb_build_object(
        'appeal_reason', 'Medical necessity',
        'appeal_date', processed_at_val
      );
    END IF;

    -- Insert the claim
    INSERT INTO public.claims (
      id,
      patient_id,
      provider_id,
      insurance_provider_id,
      service_date,
      billing_codes,
      status,
      amount,
      submitted_at,
      processed_at,
      metadata,
      created_at,
      updated_at
    ) VALUES (
      uuid_generate_v4(),
      appointment_rec.patient_id,
      appointment_rec.provider_id,
      insurance_rec,
      service_date_val,
      billing_codes_val,
      status_val,
      amount_val,
      submitted_at_val,
      processed_at_val,
      metadata_val,
      COALESCE(submitted_at_val, service_date_val + INTERVAL '1 day'),
      COALESCE(processed_at_val, submitted_at_val, service_date_val + INTERVAL '1 day')
    );
  END LOOP;
END
$$;

COMMIT;
