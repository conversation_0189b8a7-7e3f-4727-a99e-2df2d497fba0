-- Start transaction
BEGIN;

-- Insert Patients
WITH org_data AS (
  SELECT id, name FROM organizations
)
INSERT INTO public.patients (
  id,
  organization_id,
  first_name,
  last_name,
  date_of_birth,
  gender,
  phone,
  email,
  address,
  emergency_contact,
  insurance_info,
  medical_history,
  created_at,
  updated_at
)
SELECT
  uuid_generate_v4(),
  o.id,
  first_name,
  last_name,
  date_of_birth,
  gender,
  phone,
  email,
  jsonb_build_object(
    'street', street,
    'city', city,
    'state', state,
    'postal_code', postal_code,
    'country', 'USA'
  ),
  jsonb_build_object(
    'name', emergency_contact_name,
    'relationship', emergency_contact_relationship,
    'phone', emergency_contact_phone
  ),
  jsonb_build_object(
    'provider', insurance_provider,
    'policy_number', insurance_policy,
    'group_number', insurance_group
  ),
  jsonb_build_object(
    'allergies', allergies,
    'chronic_conditions', chronic_conditions,
    'current_medications', current_medications,
    'past_surgeries', past_surgeries
  ),
  NOW() - (random() * INTERVAL '365 days'),
  NOW() - (random() * INTERVAL '30 days')
FROM (
  VALUES
    -- Sample Hospital Patients
    ('Alice', '<PERSON>', '1975-05-15'::date, 'female'::gender, '************', '<EMAIL>',
     '123 Main St', 'Seattle', 'WA', '98101',
     'Robert Thompson', 'Spouse', '************',
     'Blue Cross', 'BC123456', 'G7890',
     ARRAY['Penicillin'], ARRAY['Hypertension'], ARRAY['Lisinopril 10mg'], ARRAY['Appendectomy 2010']),

    ('James', 'Wilson', '1968-09-23'::date, 'male'::gender, '************', '<EMAIL>',
     '456 Oak Ave', 'Seattle', 'WA', '98102',
     'Mary Wilson', 'Spouse', '************',
     'Aetna', 'AE789012', 'G3456',
     ARRAY['Sulfa drugs'], ARRAY['Coronary artery disease'], ARRAY['Atorvastatin 20mg', 'Aspirin 81mg'], ARRAY['CABG 2015']),

    ('Sophia', 'Martinez', '2015-03-10'::date, 'female'::gender, '************', '<EMAIL>',
     '789 Pine Blvd', 'Seattle', 'WA', '98103',
     'Elena Martinez', 'Mother', '************',
     'United Healthcare', 'UH345678', 'G9012',
     ARRAY['None'], ARRAY['Asthma'], ARRAY['Albuterol inhaler'], ARRAY['None']),

    -- Sample Clinic Patients
    ('David', 'Anderson', '1982-11-30'::date, 'male'::gender, '************', '<EMAIL>',
     '101 Cedar St', 'Bellevue', 'WA', '98004',
     'Sarah Anderson', 'Spouse', '************',
     'Cigna', 'CI901234', 'G5678',
     ARRAY['Latex'], ARRAY['Type 2 diabetes'], ARRAY['Metformin 1000mg'], ARRAY['None']),

    ('Emma', 'Garcia', '2010-07-20'::date, 'female'::gender, '************', '<EMAIL>',
     '202 Maple Dr', 'Bellevue', 'WA', '98005',
     'Carlos Garcia', 'Father', '************',
     'Kaiser Permanente', 'KP567890', 'G1234',
     ARRAY['Peanuts'], ARRAY['None'], ARRAY['None'], ARRAY['Tonsillectomy 2018']),

    -- Dev Hospital Patients
    ('Michael', 'Brown', '1970-04-12'::date, 'male'::gender, '************', '<EMAIL>',
     '303 Birch Ln', 'Portland', 'OR', '97201',
     'Jennifer Brown', 'Spouse', '************',
     'Blue Shield', 'BS234567', 'G8901',
     ARRAY['Codeine'], ARRAY['GERD'], ARRAY['Omeprazole 20mg'], ARRAY['Cholecystectomy 2012']),

    ('Olivia', 'Davis', '1990-01-25'::date, 'female'::gender, '************', '<EMAIL>',
     '404 Spruce Ct', 'Portland', 'OR', '97202',
     'William Davis', 'Brother', '************',
     'Medicare', 'ME890123', 'G4567',
     ARRAY['None'], ARRAY['Migraine'], ARRAY['Sumatriptan 50mg'], ARRAY['None']),

    -- Dev Clinic Patients
    ('Ethan', 'Taylor', '1985-08-05'::date, 'male'::gender, '************', '<EMAIL>',
     '505 Redwood Rd', 'Vancouver', 'WA', '98660',
     'Natalie Taylor', 'Spouse', '************',
     'Humana', 'HU456789', 'G0123',
     ARRAY['Ibuprofen'], ARRAY['Anxiety'], ARRAY['Sertraline 50mg'], ARRAY['None']),

    ('Ava', 'Thomas', '2018-12-15'::date, 'female'::gender, '************', '<EMAIL>',
     '606 Sequoia Way', 'Vancouver', 'WA', '98661',
     'Daniel Thomas', 'Father', '************',
     'Tricare', 'TR012345', 'G6789',
     ARRAY['Amoxicillin'], ARRAY['None'], ARRAY['None'], ARRAY['None']),

    -- Dev Practice Patients
    ('Noah', 'Harris', '1978-06-30'::date, 'male'::gender, '************', '<EMAIL>',
     '707 Walnut Ave', 'Salem', 'OR', '97301',
     'Emily Harris', 'Spouse', '************',
     'Medicaid', 'MD678901', 'G2345',
     ARRAY['None'], ARRAY['Hypertension', 'Hyperlipidemia'], ARRAY['Lisinopril 20mg', 'Simvastatin 40mg'], ARRAY['None']),

    ('Isabella', 'Clark', '2005-10-08'::date, 'female'::gender, '************', '<EMAIL>',
     '808 Elm St', 'Salem', 'OR', '97302',
     'Jessica Clark', 'Mother', '************',
     'Anthem', 'AN234567', 'G8901',
     ARRAY['None'], ARRAY['Asthma'], ARRAY['Fluticasone inhaler'], ARRAY['None'])
) AS patient_data(
  first_name, last_name, date_of_birth, gender, phone, email,
  street, city, state, postal_code,
  emergency_contact_name, emergency_contact_relationship, emergency_contact_phone,
  insurance_provider, insurance_policy, insurance_group,
  allergies, chronic_conditions, current_medications, past_surgeries
)
JOIN org_data o ON
  CASE
    WHEN patient_data.city = 'Seattle' THEN o.name = 'Sample Hospital'
    WHEN patient_data.city = 'Bellevue' THEN o.name = 'Sample Clinic'
    WHEN patient_data.city = 'Portland' THEN o.name = 'Dev Hospital'
    WHEN patient_data.city = 'Vancouver' THEN o.name = 'Dev Clinic'
    WHEN patient_data.city = 'Salem' THEN o.name = 'Dev Practice'
    ELSE FALSE
  END;

-- Add more random patients to each organization
INSERT INTO public.patients (
  id,
  organization_id,
  first_name,
  last_name,
  date_of_birth,
  gender,
  phone,
  email,
  address,
  emergency_contact,
  insurance_info,
  medical_history,
  created_at,
  updated_at
)
SELECT
  uuid_generate_v4(),
  o.id,
  (ARRAY['John', 'Jane', 'Robert', 'Mary', 'William', 'Elizabeth', 'Richard', 'Susan', 'Joseph', 'Margaret',
         'Thomas', 'Jennifer', 'Charles', 'Lisa', 'Christopher', 'Nancy', 'Daniel', 'Karen', 'Matthew', 'Betty',
         'Anthony', 'Dorothy', 'Mark', 'Sandra', 'Donald', 'Ashley', 'Steven', 'Kimberly', 'Paul', 'Emily',
         'Andrew', 'Donna', 'Joshua', 'Michelle', 'Kenneth', 'Carol', 'Kevin', 'Amanda', 'Brian', 'Melissa'])[floor(random() * 40 + 1)],
  (ARRAY['Smith', 'Johnson', 'Williams', 'Jones', 'Brown', 'Davis', 'Miller', 'Wilson', 'Moore', 'Taylor',
         'Anderson', 'Thomas', 'Jackson', 'White', 'Harris', 'Martin', 'Thompson', 'Garcia', 'Martinez', 'Robinson',
         'Clark', 'Rodriguez', 'Lewis', 'Lee', 'Walker', 'Hall', 'Allen', 'Young', 'Hernandez', 'King',
         'Wright', 'Lopez', 'Hill', 'Scott', 'Green', 'Adams', 'Baker', 'Gonzalez', 'Nelson', 'Carter'])[floor(random() * 40 + 1)],
  (NOW() - (random() * INTERVAL '80 years'))::date,
  (ARRAY['male', 'female'])[floor(random() * 2 + 1)]::gender,
  '555-' || lpad(floor(random() * 900 + 100)::text, 3, '0') || '-' || lpad(floor(random() * 9000 + 1000)::text, 4, '0'),
  'patient' || floor(random() * 1000)::text || '@example.com',
  jsonb_build_object(
    'street', floor(random() * 999 + 1)::text || ' ' ||
              (ARRAY['Main', 'Oak', 'Pine', 'Maple', 'Cedar', 'Elm', 'Walnut', 'Birch', 'Spruce', 'Redwood'])[floor(random() * 10 + 1)] || ' ' ||
              (ARRAY['St', 'Ave', 'Blvd', 'Dr', 'Ln', 'Rd', 'Way', 'Ct', 'Pl', 'Ter'])[floor(random() * 10 + 1)],
    'city', (ARRAY['Seattle', 'Bellevue', 'Portland', 'Vancouver', 'Salem'])[floor(random() * 5 + 1)],
    'state', (ARRAY['WA', 'OR'])[floor(random() * 2 + 1)],
    'postal_code', (ARRAY['98101', '98102', '98103', '98004', '98005', '97201', '97202', '98660', '98661', '97301', '97302'])[floor(random() * 11 + 1)],
    'country', 'USA'
  ),
  jsonb_build_object(
    'name', (ARRAY['John', 'Jane', 'Robert', 'Mary', 'William', 'Elizabeth'])[floor(random() * 6 + 1)] || ' ' ||
            (ARRAY['Smith', 'Johnson', 'Williams', 'Jones', 'Brown', 'Davis'])[floor(random() * 6 + 1)],
    'relationship', (ARRAY['Spouse', 'Parent', 'Child', 'Sibling', 'Friend'])[floor(random() * 5 + 1)],
    'phone', '555-' || lpad(floor(random() * 900 + 100)::text, 3, '0') || '-' || lpad(floor(random() * 9000 + 1000)::text, 4, '0')
  ),
  jsonb_build_object(
    'provider', (ARRAY['Blue Cross', 'Aetna', 'United Healthcare', 'Cigna', 'Kaiser Permanente', 'Blue Shield', 'Medicare', 'Medicaid', 'Humana', 'Anthem'])[floor(random() * 10 + 1)],
    'policy_number', upper(substring(md5(random()::text) from 1 for 8)),
    'group_number', 'G' || lpad(floor(random() * 9000 + 1000)::text, 4, '0')
  ),
  jsonb_build_object(
    'allergies', (ARRAY[
      '{}',
      '{"Penicillin"}',
      '{"Sulfa drugs"}',
      '{"Latex"}',
      '{"Peanuts"}',
      '{"Shellfish"}',
      '{"Penicillin", "Sulfa drugs"}'
    ])[floor(random() * 7 + 1)]::text[],
    'chronic_conditions', (ARRAY[
      '{}',
      '{"Hypertension"}',
      '{"Diabetes"}',
      '{"Asthma"}',
      '{"GERD"}',
      '{"Migraine"}',
      '{"Hypertension", "Diabetes"}'
    ])[floor(random() * 7 + 1)]::text[],
    'current_medications', (ARRAY[
      '{}',
      '{"Lisinopril 10mg"}',
      '{"Metformin 1000mg"}',
      '{"Albuterol inhaler"}',
      '{"Omeprazole 20mg"}',
      '{"Sumatriptan 50mg"}',
      '{"Lisinopril 10mg", "Metformin 1000mg"}'
    ])[floor(random() * 7 + 1)]::text[],
    'past_surgeries', (ARRAY[
      '{}',
      '{"Appendectomy"}',
      '{"Cholecystectomy"}',
      '{"Tonsillectomy"}',
      '{"Hernia repair"}'
    ])[floor(random() * 5 + 1)]::text[]
  ),
  NOW() - (random() * INTERVAL '365 days'),
  NOW() - (random() * INTERVAL '30 days')
FROM
  organizations o,
  generate_series(1, 10) AS num;

COMMIT;
