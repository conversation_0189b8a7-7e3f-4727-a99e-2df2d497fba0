-- Start transaction
BEGIN;

-- Insert Organization Invites
WITH org_data AS (
  SELECT id, name FROM public.organizations
),
admin_user AS (
  SELECT id FROM auth.users WHERE email = '<EMAIL>' LIMIT 1
)
INSERT INTO public.organization_invites (
  id,
  organization_id,
  email,
  role,
  status,
  invited_by,
  expires_at,
  created_at,
  updated_at
)
SELECT
  uuid_generate_v4(),
  o.id,
  'invite' || ROW_NUMBER() OVER () || '@example.com',
  (CASE
    WHEN ROW_NUMBER() OVER () = 1 THEN 'physician'
    WHEN ROW_NUMBER() OVER () = 2 THEN 'registered_nurse'
    WHEN ROW_NUMBER() OVER () = 3 THEN 'front_desk'
    WHEN ROW_NUMBER() OVER () = 4 THEN 'billing_staff'
    WHEN ROW_NUMBER() OVER () = 5 THEN 'lab_technician'
    ELSE 'physician'
  END)::user_role,
  'pending',
  (SELECT id FROM admin_user),
  NOW() + INTERVAL '7 days',
  NOW(),
  NOW()
FROM org_data o
CROSS JOIN (SELECT generate_series(1, 5)) AS nums(n);

COMMIT;
