-- Start transaction
BEGIN;

-- Create medical orders for patients
DO $BODY$
DECLARE
  appointment_record RECORD;
  order_count INTEGER;
  i INTEGER;
  
  order_types TEXT[] := ARRAY['lab', 'imaging', 'medication', 'referral', 'procedure'];
  order_tests TEXT[] := ARRAY[
    'Complete Blood Count', 'Basic Metabolic Panel', 'Chest X-Ray', 'MRI Brain',
    'Lisinopril 10mg', 'Amoxicillin 500mg', 'Cardiology Consultation', 
    'Physical Therapy', 'Colonoscopy', 'ECG', 'Ultrasound Abdomen'
  ];
  order_instructions TEXT[] := ARRAY[
    'Fasting required - NPO after midnight',
    'No special preparation needed',
    'Remove all metal objects before procedure',
    'Take with food to reduce stomach upset',
    'Follow up in 2 weeks',
    'Complete full course as prescribed',
    'Schedule within 30 days',
    'Bring comfortable clothing',
    'Pre-procedure consultation required'
  ];
  priorities TEXT[] := ARRAY['routine', 'urgent', 'stat', 'emergency'];
  statuses TEXT[] := ARRAY['pending', 'in_progress', 'completed', 'cancelled'];
  
BEGIN
  -- Create orders for appointments
  FOR appointment_record IN
    SELECT a.id as appointment_id, a.patient_id, a.provider_id, a.appointment_date
    FROM appointments a
    WHERE a.status IN ('completed', 'in_progress')
    ORDER BY RANDOM()
    LIMIT 200  -- Create orders for 200 appointments
  LOOP
    -- Each appointment gets 1-3 orders
    order_count := 1 + floor(random() * 3)::int;
    
    FOR i IN 1..order_count LOOP
      INSERT INTO public.orders (
        id,
        patient_id,
        ordering_provider_id,
        order_type,
        status,
        priority,
        order_details,
        diagnosis_codes,
        notes,
        ordered_at,
        scheduled_date,
        completed_at,
        metadata,
        created_at,
        updated_at
      ) VALUES (
        uuid_generate_v4(),
        appointment_record.patient_id,
        appointment_record.provider_id,
        order_types[1 + (random() * (array_length(order_types, 1) - 1))::int]::order_type,
        statuses[1 + (random() * (array_length(statuses, 1) - 1))::int]::order_status,
        priorities[1 + (random() * (array_length(priorities, 1) - 1))::int]::order_priority,
        jsonb_build_object(
          'test', order_tests[1 + (random() * (array_length(order_tests, 1) - 1))::int],
          'instructions', order_instructions[1 + (random() * (array_length(order_instructions, 1) - 1))::int]
        ),
        jsonb_build_object('codes', ARRAY['Z01.82', 'I10', 'E11.9']),
        CASE WHEN random() < 0.4 THEN 'Patient notified of results' ELSE NULL END,
        appointment_record.appointment_date,
        CASE WHEN random() < 0.8 THEN appointment_record.appointment_date + make_interval(days => (1 + floor(random() * 14)::int)) ELSE NULL END,
        CASE WHEN random() < 0.6 THEN appointment_record.appointment_date + make_interval(days => (1 + floor(random() * 30)::int)) ELSE NULL END,
        '{"auto_generated": true}'::jsonb,
        appointment_record.appointment_date,
        NOW()
      );
    END LOOP;
  END LOOP;
END
$BODY$;

COMMIT;
