-- Start transaction
BEGIN;

-- Insert Tasks
DO $$
DECLARE
  provider_rec RECORD;
  department_rec RECORD;
  patient_rec RECORD;
  admin_user_id UUID;
  task_title TEXT;
  task_priority task_priority;
  task_status task_status;
  due_date TIMESTAMP;
  related_type TEXT;
  related_id UUID;
BEGIN
  -- Get admin user ID
  SELECT id INTO admin_user_id FROM auth.users WHERE email = '<EMAIL>' LIMIT 1;

  -- Create tasks for each provider
  FOR provider_rec IN
    SELECT hp.id, hp.user_id, hp.first_name || ' ' || hp.last_name AS name, hp.organization_id
    FROM public.healthcare_providers hp
  LOOP
    -- Find a department in the same organization
    SELECT d.id
    INTO department_rec
    FROM public.departments d
    JOIN public.locations l ON d.location_id = l.id
    WHERE l.organization_id = provider_rec.organization_id
    ORDER BY random()
    LIMIT 1;

    -- Skip if we couldn't find a department
    CONTINUE WHEN department_rec IS NULL;

    -- Find a patient in the same organization
    SELECT p.id, p.first_name || ' ' || p.last_name AS name
    INTO patient_rec
    FROM public.patients p
    WHERE p.organization_id = provider_rec.organization_id
    ORDER BY random()
    LIMIT 1;

    -- Create 1-5 tasks for each provider
    FOR i IN 1..floor(random() * 5) + 1 LOOP
      -- Set task title based on random selection
      CASE floor(random() * 10)::integer
        WHEN 0 THEN task_title := 'Review lab results';
        WHEN 1 THEN task_title := 'Schedule follow-up appointment';
        WHEN 2 THEN task_title := 'Update patient chart';
        WHEN 3 THEN task_title := 'Call patient about medication';
        WHEN 4 THEN task_title := 'Complete vaccination record';
        WHEN 5 THEN task_title := 'Prepare for surgery';
        WHEN 6 THEN task_title := 'Order medical supplies';
        WHEN 7 THEN task_title := 'Review insurance claim';
        WHEN 8 THEN task_title := 'Update treatment plan';
        ELSE task_title := 'Coordinate with specialist';
      END CASE;

      -- Set priority based on random selection
      CASE floor(random() * 4)::integer
        WHEN 0 THEN task_priority := 'low';
        WHEN 1 THEN task_priority := 'medium';
        WHEN 2 THEN task_priority := 'high';
        ELSE task_priority := 'urgent';
      END CASE;

      -- Set status based on random selection
      CASE floor(random() * 4)::integer
        WHEN 0 THEN task_status := 'pending';
        WHEN 1 THEN task_status := 'in_progress';
        WHEN 2 THEN task_status := 'completed';
        ELSE task_status := 'cancelled';
      END CASE;

      -- Set due date
      due_date := NOW() + (random() * 14 - 7)::integer * INTERVAL '1 day';

      -- Set related entity type and ID
      CASE floor(random() * 5)::integer
        WHEN 0 THEN related_type := 'patient';
        WHEN 1 THEN related_type := 'appointment';
        WHEN 2 THEN related_type := 'medical_record';
        WHEN 3 THEN related_type := 'medication';
        ELSE related_type := 'immunization';
      END CASE;
      related_id := uuid_generate_v4();

      -- Insert the task
      INSERT INTO public.tasks (
        id,
        organization_id,
        department_id,
        title,
        description,
        priority,
        status,
        assigned_to,
        assigned_by,
        due_date,
        related_to,
        created_at,
        updated_at
      ) VALUES (
        uuid_generate_v4(),
        provider_rec.organization_id,
        department_rec.id,
        task_title,
        'Task: ' || task_title || CASE WHEN patient_rec IS NOT NULL THEN ' for patient ' || patient_rec.name ELSE '' END || '. Assigned to ' || provider_rec.name,
        task_priority,
        task_status,
        provider_rec.user_id,
        CASE WHEN random() < 0.3 THEN admin_user_id ELSE provider_rec.user_id END,
        due_date,
        jsonb_build_object('type', related_type, 'id', related_id),
        NOW() - (random() * 30)::integer * INTERVAL '1 day',
        NOW() - (random() * 15)::integer * INTERVAL '1 day'
      );
    END LOOP;

    -- Create an urgent task due today for some providers (1 in 3 chance)
    IF random() < 0.3 THEN
      INSERT INTO public.tasks (
        id,
        organization_id,
        department_id,
        title,
        description,
        priority,
        status,
        assigned_to,
        assigned_by,
        due_date,
        related_to,
        created_at,
        updated_at
      ) VALUES (
        uuid_generate_v4(),
        provider_rec.organization_id,
        department_rec.id,
        'Urgent: Review patient case',
        'This task needs immediate attention. Due today.',
        'urgent',
        'pending',
        provider_rec.user_id,
        admin_user_id,
        date_trunc('day', NOW()) + interval '17 hours', -- Due at 5pm today
        jsonb_build_object('type', 'patient', 'id', uuid_generate_v4()),
        NOW() - interval '1 day',
        NOW() - interval '1 day'
      );
    END IF;
  END LOOP;
END
$$;

-- Insert Task Comments (Only if tasks were created successfully)
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM public.tasks LIMIT 1) THEN
    INSERT INTO public.task_comments (
      id,
      task_id,
      user_id,
      content,
      created_at,
      updated_at
    )
    SELECT
      uuid_generate_v4(),
      t.id,
      (SELECT id FROM auth.users WHERE email = '<EMAIL>' LIMIT 1),
      'This is a comment on task: ' || t.title,
      NOW(),
      NOW()
    FROM (SELECT id, title FROM public.tasks LIMIT 5) t;
  END IF;
END $$;

-- Insert Task Watchers (Only if tasks were created successfully)
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM public.tasks LIMIT 1) THEN
    INSERT INTO public.task_watchers (
      task_id,
      user_id
    )
    SELECT
      t.id,
      u.id
    FROM (SELECT id FROM public.tasks LIMIT 5) t
    CROSS JOIN (SELECT id FROM auth.users LIMIT 5) u
    LIMIT 5;
  END IF;
END $$;

COMMIT;
