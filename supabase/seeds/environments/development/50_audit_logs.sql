-- Start transaction
BEGIN;

-- Create audit logs for patients
DO $BODY$
DECLARE
  patient_record RECORD;
  log_count INTEGER;
  i INTEGER;
  
  actions TEXT[] := ARRAY['view', 'update', 'create', 'delete'];
  sections TEXT[] := ARRAY['medical_history', 'medications', 'lab_results', 'clinical_notes', 'vital_signs', 'appointments', 'documents'];
  
BEGIN
  -- Create audit logs for random patients
  FOR patient_record IN
    SELECT p.id as patient_id, p.organization_id
    FROM patients p
    ORDER BY RANDOM()
    LIMIT 80  -- Create audit logs for 80 patients
  LOOP
    -- Each patient gets 2-5 audit log entries
    log_count := 2 + floor(random() * 4)::int;
    
    FOR i IN 1..log_count LOOP
      INSERT INTO public.audit_logs (
        id,
        table_name,
        record_id,
        action,
        old_data,
        new_data,
        changed_by,
        timestamp
      ) VALUES (
        uuid_generate_v4(),
        'patients',
        patient_record.patient_id,
        actions[1 + (random() * (array_length(actions, 1) - 1))::int],
        CASE WHEN random() < 0.3 THEN jsonb_build_object('previous_value', 'old_data_' || i) ELSE NULL END,
        jsonb_build_object(
          'section', sections[1 + (random() * (array_length(sections, 1) - 1))::int],
          'details', 'Automated audit log entry ' || i,
          'timestamp', NOW()
        ),
        (SELECT user_id FROM healthcare_providers WHERE organization_id = patient_record.organization_id ORDER BY RANDOM() LIMIT 1),
        NOW() - make_interval(days => (random() * 30)::int, hours => (random() * 24)::int)
      );
    END LOOP;
  END LOOP;
END
$BODY$;

COMMIT;
