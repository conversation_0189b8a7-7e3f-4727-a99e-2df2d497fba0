-- Start transaction
BEGIN;

-- Create patient education records
DO $BODY$
DECLARE
  patient_record RECORD;
  education_count INTEGER;
  i INTEGER;
  
  education_topics TEXT[] := ARRAY[
    'Understanding Hypertension', 'Heart Health Guidelines', 'Asthma Action Plan',
    'Managing Seasonal Allergies', 'Childhood Vaccination Schedule', 'Diabetes Management',
    'Nutrition Guidelines', 'Exercise and Wellness', 'Medication Safety',
    'Preventive Care', 'Mental Health Resources', 'Smoking Cessation'
  ];
  
  education_notes TEXT[] := ARRAY[
    'Patient reports better understanding of condition management',
    'Patient reviewing health materials provided',
    'Patient implementing action plan successfully',
    'Materials provided at last visit',
    'Patient reviewing information with family',
    'Follow-up education scheduled',
    'Patient demonstrates good understanding',
    'Additional resources provided'
  ];
  
BEGIN
  -- Create education records for random patients
  FOR patient_record IN
    SELECT p.id as patient_id, p.organization_id
    FROM patients p
    ORDER BY RANDOM()
    LIMIT 80  -- Create education records for 80 patients
  LOOP
    -- Each patient gets 1-3 education records
    education_count := 1 + floor(random() * 3)::int;
    
    FOR i IN 1..education_count LOOP
      INSERT INTO public.patient_education_records (
        id,
        patient_id,
        material_id,
        provider_id,
        provided_date,
        notes,
        metadata,
        created_at,
        updated_at
      ) VALUES (
        uuid_generate_v4(),
        patient_record.patient_id,
        (SELECT id FROM education_materials ORDER BY RANDOM() LIMIT 1),
        (SELECT id FROM healthcare_providers WHERE organization_id = patient_record.organization_id ORDER BY RANDOM() LIMIT 1),
        NOW() - make_interval(days => (random() * 90)::int),
        education_notes[1 + (random() * (array_length(education_notes, 1) - 1))::int],
        jsonb_build_object(
          'status', CASE WHEN random() < 0.6 THEN 'completed' WHEN random() < 0.8 THEN 'in_progress' ELSE 'not_started' END,
          'completion_date', CASE WHEN random() < 0.6 THEN (NOW() - make_interval(days => (random() * 30)::int))::text ELSE NULL END
        ),
        NOW() - make_interval(days => (random() * 30)::int),
        NOW()
      );
    END LOOP;
  END LOOP;
END
$BODY$;

COMMIT;
