-- Start transaction
BEGIN;

-- Create workflow instances for patients
DO $BODY$
DECLARE
  patient_record RECORD;
  workflow_record RECORD;
  instance_count INTEGER;
  i INTEGER;
  current_step_val INTEGER;
  status_val workflow_status;
  started_at_val TIMESTAMP;
  completed_at_val TIMESTAMP;
  
BEGIN
  -- Create workflow instances for random patients
  FOR patient_record IN
    SELECT p.id as patient_id, p.organization_id
    FROM patients p
    ORDER BY RANDOM()
    LIMIT 100  -- Create workflow instances for 100 patients
  LOOP
    -- Each patient gets 1-3 workflow instances
    instance_count := 1 + floor(random() * 3)::int;
    
    FOR i IN 1..instance_count LOOP
      -- Get a random workflow
      SELECT id, name INTO workflow_record
      FROM workflows
      ORDER BY RANDOM()
      LIMIT 1;
      
      -- Determine workflow progress
      current_step_val := 1 + floor(random() * 5)::int;
      started_at_val := NOW() - make_interval(days => (random() * 180)::int);
      
      -- Set status based on step progress
      IF random() < 0.3 THEN
        status_val := 'completed'::workflow_status;
        completed_at_val := started_at_val + make_interval(days => (1 + random() * 30)::int);
      ELSIF random() < 0.6 THEN
        status_val := 'in_progress'::workflow_status;
        completed_at_val := NULL;
      ELSE
        status_val := 'pending'::workflow_status;
        completed_at_val := NULL;
      END IF;
      
      INSERT INTO public.workflow_instances (
        id,
        workflow_id,
        status,
        current_step,
        context,
        started_at,
        completed_at,
        created_at,
        updated_at
      ) VALUES (
        uuid_generate_v4(),
        workflow_record.id,
        status_val,
        current_step_val,
        jsonb_build_object(
          'patient_id', patient_record.patient_id,
          'organization_id', patient_record.organization_id,
          'workflow_name', workflow_record.name,
          'priority', CASE WHEN random() < 0.2 THEN 'high' WHEN random() < 0.6 THEN 'medium' ELSE 'normal' END
        ),
        started_at_val,
        completed_at_val,
        started_at_val,
        CASE WHEN completed_at_val IS NOT NULL THEN completed_at_val ELSE NOW() END
      );
    END LOOP;
  END LOOP;
END
$BODY$;

-- Create workflow logs for workflow instances
DO $BODY$
DECLARE
  instance_record RECORD;
  step_number INTEGER;
  step_names TEXT[] := ARRAY['Initiation', 'Review', 'Processing', 'Validation', 'Completion'];
  log_messages TEXT[] := ARRAY[
    'Workflow step initiated',
    'Step in progress',
    'Step completed successfully',
    'Step requires attention',
    'Step reviewed and approved'
  ];
  
BEGIN
  -- Create logs for each workflow instance
  FOR instance_record IN
    SELECT wi.id as instance_id, wi.current_step, wi.status, wi.started_at
    FROM workflow_instances wi
  LOOP
    -- Create logs for completed steps
    FOR step_number IN 1..instance_record.current_step LOOP
      INSERT INTO public.workflow_logs (
        id,
        workflow_instance_id,
        step_number,
        step_name,
        status,
        message,
        details,
        created_at
      ) VALUES (
        uuid_generate_v4(),
        instance_record.instance_id,
        step_number,
        step_names[LEAST(step_number, array_length(step_names, 1))],
        CASE 
          WHEN step_number < instance_record.current_step THEN 'completed'::workflow_status
          WHEN step_number = instance_record.current_step AND instance_record.status = 'completed' THEN 'completed'::workflow_status
          WHEN step_number = instance_record.current_step THEN instance_record.status
          ELSE 'pending'::workflow_status
        END,
        log_messages[1 + (random() * (array_length(log_messages, 1) - 1))::int],
        jsonb_build_object(
          'step_completed_at', instance_record.started_at + make_interval(days => step_number),
          'automated', random() < 0.6
        ),
        instance_record.started_at + make_interval(days => step_number)
      );
    END LOOP;
  END LOOP;
END
$BODY$;

COMMIT;
