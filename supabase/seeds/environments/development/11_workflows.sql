-- Start transaction
BEGIN;

-- Insert Workflows using CTEs
WITH workflow_data (name, id) AS (
  VALUES
    ('New Patient Registration', uuid_generate_v4()),
    ('Annual Physical Exam', uuid_generate_v4()),
    ('Prescription Refill', uuid_generate_v4()),
    ('Lab Results Review', uuid_generate_v4()),
    ('Specialist Referral', uuid_generate_v4())
),
org_data AS (
  SELECT id, name FROM public.organizations
  WHERE name = 'Spritely Medical Center'
)
INSERT INTO public.workflows (
  id,
  organization_id,
  name,
  type,
  description,
  trigger_type,
  trigger_config,
  steps,
  enabled,
  created_at,
  updated_at
)
SELECT
  w.id,
  o.id as organization_id,
  w.name,
  CASE w.name
    WHEN 'New Patient Registration' THEN 'document_review'::workflow_type
    WHEN 'Annual Physical Exam' THEN 'patient_followup'::workflow_type
    WHEN 'Prescription Refill' THEN 'prescription_renewal'::workflow_type
    WHEN 'Lab Results Review' THEN 'lab_result_notification'::workflow_type
    WHEN 'Specialist Referral' THEN 'referral_management'::workflow_type
  END as type,
  CASE w.name
    WHEN 'New Patient Registration' THEN 'Workflow for registering new patients'
    WHEN 'Annual Physical Exam' THEN 'Standard annual physical examination workflow'
    WHEN 'Prescription Refill' THEN 'Process for handling prescription refill requests'
    WHEN 'Lab Results Review' THEN 'Workflow for reviewing and communicating lab results'
    WHEN 'Specialist Referral' THEN 'Process for referring patients to specialists'
  END as description,
  'manual'::workflow_trigger as trigger_type,
  '{}'::jsonb as trigger_config,
  CASE w.name
    WHEN 'New Patient Registration' THEN ARRAY[
      '{"step": 1, "name": "Patient Information", "type": "form", "required": true}'::jsonb,
      '{"step": 2, "name": "Insurance Verification", "type": "verification", "required": true}'::jsonb,
      '{"step": 3, "name": "Medical History", "type": "form", "required": true}'::jsonb,
      '{"step": 4, "name": "Consent Forms", "type": "document", "required": true}'::jsonb,
      '{"step": 5, "name": "Schedule Initial Visit", "type": "appointment", "required": false}'::jsonb
    ]
    WHEN 'Annual Physical Exam' THEN ARRAY[
      '{"step": 1, "name": "Vitals", "type": "measurement", "required": true}'::jsonb,
      '{"step": 2, "name": "Medical History Review", "type": "review", "required": true}'::jsonb,
      '{"step": 3, "name": "Physical Examination", "type": "examination", "required": true}'::jsonb,
      '{"step": 4, "name": "Lab Orders", "type": "order", "required": false}'::jsonb,
      '{"step": 5, "name": "Care Plan", "type": "plan", "required": true}'::jsonb
    ]
    WHEN 'Prescription Refill' THEN ARRAY[
      '{"step": 1, "name": "Medication Review", "type": "review", "required": true}'::jsonb,
      '{"step": 2, "name": "Patient Assessment", "type": "assessment", "required": true}'::jsonb,
      '{"step": 3, "name": "Prescription Authorization", "type": "authorization", "required": true}'::jsonb,
      '{"step": 4, "name": "Pharmacy Notification", "type": "notification", "required": true}'::jsonb
    ]
    WHEN 'Lab Results Review' THEN ARRAY[
      '{"step": 1, "name": "Results Receipt", "type": "receipt", "required": true}'::jsonb,
      '{"step": 2, "name": "Provider Review", "type": "review", "required": true}'::jsonb,
      '{"step": 3, "name": "Patient Notification", "type": "notification", "required": true}'::jsonb,
      '{"step": 4, "name": "Follow-up Scheduling", "type": "appointment", "required": false}'::jsonb
    ]
    WHEN 'Specialist Referral' THEN ARRAY[
      '{"step": 1, "name": "Referral Request", "type": "request", "required": true}'::jsonb,
      '{"step": 2, "name": "Insurance Verification", "type": "verification", "required": true}'::jsonb,
      '{"step": 3, "name": "Specialist Selection", "type": "selection", "required": true}'::jsonb,
      '{"step": 4, "name": "Records Transfer", "type": "transfer", "required": true}'::jsonb,
      '{"step": 5, "name": "Appointment Coordination", "type": "coordination", "required": true}'::jsonb
    ]
  END as steps,
  true as enabled,
  NOW(),
  NOW()
FROM workflow_data w
CROSS JOIN org_data o;

COMMIT;