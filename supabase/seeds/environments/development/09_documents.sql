-- Start transaction
BEGIN;

-- Create documents for patients
DO $BODY$
DECLARE
  patient_record RECORD;
  org_record RECORD;
  doc_count INTEGER;
  i INTEGER;
  
  doc_types TEXT[] := ARRAY['lab_report', 'imaging', 'vaccination_record', 'prescription', 'growth_chart', 'discharge_summary', 'consent_form', 'insurance_card'];
  doc_titles TEXT[] := ARRAY[
    'Complete Blood Count Results',
    'Chest X-Ray Report', 
    'Vaccination Record 2024',
    'Prescription - Medication',
    'Growth Chart Assessment',
    'Hospital Discharge Summary',
    'Treatment Consent Form',
    'Insurance Information'
  ];
  doc_descriptions TEXT[] := ARRAY[
    'Lab results within normal range',
    'Medical imaging report',
    'Current vaccination status',
    'Prescribed medication details',
    'Patient growth tracking',
    'Summary of hospital stay',
    'Signed consent for treatment',
    'Insurance coverage details'
  ];

BEGIN
  -- Create documents for each patient
  FOR patient_record IN
    SELECT p.id as patient_id, p.organization_id, p.first_name || ' ' || p.last_name as patient_name
    FROM patients p
  LOOP
    -- Each patient gets 1-4 documents
    doc_count := 1 + floor(random() * 4)::int;
    
    FOR i IN 1..doc_count LOOP
      INSERT INTO public.documents (
        id,
        organization_id,
        patient_id,
        document_type,
        title,
        description,
        file_path,
        mime_type,
        metadata,
        created_by,
        created_at,
        updated_at
      ) VALUES (
        uuid_generate_v4(),
        patient_record.organization_id,
        patient_record.patient_id,
        doc_types[1 + (random() * (array_length(doc_types, 1) - 1))::int],
        doc_titles[1 + (random() * (array_length(doc_titles, 1) - 1))::int],
        doc_descriptions[1 + (random() * (array_length(doc_descriptions, 1) - 1))::int],
        '/documents/' || lower(replace(patient_record.patient_name, ' ', '_')) || '_' || i::text || '.pdf',
        'application/pdf',
        ('{"patient_name": "' || patient_record.patient_name || '", "document_number": ' || i::text || '}')::jsonb,
        (SELECT hp.user_id FROM healthcare_providers hp WHERE hp.organization_id = patient_record.organization_id ORDER BY RANDOM() LIMIT 1),
        NOW() - make_interval(days => (random() * 365)::int),
        NOW() - make_interval(days => (random() * 30)::int)
      );
    END LOOP;
  END LOOP;
END
$BODY$;

COMMIT;