-- Start transaction
BEGIN;

-- Create allergies for patients
DO $BODY$
DECLARE
  patient_record RECORD;
  allergy_count INTEGER;
  i INTEGER;
  
  allergens TEXT[] := ARRAY['Penicillin', 'Sulfa drugs', 'Aspirin', 'Ibuprofen', 'Codeine', 'Morphine', 'Shellfish', 'Peanuts', 'Tree nuts', 'Eggs', 'Milk', 'Soy', 'Wheat', 'Latex', 'Contrast dye', 'Iodine'];
  severities allergy_severity[] := ARRAY['mild'::allergy_severity, 'moderate'::allergy_severity, 'severe'::allergy_severity];
  
BEGIN
  -- Create allergies for 40% of patients
  FOR patient_record IN
    SELECT id, organization_id
    FROM patients
    WHERE random() < 0.4  -- 40% of patients have allergies
  LOOP
    -- Each patient gets 1-3 allergies
    allergy_count := 1 + floor(random() * 3)::int;
    
    FOR i IN 1..allergy_count LOOP
      INSERT INTO public.allergies (
        id,
        patient_id,
        allergen,
        reaction,
        severity,
        onset_date,
        created_at,
        updated_at
      ) VALUES (
        uuid_generate_v4(),
        patient_record.id,
        allergens[1 + (random() * (array_length(allergens, 1) - 1))::int],
        CASE 
          WHEN random() < 0.3 THEN 'Rash, hives'
          WHEN random() < 0.6 THEN 'Swelling, difficulty breathing'
          WHEN random() < 0.8 THEN 'Nausea, vomiting'
          ELSE 'Anaphylaxis'
        END,
        severities[1 + (random() * (array_length(severities, 1) - 1))::int],
        (NOW() - make_interval(years => (random() * 20)::int))::date,
        NOW() - make_interval(days => (random() * 365)::int),
        NOW() - make_interval(days => (random() * 30)::int)
      );
    END LOOP;
  END LOOP;
END
$BODY$;

COMMIT;
