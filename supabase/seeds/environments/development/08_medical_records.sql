-- Start transaction
BEGIN;

-- Generate comprehensive medical records for patients
DO $$
DECLARE
  patient_rec RECORD;
  provider_rec RECORD;
  i INTEGER;
  visit_date_val TIMESTAMP;
  chief_complaints TEXT[] := ARRAY[
    'Annual physical examination',
    'Follow-up for chronic condition',
    'Chest pain',
    'Shortness of breath',
    'Abdominal pain',
    'Headache',
    'Back pain',
    'Joint pain',
    'Fatigue',
    'Dizziness',
    'Cough',
    'Fever',
    'Nausea and vomiting',
    'Skin rash',
    'Vision problems',
    'Hearing problems',
    'Sleep problems',
    'Anxiety',
    'Depression',
    'Weight loss/gain',
    'Medication review',
    'Vaccination',
    'Pre-operative evaluation',
    'Post-operative follow-up',
    'Well-child visit',
    'Pregnancy check-up',
    'Diabetes management',
    'Hypertension follow-up',
    'Asthma management',
    'Allergy evaluation'
  ];

  diagnoses TEXT[] := ARRAY[
    'Essential hypertension',
    'Type 2 diabetes mellitus',
    'Asthma',
    'COPD',
    'Coronary artery disease',
    'Arthritis',
    'Depression',
    'Anxiety disorder',
    'Migraine',
    'Allergic rhinitis',
    'GERD',
    'Osteoporosis',
    'Chronic kidney disease',
    'Atrial fibrillation',
    'Heart failure',
    'Hyperlipidemia',
    'Obesity',
    'Sleep apnea',
    'Hypothyroidism',
    'Anemia',
    'Upper respiratory infection',
    'Urinary tract infection',
    'Gastroenteritis',
    'Pneumonia',
    'Bronchitis',
    'Sinusitis',
    'Dermatitis',
    'Conjunctivitis',
    'Otitis media',
    'Healthy adult examination'
  ];

  chief_complaint_val TEXT;
  diagnosis_val TEXT[];
  treatment_plan_val TEXT;
  notes_val TEXT;
BEGIN
  -- Create medical records for patients (about 40% get medical records)
  FOR patient_rec IN
    SELECT p.id, p.first_name || ' ' || p.last_name AS name, p.organization_id, p.date_of_birth
    FROM public.patients p
    WHERE random() < 0.4  -- 40% of patients get medical records
  LOOP
    -- Find a provider in the same organization
    SELECT hp.id, hp.first_name || ' ' || hp.last_name AS name
    INTO provider_rec
    FROM public.healthcare_providers hp
    WHERE hp.organization_id = patient_rec.organization_id
    ORDER BY random()
    LIMIT 1;

    -- Skip if no provider found
    CONTINUE WHEN provider_rec IS NULL;

    -- Create 1-3 medical records per patient
    FOR i IN 1..(1 + floor(random() * 3)::int) LOOP
      -- Generate visit date (within last 2 years)
      visit_date_val := NOW() - (random() * 730)::int * INTERVAL '1 day';

      -- Select random chief complaint
      chief_complaint_val := chief_complaints[1 + (random() * (array_length(chief_complaints, 1) - 1))::int];

      -- Generate 1-3 diagnoses
      diagnosis_val := ARRAY[diagnoses[1 + (random() * (array_length(diagnoses, 1) - 1))::int]];
      IF random() < 0.3 THEN
        diagnosis_val := diagnosis_val || diagnoses[1 + (random() * (array_length(diagnoses, 1) - 1))::int];
      END IF;
      IF random() < 0.1 THEN
        diagnosis_val := diagnosis_val || diagnoses[1 + (random() * (array_length(diagnoses, 1) - 1))::int];
      END IF;

      -- Generate treatment plan based on chief complaint
      CASE
        WHEN chief_complaint_val LIKE '%physical%' THEN
          treatment_plan_val := 'Continue current medications. Lifestyle counseling. Routine screening tests. Follow-up in 12 months.';
        WHEN chief_complaint_val LIKE '%chronic%' OR chief_complaint_val LIKE '%diabetes%' OR chief_complaint_val LIKE '%hypertension%' THEN
          treatment_plan_val := 'Medication adjustment. Lifestyle modifications. Regular monitoring. Follow-up in 3 months.';
        WHEN chief_complaint_val LIKE '%pain%' THEN
          treatment_plan_val := 'Pain management plan. Physical therapy referral. Anti-inflammatory medication. Follow-up in 2 weeks.';
        WHEN chief_complaint_val LIKE '%infection%' OR chief_complaint_val LIKE '%cough%' OR chief_complaint_val LIKE '%fever%' THEN
          treatment_plan_val := 'Antibiotic therapy. Symptomatic treatment. Rest and hydration. Follow-up if not improving in 5-7 days.';
        WHEN chief_complaint_val LIKE '%child%' THEN
          treatment_plan_val := 'Age-appropriate developmental screening. Vaccination updates. Nutritional counseling. Next visit in 6 months.';
        WHEN chief_complaint_val LIKE '%anxiety%' OR chief_complaint_val LIKE '%depression%' THEN
          treatment_plan_val := 'Counseling referral. Medication evaluation. Lifestyle modifications. Follow-up in 4 weeks.';
        WHEN chief_complaint_val LIKE '%vaccination%' THEN
          treatment_plan_val := 'Vaccination administered. Monitor for adverse reactions. Maintain vaccination schedule.';
        ELSE
          treatment_plan_val := 'Symptomatic treatment. Lifestyle modifications. Follow-up as needed or if symptoms worsen.';
      END CASE;

      -- Generate comprehensive notes
      notes_val := 'Patient: ' || patient_rec.name ||
                  ' | Chief Complaint: ' || chief_complaint_val ||
                  ' | Provider: ' || provider_rec.name ||
                  ' | Visit Date: ' || visit_date_val::date ||
                  ' | Assessment: ';

      -- Add assessment based on diagnosis
      IF array_length(diagnosis_val, 1) = 1 THEN
        notes_val := notes_val || 'Patient diagnosed with ' || diagnosis_val[1] || '. ';
      ELSE
        notes_val := notes_val || 'Patient diagnosed with multiple conditions including ' || array_to_string(diagnosis_val, ', ') || '. ';
      END IF;

      -- Add clinical notes
      CASE
        WHEN random() < 0.3 THEN
          notes_val := notes_val || 'Patient reports improvement with current treatment. Vital signs stable. No acute distress noted.';
        WHEN random() < 0.6 THEN
          notes_val := notes_val || 'Patient compliant with medications. Some ongoing symptoms but manageable. Continue current plan.';
        ELSE
          notes_val := notes_val || 'Patient doing well overall. Discussed treatment options and lifestyle modifications. Patient understanding confirmed.';
      END CASE;

      -- Insert the medical record
      INSERT INTO public.medical_records (
        id,
        organization_id,
        patient_id,
        provider_id,
        visit_date,
        chief_complaint,
        diagnosis,
        treatment_plan,
        notes,
        created_at,
        updated_at
      ) VALUES (
        uuid_generate_v4(),
        patient_rec.organization_id,
        patient_rec.id,
        provider_rec.id,
        visit_date_val,
        chief_complaint_val,
        diagnosis_val,
        treatment_plan_val,
        notes_val,
        visit_date_val + INTERVAL '1 hour',
        visit_date_val + INTERVAL '2 hours'
      );
    END LOOP;
  END LOOP;
END
$$;

COMMIT;