-- Start transaction
BEGIN;

-- Create immunizations for patients
DO $BODY$
DECLARE
  patient_record RECORD;
  imm_count INTEGER;
  i INTEGER;
  admin_date DATE;
  
  vaccines TEXT[] := ARRAY[
    'COVID-19', 'Influenza', 'Tdap', 'MMR', 'Varicella', 'Hepatitis B', 
    'HPV', 'Pneumococcal', 'Meningococcal', 'Shingles', 'Polio', 'Hepatitis A'
  ];
  manufacturers TEXT[] := ARRAY[
    'Pfizer-BioNTech', 'Moderna', 'Sanofi', 'GSK', 'Merck', 'Johnson & Johnson'
  ];
  
BEGIN
  -- Create immunizations for each patient
  FOR patient_record IN
    SELECT p.id as patient_id, p.date_of_birth
    FROM patients p
  LOOP
    -- Each patient gets 2-6 immunizations based on age
    imm_count := 2 + floor(random() * 5)::int;
    
    FOR i IN 1..imm_count LOOP
      -- Calculate administration date first
      admin_date := (patient_record.date_of_birth + make_interval(years => (1 + floor(random() * 40)::int)))::date;
      
      INSERT INTO public.immunizations (
        id,
        patient_id,
        vaccine_name,
        vaccine_code,
        manufacturer,
        lot_number,
        dose_number,
        administered_date,
        administered_by,
        site,
        route,
        notes,
        expiration_date,
        metadata,
        created_at,
        updated_at
      ) VALUES (
        uuid_generate_v4(),
        patient_record.patient_id,
        vaccines[1 + (random() * (array_length(vaccines, 1) - 1))::int],
        'CVX-' || (100 + floor(random() * 50)::int)::text,
        manufacturers[1 + (random() * (array_length(manufacturers, 1) - 1))::int],
        'LOT' || (1000 + floor(random() * 9000)::int)::text,
        CASE WHEN random() < 0.7 THEN 1 ELSE 2 END,
        admin_date,
        (SELECT hp.id FROM healthcare_providers hp ORDER BY RANDOM() LIMIT 1),
        CASE floor(random() * 4)::int
          WHEN 0 THEN 'left_arm'
          WHEN 1 THEN 'right_arm'
          WHEN 2 THEN 'left_thigh'
          ELSE 'right_thigh'
        END::administration_site,
        'intramuscular'::administration_route,
        CASE WHEN random() < 0.3 THEN 'No adverse reactions noted' ELSE NULL END,
        admin_date + make_interval(years => (1 + floor(random() * 3)::int)),
        '{"auto_generated": true}'::jsonb,
        NOW() - make_interval(days => (random() * 365)::int),
        NOW() - make_interval(days => (random() * 30)::int)
      );
    END LOOP;
  END LOOP;
END
$BODY$;

COMMIT;
