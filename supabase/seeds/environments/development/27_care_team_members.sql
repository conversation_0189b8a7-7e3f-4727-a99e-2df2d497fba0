-- Start transaction
BEGIN;

-- Create care team members by assigning providers to patients
DO $BODY$
DECLARE
  patient_record RECORD;
  provider_record RECORD;
  team_size INTEGER;
  start_date_val DATE;
  roles TEXT[] := ARRAY['Primary Care Physician', 'Specialist', 'Nurse Practitioner', 'Care Coordinator', 'Nutritionist', 'Social Worker'];
BEGIN
  -- Loop through patients and assign care teams
  FOR patient_record IN
    SELECT p.id as patient_id
    FROM patients p
    ORDER BY RANDOM()
    LIMIT 400  -- Give care teams to 400 patients
  LOOP
    -- Each patient gets 1-3 care team members
    team_size := 1 + floor(random() * 3)::int;
    
    -- Get random providers for this patient
    FOR provider_record IN
      SELECT hp.id as provider_id
      FROM healthcare_providers hp
      ORDER BY RANDOM()
      LIMIT team_size
    LOOP
      -- Calculate start date first
      start_date_val := (CURRENT_DATE - make_interval(days => (random() * 365)::int))::date;
      
      -- Insert care team member
      INSERT INTO public.care_team_members (
        id,
        patient_id,
        provider_id,
        role,
        start_date,
        end_date,
        primary_contact,
        notes,
        created_at,
        updated_at
      ) VALUES (
        uuid_generate_v4(),
        patient_record.patient_id,
        provider_record.provider_id,
        roles[1 + (random() * (array_length(roles, 1) - 1))::int],
        start_date_val,
        CASE WHEN random() < 0.95 THEN NULL ELSE start_date_val + make_interval(days => (1 + floor(random() * 180)::int)) END,
        CASE WHEN random() < 0.3 THEN true ELSE false END,
        CASE WHEN random() < 0.4 THEN 'Key member of patient care team' ELSE NULL END,
        NOW() - make_interval(days => (random() * 180)::int),
        NOW() - make_interval(days => (random() * 30)::int)
      );
    END LOOP;
  END LOOP;
END
$BODY$;

COMMIT;
