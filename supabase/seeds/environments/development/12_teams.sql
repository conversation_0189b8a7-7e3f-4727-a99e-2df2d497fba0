-- Teams seed data
BEGIN;

-- Create teams for departments
DO $BODY$
DECLARE
  dept_record RECORD;
  team_count INTEGER;
  i INTEGER;
  team_name TEXT;
  team_descriptions TEXT[] := ARRAY[
    'Primary care team providing comprehensive healthcare',
    'Emergency response team for urgent medical situations', 
    'Specialized care team for complex cases',
    'Nursing team providing direct patient care',
    'Administrative team managing operations',
    'Support team for patient services'
  ];
BEGIN
  -- Loop through each department and create teams
  FOR dept_record IN
    SELECT d.id as dept_id, d.name as dept_name, d.type as dept_type
    FROM departments d
  LOOP
    -- Each department gets 1-3 teams
    team_count := 1 + floor(random() * 3)::int;
    
    FOR i IN 1..team_count LOOP
      -- Generate team name based on department
      CASE i
        WHEN 1 THEN team_name := dept_record.dept_name || ' Core Team';
        WHEN 2 THEN team_name := dept_record.dept_name || ' Support Team';
        WHEN 3 THEN team_name := dept_record.dept_name || ' Administrative Team';
        ELSE team_name := dept_record.dept_name || ' Team ' || i::text;
      END CASE;
      
      -- Insert team
      INSERT INTO public.teams (
        id,
        department_id,
        name,
        description,
        created_at,
        updated_at
      ) VALUES (
        uuid_generate_v4(),
        dept_record.dept_id,
        team_name,
        team_descriptions[1 + (random() * (array_length(team_descriptions, 1) - 1))::int],
        NOW() - make_interval(days => (random() * 180)::int),
        NOW() - make_interval(days => (random() * 30)::int)
      );
    END LOOP;
  END LOOP;
END
$BODY$;

COMMIT;
