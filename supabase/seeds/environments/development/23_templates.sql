-- Start transaction
BEGIN;

-- Insert Templates
WITH org_data AS (
  SELECT id, name
  FROM public.organizations
),
template_nums AS (
  SELECT 1 as template_num UNION SELECT 2 UNION SELECT 3
)
INSERT INTO public.templates (
  id,
  organization_id,
  name,
  type,
  content,
  metadata,
  created_at,
  updated_at
)
SELECT
  uuid_generate_v4(),
  o.id as organization_id,
  CASE
    WHEN o.name = 'Spritely Medical Center' AND t.template_num = 1 THEN 'Progress Note'
    WHEN o.name = 'Spritely Medical Center' AND t.template_num = 2 THEN 'Referral Letter'
    WHEN o.name = 'Spritely Medical Center' AND t.template_num = 3 THEN 'Prescription'
    WHEN o.name = 'Spritely Community Clinic' THEN 'After Visit Summary'
    WHEN o.name = 'Spritely Pediatrics' THEN 'Well Child Visit'
  END as name,
  CASE
    WHEN o.name = 'Spritely Medical Center' AND t.template_num = 1 THEN 'clinical_note'
    WHEN o.name = 'Spritely Medical Center' AND t.template_num = 2 THEN 'letter'
    WHEN o.name = 'Spritely Medical Center' AND t.template_num = 3 THEN 'prescription'
    WHEN o.name = 'Spritely Community Clinic' THEN 'patient_instructions'
    WHEN o.name = 'Spritely Pediatrics' THEN 'clinical_note'
  END as type,
  CASE
    WHEN o.name = 'Spritely Medical Center' AND t.template_num = 1 THEN 'SUBJECTIVE: {{subjective}}\n\nOBJECTIVE: {{objective}}\n\nASSESSMENT: {{assessment}}\n\nPLAN: {{plan}}'
    WHEN o.name = 'Spritely Medical Center' AND t.template_num = 2 THEN 'Dear Dr. {{recipient_name}},\n\nI am referring {{patient_name}} to you for evaluation and management of {{condition}}.\n\n{{clinical_information}}\n\nThank you for seeing this patient.\n\nSincerely,\nDr. {{sender_name}}'
    WHEN o.name = 'Spritely Medical Center' AND t.template_num = 3 THEN 'Rx\n\nPatient: {{patient_name}}\nDOB: {{patient_dob}}\nDate: {{current_date}}\n\n{{medication_name}} {{dosage}} {{sig}}\nQuantity: {{quantity}}\nRefills: {{refills}}\n\nPrescriber: {{prescriber_name}}, {{credentials}}\nDEA: {{dea_number}}\nSignature: ___________________'
    WHEN o.name = 'Spritely Community Clinic' THEN 'Thank you for visiting {{clinic_name}} today.\n\nDiagnosis: {{diagnosis}}\n\nTreatment Plan: {{treatment_plan}}\n\nMedications: {{medications}}\n\nFollow-up: {{follow_up}}\n\nPlease call our office if you have any questions or concerns.'
    WHEN o.name = 'Spritely Pediatrics' THEN 'GROWTH: Height {{height}} ({{height_percentile}}%), Weight {{weight}} ({{weight_percentile}}%), Head Circumference {{head_circumference}} ({{hc_percentile}}%)\n\nDEVELOPMENT: {{development}}\n\nPHYSICAL EXAM: {{physical_exam}}\n\nASSESSMENT: {{assessment}}\n\nPLAN: {{plan}}\n\nANTICIPATORY GUIDANCE: {{anticipatory_guidance}}'
  END as content,
  CASE
    WHEN o.name = 'Spritely Medical Center' AND t.template_num = 1 THEN '{"variables": ["subjective", "objective", "assessment", "plan"]}'::jsonb
    WHEN o.name = 'Spritely Medical Center' AND t.template_num = 2 THEN '{"variables": ["recipient_name", "patient_name", "condition", "clinical_information", "sender_name"]}'::jsonb
    WHEN o.name = 'Spritely Medical Center' AND t.template_num = 3 THEN '{"variables": ["patient_name", "patient_dob", "current_date", "medication_name", "dosage", "sig", "quantity", "refills", "prescriber_name", "credentials", "dea_number"]}'::jsonb
    WHEN o.name = 'Spritely Community Clinic' THEN '{"variables": ["clinic_name", "diagnosis", "treatment_plan", "medications", "follow_up"]}'::jsonb
    WHEN o.name = 'Spritely Pediatrics' THEN '{"variables": ["height", "height_percentile", "weight", "weight_percentile", "head_circumference", "hc_percentile", "development", "physical_exam", "assessment", "plan", "anticipatory_guidance"]}'::jsonb
  END as metadata,
  NOW(),
  NOW()
FROM org_data o
CROSS JOIN template_nums t
WHERE (o.name = 'Spritely Medical Center') OR
      (o.name = 'Spritely Community Clinic' AND t.template_num = 1) OR
      (o.name = 'Spritely Pediatrics' AND t.template_num = 1);

COMMIT;
