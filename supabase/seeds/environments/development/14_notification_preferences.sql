-- Start transaction
BEGIN;

-- Insert Notification Preferences
INSERT INTO public.notification_preferences (
  id,
  user_id,
  type,
  email_enabled,
  sms_enabled,
  in_app_enabled,
  created_at,
  updated_at
)
-- Provider notification preferences
SELECT
  uuid_generate_v4(),
  user_id,
  notification_type,
  email_enabled,
  sms_enabled,
  in_app_enabled,
  NOW(),
  NOW()
FROM (
  SELECT id as user_id FROM auth.users
) as users
CROSS JOIN (
  VALUES
    ('message'::notification_type, true, false, true),
    ('task_assignment'::notification_type, true, false, true),
    ('lab_result'::notification_type, true, false, true),
    ('medical_record_update'::notification_type, true, false, true),
    ('appointment_reminder'::notification_type, true, true, true),
    ('prescription_update'::notification_type, true, true, true)
) AS pref_data(notification_type, email_enabled, sms_enabled, in_app_enabled)
;

COMMIT;
