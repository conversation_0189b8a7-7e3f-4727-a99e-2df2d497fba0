-- Start transaction
BEGIN;

-- Insert Analytics Events
WITH org_data AS (
  SELECT id, name
  FROM public.organizations
)
INSERT INTO public.analytics_events (
  id,
  organization_id,
  event_type,
  event_data,
  user_id,
  timestamp,
  created_at
)
SELECT
  uuid_generate_v4(),
  o.id as organization_id,
  event_type,
  event_data,
  NULL as user_id,
  NOW() - (random() * INTERVAL '30 days') as timestamp,
  NOW()
FROM org_data o
CROSS JOIN (
  VALUES
    ('login', '{"method": "email", "success": true}'::jsonb),
    ('appointment_scheduled', '{"method": "online", "appointment_type": "follow_up"}'::jsonb),
    ('prescription_refill', '{"medication": "Lisinopril", "quantity": 30}'::jsonb),
    ('lab_order', '{"test_type": "blood_panel", "ordered_by": "physician"}'::jsonb),
    ('message_sent', '{"recipient_type": "provider", "message_type": "question"}'::jsonb)
) AS event_data(event_type, event_data)
WHERE o.name = 'Spritely Medical Center';

-- Insert Analytics Metrics
WITH org_data AS (
  SELECT id, name
  FROM public.organizations
)
INSERT INTO public.analytics_metrics (
  id,
  organization_id,
  metric_name,
  metric_value,
  dimensions,
  timestamp,
  created_at
)
SELECT
  uuid_generate_v4(),
  o.id as organization_id,
  metric_name,
  (random() * 100)::numeric(10,2) as metric_value,
  dimensions,
  date_trunc('day', NOW() - (n || ' days')::interval) as timestamp,
  NOW()
FROM org_data o
CROSS JOIN (
  VALUES
    ('appointments_completed', '{"department": "primary_care"}'::jsonb),
    ('appointments_cancelled', '{"department": "primary_care"}'::jsonb),
    ('new_patients', '{"source": "referral"}'::jsonb),
    ('average_wait_time', '{"department": "primary_care", "unit": "minutes"}'::jsonb),
    ('prescription_count', '{"type": "new"}'::jsonb)
) AS metric_data(metric_name, dimensions)
CROSS JOIN (
  SELECT generate_series(0, 29) as n
) as days
WHERE o.name = 'Spritely Medical Center';

-- Refresh Analytics Daily Appointments materialized view
REFRESH MATERIALIZED VIEW public.analytics_daily_appointments;

-- Refresh Analytics Provider Metrics materialized view
REFRESH MATERIALIZED VIEW public.analytics_provider_metrics;

-- Refresh Analytics Patient Metrics materialized view
REFRESH MATERIALIZED VIEW public.analytics_patient_metrics;

COMMIT;
