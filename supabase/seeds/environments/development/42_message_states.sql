-- Start transaction
BEGIN;

-- Insert Message States
WITH message_data AS (
  SELECT m.id, m.conversation_id, m.sender_id
  FROM public.messages m
),
participant_data AS (
  SELECT cp.conversation_id, cp.user_id
  FROM public.conversation_participants cp
)
INSERT INTO public.message_states (
  id,
  message_id,
  user_id,
  state,
  updated_at
)
SELECT
  uuid_generate_v4(),
  m.id as message_id,
  p.user_id,
  CASE 
    WHEN m.sender_id = p.user_id THEN 'sent'::message_state
    ELSE 'delivered'::message_state
  END as state,
  NOW() - INTERVAL '1 day'
FROM message_data m
JOIN participant_data p ON m.conversation_id = p.conversation_id;

COMMIT;
