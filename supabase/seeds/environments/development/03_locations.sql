-- Start transaction
BEGIN;

-- Create multiple locations for organizations based on their type and size
DO $$
DECLARE
  org_record RECORD;
  location_count INTEGER;
  location_name TEXT;
  location_type TEXT;
  street_address TEXT;
  city_name TEXT;
  phone_number TEXT;
  email_address TEXT;
  i INTEGER;
  
  -- Arrays for generating realistic data
  streets TEXT[] := ARRAY['Medical Way', 'Health Blvd', 'Wellness Ave', 'Care St', 'Hospital Dr', 'Clinic Rd', 'Center Ln', 'Plaza Way', 'Park Ave', 'Main St', 'University Dr', 'Research Blvd', 'Innovation Way', 'Science Park Dr'];
  cities TEXT[] := ARRAY['Healthville', 'Careville', 'Medtown', 'Wellness City', 'Healthcare Heights', 'Medical Park', 'Clinic Grove', 'Hospital Hills', 'Care Valley', 'Health Springs', 'Riverside', 'Northside', 'Westlake', 'Eastside', 'Downtown', 'Midtown'];
  
  -- Location type variations for different organizations
  hospital_location_types TEXT[] := ARRAY['hospital', 'outpatient', 'emergency', 'specialty'];
  clinic_location_types TEXT[] := ARRAY['clinic', 'urgent_care', 'outpatient', 'specialty'];
  practice_location_types TEXT[] := ARRAY['specialty', 'clinic', 'outpatient'];
  
  -- Location name suffixes
  hospital_suffixes TEXT[] := ARRAY['Main Campus', 'North Campus', 'South Campus', 'East Campus', 'West Campus', 'Downtown Campus', 'Outpatient Center', 'Emergency Center', 'Specialty Center', 'Research Center'];
  clinic_suffixes TEXT[] := ARRAY['Main Clinic', 'North Clinic', 'South Clinic', 'East Clinic', 'West Clinic', 'Urgent Care', 'Family Medicine', 'Specialty Clinic', 'Outpatient Center'];
  practice_suffixes TEXT[] := ARRAY['Main Office', 'North Office', 'South Office', 'Specialty Center', 'Outpatient Clinic'];
  
BEGIN
  -- Loop through each organization and create multiple locations
  FOR org_record IN
    SELECT id, name, type, settings FROM organizations ORDER BY name
  LOOP
    -- Determine number of locations based on organization type and size
    CASE org_record.type
      WHEN 'hospital' THEN 
        -- Large hospitals get 3-6 locations, smaller ones get 2-4
        IF (org_record.settings->>'bed_count')::int > 400 THEN
          location_count := 4 + (random() * 3)::int; -- 4-6 locations
        ELSE
          location_count := 2 + (random() * 3)::int; -- 2-4 locations
        END IF;
      WHEN 'clinic' THEN 
        location_count := 2 + (random() * 2)::int; -- 2-3 locations
      WHEN 'practice' THEN 
        location_count := 1 + (random() * 2)::int; -- 1-2 locations
      ELSE 
        location_count := 2; -- Default
    END CASE;

    -- Create locations for this organization
    FOR i IN 1..location_count LOOP
      -- Generate location name
      IF i = 1 THEN
        location_name := org_record.name; -- First location keeps the org name
      ELSE
        CASE org_record.type
          WHEN 'hospital' THEN 
            location_name := split_part(org_record.name, ' Hospital', 1) || ' - ' || hospital_suffixes[1 + (random() * (array_length(hospital_suffixes, 1) - 1))::int];
          WHEN 'clinic' THEN 
            location_name := split_part(org_record.name, ' Clinic', 1) || ' - ' || clinic_suffixes[1 + (random() * (array_length(clinic_suffixes, 1) - 1))::int];
          WHEN 'practice' THEN 
            location_name := org_record.name || ' - ' || practice_suffixes[1 + (random() * (array_length(practice_suffixes, 1) - 1))::int];
          ELSE 
            location_name := org_record.name || ' - Location ' || i;
        END CASE;
      END IF;

      -- Set location type based on organization type and location number
      CASE org_record.type
        WHEN 'hospital' THEN 
          IF i = 1 THEN
            location_type := 'hospital'; -- Main location is always hospital
          ELSE
            location_type := hospital_location_types[1 + (random() * (array_length(hospital_location_types, 1) - 1))::int];
          END IF;
        WHEN 'clinic' THEN 
          location_type := clinic_location_types[1 + (random() * (array_length(clinic_location_types, 1) - 1))::int];
        WHEN 'practice' THEN 
          location_type := practice_location_types[1 + (random() * (array_length(practice_location_types, 1) - 1))::int];
        ELSE 
          location_type := 'clinic';
      END CASE;

      -- Generate realistic address (different locations for each facility)
      street_address := (100 + random() * 9899)::int || ' ' || streets[1 + (random() * (array_length(streets, 1) - 1))::int];
      city_name := cities[1 + (random() * (array_length(cities, 1) - 1))::int];

      -- Generate phone and email
      phone_number := '555-' || LPAD((100 + random() * 899)::int::text, 3, '0') || '-' || LPAD((1000 + random() * 8999)::int::text, 4, '0');
      email_address := 'location' || i || '@' || lower(replace(replace(split_part(org_record.name, ' ', 1), '''', ''), '-', '')) || '.example.com';

      -- Insert the location
      INSERT INTO public.locations (
        id,
        organization_id,
        name,
        type,
        address,
        contact_info,
        operating_hours,
        settings,
        created_at,
        updated_at
      ) VALUES (
        uuid_generate_v4(),
        org_record.id,
        location_name,
        location_type,
        jsonb_build_object(
          'street', street_address,
          'city', city_name,
          'state', CASE (random() * 10)::int
            WHEN 0 THEN 'CA'
            WHEN 1 THEN 'TX'
            WHEN 2 THEN 'FL'
            WHEN 3 THEN 'NY'
            WHEN 4 THEN 'PA'
            WHEN 5 THEN 'IL'
            WHEN 6 THEN 'OH'
            WHEN 7 THEN 'GA'
            WHEN 8 THEN 'NC'
            ELSE 'MI'
          END,
          'zip', LPAD((10000 + random() * 89999)::int::text, 5, '0')
        ),
        jsonb_build_object(
          'phone', phone_number,
          'email', email_address,
          'fax', '555-' || LPAD((100 + random() * 899)::int::text, 3, '0') || '-' || LPAD((1000 + random() * 8999)::int::text, 4, '0'),
          'website', 'https://' || lower(replace(replace(split_part(org_record.name, ' ', 1), '''', ''), '-', '')) || '.example.com'
        ),
        jsonb_build_object(
          'monday', jsonb_build_object('open', '08:00', 'close', CASE location_type WHEN 'emergency' THEN '24:00' ELSE '18:00' END),
          'tuesday', jsonb_build_object('open', '08:00', 'close', CASE location_type WHEN 'emergency' THEN '24:00' ELSE '18:00' END),
          'wednesday', jsonb_build_object('open', '08:00', 'close', CASE location_type WHEN 'emergency' THEN '24:00' ELSE '18:00' END),
          'thursday', jsonb_build_object('open', '08:00', 'close', CASE location_type WHEN 'emergency' THEN '24:00' ELSE '18:00' END),
          'friday', jsonb_build_object('open', '08:00', 'close', CASE location_type WHEN 'emergency' THEN '24:00' ELSE '18:00' END),
          'saturday', jsonb_build_object('open', CASE location_type WHEN 'emergency' THEN '00:00' WHEN 'urgent_care' THEN '09:00' ELSE 'closed' END, 'close', CASE location_type WHEN 'emergency' THEN '24:00' WHEN 'urgent_care' THEN '17:00' ELSE 'closed' END),
          'sunday', jsonb_build_object('open', CASE location_type WHEN 'emergency' THEN '00:00' ELSE 'closed' END, 'close', CASE location_type WHEN 'emergency' THEN '24:00' ELSE 'closed' END)
        ),
        jsonb_build_object(
          'is_main_location', i = 1,
          'location_number', i,
          'total_locations', location_count,
          'specialties', CASE location_type
            WHEN 'hospital' THEN '["Emergency Medicine", "Surgery", "Internal Medicine", "Cardiology"]'::jsonb
            WHEN 'emergency' THEN '["Emergency Medicine", "Trauma Care"]'::jsonb
            WHEN 'urgent_care' THEN '["Urgent Care", "Minor Surgery", "X-Ray"]'::jsonb
            WHEN 'specialty' THEN '["Specialty Care", "Consultations"]'::jsonb
            WHEN 'outpatient' THEN '["Outpatient Surgery", "Diagnostics", "Rehabilitation"]'::jsonb
            ELSE '["Primary Care", "Preventive Medicine"]'::jsonb
          END,
          'bed_count', CASE location_type
            WHEN 'hospital' THEN (50 + random() * 200)::int
            WHEN 'outpatient' THEN (10 + random() * 30)::int
            ELSE 0
          END
        ),
        NOW() - (random() * interval '365 days'),
        NOW() - (random() * interval '30 days')
      );
      
      RAISE NOTICE 'Created location % for organization %', location_name, org_record.name;
    END LOOP;
  END LOOP;
END
$$;

-- Commit transaction
COMMIT;