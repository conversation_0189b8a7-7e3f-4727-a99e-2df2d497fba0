-- Development environment organizations
BEGIN;

-- Insert development organizations
INSERT INTO public.organizations (
    name,
    type,
    subscription_tier,
    settings
)
VALUES
    -- Main organizations (original three)
    (
        'Spritely Medical Center',
        'hospital'::text,
        'enterprise'::text,
        '{
            "features": {
                "dev_mode": true,
                "test_data": true,
                "advanced_analytics": true,
                "ai_diagnostics": true,
                "telehealth": true,
                "patient_portal": true
            },
            "is_test": true,
            "environment": "development",
            "bed_count": 450,
            "departments": ["Emergency", "Surgery", "Cardiology", "Neurology", "Oncology", "Pediatrics", "Obstetrics", "Radiology", "Orthopedics", "Psychiatry"],
            "accreditations": ["Joint Commission", "ANCC Magnet Recognition"],
            "founded_year": 1985
        }'
    ),
    (
        'Spritely Community Clinic',
        'clinic'::text,
        'professional'::text,
        '{
            "features": {
                "dev_mode": true,
                "test_data": true,
                "telehealth": true,
                "patient_portal": true
            },
            "is_test": true,
            "environment": "development",
            "bed_count": 120,
            "departments": ["Primary Care", "Urgent Care", "Pediatrics", "Women''s Health", "Physical Therapy", "Mental Health"],
            "accreditations": ["NCQA Patient-Centered Medical Home"],
            "founded_year": 1998
        }'
    ),
    (
        'Spritely Pediatrics',
        'practice'::text,
        'starter'::text,
        '{
            "features": {
                "dev_mode": true,
                "test_data": true,
                "telehealth": true,
                "patient_portal": true
            },
            "is_test": true,
            "environment": "development",
            "bed_count": 0,
            "departments": ["General Pediatrics", "Pediatric Neurology", "Pediatric Cardiology", "Pediatric Allergy"],
            "accreditations": ["American Academy of Pediatrics"],
            "founded_year": 2010
        }'
    ),

    -- Additional hospitals
    (
        'Northside Regional Hospital',
        'hospital'::text,
        'enterprise'::text,
        '{
            "features": {
                "dev_mode": true,
                "test_data": true,
                "advanced_analytics": true,
                "ai_diagnostics": true,
                "telehealth": true,
                "patient_portal": true
            },
            "is_test": true,
            "environment": "development",
            "bed_count": 380,
            "departments": ["Emergency", "Surgery", "Cardiology", "Neurology", "Oncology", "Pediatrics", "Obstetrics", "Radiology", "Orthopedics", "Psychiatry", "Rehabilitation"],
            "accreditations": ["Joint Commission", "ANCC Magnet Recognition", "Level II Trauma Center"],
            "founded_year": 1978
        }'
    ),
    (
        'Westlake Medical System',
        'hospital'::text,
        'enterprise'::text,
        '{
            "features": {
                "dev_mode": true,
                "test_data": true,
                "advanced_analytics": true,
                "ai_diagnostics": true,
                "telehealth": true,
                "patient_portal": true
            },
            "is_test": true,
            "environment": "development",
            "bed_count": 520,
            "departments": ["Emergency", "Surgery", "Cardiology", "Neurology", "Oncology", "Pediatrics", "Obstetrics", "Radiology", "Orthopedics", "Psychiatry", "Transplant", "Burn Unit"],
            "accreditations": ["Joint Commission", "Level I Trauma Center", "Comprehensive Stroke Center"],
            "founded_year": 1962
        }'
    ),
    (
        'Eastside Community Hospital',
        'hospital'::text,
        'professional'::text,
        '{
            "features": {
                "dev_mode": true,
                "test_data": true,
                "telehealth": true,
                "patient_portal": true
            },
            "is_test": true,
            "environment": "development",
            "bed_count": 180,
            "departments": ["Emergency", "Surgery", "Internal Medicine", "Pediatrics", "Obstetrics", "Radiology"],
            "accreditations": ["Joint Commission", "Primary Stroke Center"],
            "founded_year": 1992
        }'
    ),
    (
        'Metropolitan Medical Center',
        'hospital'::text,
        'enterprise'::text,
        '{
            "features": {
                "dev_mode": true,
                "test_data": true,
                "advanced_analytics": true,
                "ai_diagnostics": true,
                "telehealth": true,
                "patient_portal": true
            },
            "is_test": true,
            "environment": "development",
            "bed_count": 650,
            "departments": ["Emergency", "Surgery", "Cardiology", "Neurology", "Oncology", "Pediatrics", "Obstetrics", "Radiology", "Orthopedics", "Psychiatry", "Transplant", "Research"],
            "accreditations": ["Joint Commission", "Level I Trauma Center", "NCI-Designated Cancer Center"],
            "founded_year": 1955
        }'
    ),
    (
        'Harbor View Hospital',
        'hospital'::text,
        'enterprise'::text,
        '{
            "features": {
                "dev_mode": true,
                "test_data": true,
                "advanced_analytics": true,
                "telehealth": true,
                "patient_portal": true
            },
            "is_test": true,
            "environment": "development",
            "bed_count": 320,
            "departments": ["Emergency", "Surgery", "Cardiology", "Neurology", "Oncology", "Pediatrics", "Obstetrics", "Radiology"],
            "accreditations": ["Joint Commission", "Level II Trauma Center"],
            "founded_year": 1982
        }'
    );

-- Insert additional clinics and practices
INSERT INTO public.organizations (
    name,
    type,
    subscription_tier,
    settings
)
VALUES
    -- Additional clinics
    (
        'Riverside Health Center',
        'clinic'::text,
        'professional'::text,
        '{
            "features": {
                "dev_mode": true,
                "test_data": true,
                "telehealth": true,
                "patient_portal": true
            },
            "is_test": true,
            "environment": "development",
            "bed_count": 0,
            "departments": ["Primary Care", "Urgent Care", "Pediatrics", "Women''s Health", "Physical Therapy", "Cardiology", "Dermatology"],
            "accreditations": ["NCQA Patient-Centered Medical Home"],
            "founded_year": 2005
        }'
    ),
    (
        'Parkside Family Medicine',
        'clinic'::text,
        'professional'::text,
        '{
            "features": {
                "dev_mode": true,
                "test_data": true,
                "telehealth": true,
                "patient_portal": true
            },
            "is_test": true,
            "environment": "development",
            "bed_count": 0,
            "departments": ["Family Medicine", "Pediatrics", "Internal Medicine", "Behavioral Health"],
            "accreditations": ["NCQA Patient-Centered Medical Home"],
            "founded_year": 2001
        }'
    ),
    (
        'Downtown Urgent Care',
        'clinic'::text,
        'starter'::text,
        '{
            "features": {
                "dev_mode": true,
                "test_data": true,
                "telehealth": true,
                "patient_portal": true
            },
            "is_test": true,
            "environment": "development",
            "bed_count": 0,
            "departments": ["Urgent Care", "Occupational Health", "Travel Medicine"],
            "accreditations": ["Urgent Care Association"],
            "founded_year": 2015
        }'
    ),

    -- Additional specialty practices
    (
        'Valley Cardiology Associates',
        'practice'::text,
        'professional'::text,
        '{
            "features": {
                "dev_mode": true,
                "test_data": true,
                "telehealth": true,
                "patient_portal": true
            },
            "is_test": true,
            "environment": "development",
            "bed_count": 0,
            "departments": ["General Cardiology", "Interventional Cardiology", "Electrophysiology", "Heart Failure"],
            "accreditations": ["American College of Cardiology"],
            "founded_year": 2003
        }'
    ),
    (
        'Summit Orthopedic Specialists',
        'practice'::text,
        'professional'::text,
        '{
            "features": {
                "dev_mode": true,
                "test_data": true,
                "telehealth": true,
                "patient_portal": true
            },
            "is_test": true,
            "environment": "development",
            "bed_count": 0,
            "departments": ["Joint Replacement", "Sports Medicine", "Spine Surgery", "Hand Surgery", "Physical Therapy"],
            "accreditations": ["American Academy of Orthopedic Surgeons"],
            "founded_year": 2008
        }'
    ),
    (
        'Lakeside OB/GYN',
        'practice'::text,
        'starter'::text,
        '{
            "features": {
                "dev_mode": true,
                "test_data": true,
                "telehealth": true,
                "patient_portal": true
            },
            "is_test": true,
            "environment": "development",
            "bed_count": 0,
            "departments": ["Obstetrics", "Gynecology", "Fertility", "Women''s Health"],
            "accreditations": ["American College of Obstetricians and Gynecologists"],
            "founded_year": 2012
        }'
    ),
    (
        'Dermatology Partners',
        'practice'::text,
        'starter'::text,
        '{
            "features": {
                "dev_mode": true,
                "test_data": true,
                "telehealth": true,
                "patient_portal": true
            },
            "is_test": true,
            "environment": "development",
            "bed_count": 0,
            "departments": ["Medical Dermatology", "Cosmetic Dermatology", "Mohs Surgery", "Pediatric Dermatology"],
            "accreditations": ["American Academy of Dermatology"],
            "founded_year": 2011
        }'
    ),
    (
        'Neurology Consultants',
        'practice'::text,
        'professional'::text,
        '{
            "features": {
                "dev_mode": true,
                "test_data": true,
                "telehealth": true,
                "patient_portal": true
            },
            "is_test": true,
            "environment": "development",
            "bed_count": 0,
            "departments": ["General Neurology", "Movement Disorders", "Epilepsy", "Headache", "Multiple Sclerosis"],
            "accreditations": ["American Academy of Neurology"],
            "founded_year": 2007
        }'
    );

COMMIT;