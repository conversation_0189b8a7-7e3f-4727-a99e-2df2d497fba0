-- Start transaction
BEGIN;

-- Insert Medications for random patients
DO $BODY$
DECLARE
  patient_record RECORD;
  provider_id UUID;
  med_count INTEGER;
  i INTEGER;
  
  -- Common medications with details
  medications TEXT[] := ARRAY[
    'Lisinopril:10mg:Once daily:Take with water in the morning',
    'Atorvastatin:20mg:Once daily at bedtime:Take at night before bed',
    'Amoxicillin:250mg/5mL:Twice daily for 10 days:Take with food. Complete full course.',
    'Albuterol:90mcg:2 puffs every 4-6 hours as needed:Use as needed for shortness of breath',
    'Cetirizine:5mg/5mL:Once daily:Give in the morning for allergy symptoms',
    'Metformin:500mg:Twice daily with meals:Take with breakfast and dinner',
    'Ibuprofen:200mg:Every 6 hours as needed:Take with food. Do not exceed 1200mg daily',
    'Prednisone:10mg:Once daily for 5 days:Take in the morning with food',
    'Omeprazole:20mg:Once daily before breakfast:Take 30 minutes before eating',
    'Vitamin D:1000 IU:Once daily:Take with food for better absorption',
    'Levothyroxine:50mcg:Once daily on empty stomach:Take 30-60 minutes before breakfast',
    'Sertraline:50mg:Once daily:Take at the same time each day',
    'Aspirin:81mg:Once daily:Take with food to reduce stomach irritation',
    'Montelukast:10mg:Once daily at bedtime:For asthma and allergy control',
    'Gabapentin:100mg:Three times daily:May cause drowsiness'
  ];
  
  med_parts TEXT[];
  med_name TEXT;
  med_dosage TEXT;
  med_frequency TEXT;
  med_instructions TEXT;
  
BEGIN
  -- Loop through patients and assign medications
  FOR patient_record IN
    SELECT p.id as patient_id
    FROM patients p
    ORDER BY RANDOM()
    LIMIT 300  -- Give medications to 300 random patients
  LOOP
    -- Each patient gets 1-4 medications
    med_count := 1 + floor(random() * 4)::int;
    
    FOR i IN 1..med_count LOOP
      -- Select a random provider
      SELECT hp.id INTO provider_id
      FROM healthcare_providers hp
      ORDER BY RANDOM()
      LIMIT 1;
      
      -- Select random medication
      med_parts := string_to_array(medications[1 + (random() * (array_length(medications, 1) - 1))::int], ':');
      med_name := med_parts[1];
      med_dosage := med_parts[2];
      med_frequency := med_parts[3];
      med_instructions := med_parts[4];
      
      -- Insert medication
      INSERT INTO public.medications (
        id,
        patient_id,
        provider_id,
        medication_name,
        dosage,
        frequency,
        start_date,
        end_date,
        active,
        instructions,
        created_at,
        updated_at
      ) VALUES (
        uuid_generate_v4(),
        patient_record.patient_id,
        provider_id,
        med_name,
        med_dosage,
        med_frequency,
        CURRENT_DATE - make_interval(days => (random() * 365)::int),
        CASE 
          WHEN random() < 0.8 THEN NULL  -- 80% ongoing medications
          ELSE CURRENT_DATE - make_interval(days => (random() * 30)::int)  -- 20% completed
        END,
        CASE 
          WHEN random() < 0.85 THEN true  -- 85% active
          ELSE false  -- 15% inactive
        END,
        med_instructions,
        NOW() - make_interval(days => (random() * 30)::int),
        NOW() - make_interval(days => (random() * 7)::int)
      );
    END LOOP;
  END LOOP;
END
$BODY$;

COMMIT;
