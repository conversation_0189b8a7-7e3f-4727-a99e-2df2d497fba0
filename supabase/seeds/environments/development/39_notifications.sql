-- Start transaction
BEGIN;

-- Create notifications for patients and providers
DO $BODY$
DECLARE
  patient_record RECORD;
  provider_record RECORD;
  notification_count INTEGER;
  i INTEGER;
  template_record RECORD;
  
  patient_notification_types TEXT[] := ARRAY['appointment_reminder', 'lab_result', 'prescription_update', 'medical_record_update'];
  provider_notification_types TEXT[] := ARRAY['task_assignment', 'message', 'alert', 'system_update'];
  
BEGIN
  -- Create notifications for patients
  FOR patient_record IN
    SELECT p.id, p.first_name, p.last_name, p.organization_id
    FROM patients p
    ORDER BY RANDOM()
    LIMIT 80  -- Create notifications for 80 patients
  LOOP
    -- Each patient gets 1-4 notifications
    notification_count := 1 + floor(random() * 4)::int;
    
    FOR i IN 1..notification_count LOOP
      -- Get a random notification template for patients
      SELECT id, name INTO template_record
      FROM notification_templates nt
      WHERE nt.organization_id = patient_record.organization_id
        AND nt.type = ANY(patient_notification_types::notification_type[])
      ORDER BY RANDOM()
      LIMIT 1;
      
      IF template_record.id IS NOT NULL THEN
        INSERT INTO public.notifications (
          id,
          organization_id,
          type,
          recipient_id,
          status,
          title,
          content,
          metadata,
          sent_at,
          read_at,
          created_at,
          updated_at
        ) VALUES (
          uuid_generate_v4(),
          patient_record.organization_id,
          patient_notification_types[1 + (random() * (array_length(patient_notification_types, 1) - 1))::int]::notification_type,
          patient_record.id,
          CASE WHEN random() < 0.7 THEN 'read'::notification_status ELSE 'sent'::notification_status END,
          template_record.name || ' - ' || patient_record.first_name || ' ' || patient_record.last_name,
          CASE 
            WHEN template_record.name LIKE '%Appointment%' THEN 'You have an upcoming appointment. Please arrive 15 minutes early.'
            WHEN template_record.name LIKE '%Lab%' THEN 'Your lab results are ready for review.'
            WHEN template_record.name LIKE '%Prescription%' THEN 'Your prescription is ready for pickup.'
            WHEN template_record.name LIKE '%Bill%' THEN 'You have an outstanding balance on your account.'
            ELSE 'This is a wellness reminder from your healthcare team.'
          END,
          jsonb_build_object(
            'patient_id', patient_record.id,
            'priority', CASE WHEN random() < 0.3 THEN 'high' ELSE 'normal' END
          ),
          NOW() - make_interval(days => (random() * 30)::int),
          CASE WHEN random() < 0.7 THEN NOW() - make_interval(days => (random() * 20)::int) ELSE NULL END,
          NOW() - make_interval(days => (random() * 90)::int),
          NOW() - make_interval(days => (random() * 30)::int)
        );
      END IF;
    END LOOP;
  END LOOP;
  
  -- Create notifications for providers
  FOR provider_record IN
    SELECT hp.user_id, hp.first_name, hp.last_name, hp.organization_id
    FROM healthcare_providers hp
    ORDER BY RANDOM()
    LIMIT 50  -- Create notifications for 50 providers
  LOOP
    -- Each provider gets 2-6 notifications
    notification_count := 2 + floor(random() * 5)::int;
    
    FOR i IN 1..notification_count LOOP
      -- Get a random notification template for providers
      SELECT id, name INTO template_record
      FROM notification_templates nt
      WHERE nt.organization_id = provider_record.organization_id
        AND nt.type = ANY(provider_notification_types::notification_type[])
      ORDER BY RANDOM()
      LIMIT 1;
      
      IF template_record.id IS NOT NULL THEN
        INSERT INTO public.notifications (
          id,
          organization_id,
          type,
          recipient_id,
          status,
          title,
          content,
          metadata,
          sent_at,
          read_at,
          created_at,
          updated_at
        ) VALUES (
          uuid_generate_v4(),
          provider_record.organization_id,
          provider_notification_types[1 + (random() * (array_length(provider_notification_types, 1) - 1))::int]::notification_type,
          provider_record.user_id,
          CASE WHEN random() < 0.8 THEN 'read'::notification_status ELSE 'sent'::notification_status END,
          template_record.name || ' - ' || provider_record.first_name || ' ' || provider_record.last_name,
          CASE 
            WHEN template_record.name LIKE '%Task%' THEN 'You have been assigned a new task requiring your attention.'
            WHEN template_record.name LIKE '%Message%' THEN 'You have a new message from a patient.'
            WHEN template_record.name LIKE '%Critical%' THEN 'Critical lab results require immediate review.'
            WHEN template_record.name LIKE '%Cancelled%' THEN 'An appointment has been cancelled.'
            ELSE 'System alert notification.'
          END,
          jsonb_build_object(
            'provider_id', provider_record.user_id,
            'priority', CASE WHEN random() < 0.4 THEN 'high' ELSE 'normal' END
          ),
          NOW() - make_interval(days => (random() * 30)::int),
          CASE WHEN random() < 0.8 THEN NOW() - make_interval(days => (random() * 20)::int) ELSE NULL END,
          NOW() - make_interval(days => (random() * 90)::int),
          NOW() - make_interval(days => (random() * 30)::int)
        );
      END IF;
    END LOOP;
  END LOOP;
END
$BODY$;

COMMIT;
