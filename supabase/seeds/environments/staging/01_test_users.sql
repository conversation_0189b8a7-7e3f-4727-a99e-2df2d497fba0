-- Staging environment test users
-- These users are only available in the staging environment

BEGIN;

-- Create test users for each organization
WITH org_data AS (
    SELECT id, name, type FROM public.organizations WHERE metadata->>'environment' = 'staging'
),
user_data AS (
    INSERT INTO auth.users (
        email,
        encrypted_password,
        email_confirmed_at,
        raw_app_meta_data,
        raw_user_meta_data,
        created_at,
        updated_at
    )
    SELECT
        email,
        -- Password is 'staging123' for all test users
        '$2a$10$Ql9XZz3Jz8KPpJbVrFVzXOQCKLNRhJT9QV0OfJYK/CExqhxGvuwRW',
        NOW(),
        '{"provider":"email","providers":["email"]}',
        jsonb_build_object('name', name, 'organization', org_name, 'is_test', true),
        NOW(),
        NOW()
    FROM (
        -- System admin (access to all orgs)
        SELECT 
            '<EMAIL>' as email,
            'Staging Administrator' as name,
            NULL as org_name
        
        UNION ALL
        
        -- Staging Hospital users
        SELECT '<EMAIL>', 'Hospital Admin', name
        FROM org_data WHERE name = 'Staging Hospital'
        UNION ALL
        SELECT '<EMAIL>', 'Hospital Provider', name
        FROM org_data WHERE name = 'Staging Hospital'
        
        -- Staging Clinic users
        UNION ALL
        SELECT '<EMAIL>', 'Clinic Admin', name
        FROM org_data WHERE name = 'Staging Clinic'
        UNION ALL
        SELECT '<EMAIL>', 'Clinic Provider', name
        FROM org_data WHERE name = 'Staging Clinic'
    ) as users(email, name, org_name)
    RETURNING id, email
)
-- Create identities for the test users
INSERT INTO auth.identities (
    id,
    user_id,
    identity_data,
    provider,
    last_sign_in_at,
    created_at,
    updated_at
)
SELECT
    uuid_generate_v4(),
    id,
    jsonb_build_object('sub', id::text, 'email', email),
    'email',
    NOW(),
    NOW(),
    NOW()
FROM user_data;

-- Map users to organizations with appropriate roles
WITH org_data AS (
    SELECT id, name FROM public.organizations WHERE metadata->>'environment' = 'staging'
),
user_data AS (
    SELECT id, email FROM auth.users WHERE email LIKE '<EMAIL>'
)
INSERT INTO public.user_roles (
    id,
    user_id,
    organization_id,
    role,
    custom_permissions,
    created_at,
    updated_at
)
SELECT
    uuid_generate_v4(),
    u.id,
    CASE
        WHEN u.email = '<EMAIL>' THEN NULL -- System admin has no specific org
        WHEN u.email LIKE 'staging.hospital.%' THEN (SELECT id FROM org_data WHERE name = 'Staging Hospital')
        WHEN u.email LIKE 'staging.clinic.%' THEN (SELECT id FROM org_data WHERE name = 'Staging Clinic')
    END,
    CASE
        WHEN u.email = '<EMAIL>' THEN 'system_admin'
        WHEN u.email LIKE '%.admin@%' THEN 'org_admin'
        WHEN u.email LIKE '%.provider@%' THEN 'provider'
    END::user_role,
    CASE
        WHEN u.email = '<EMAIL>' THEN '{"all": true, "multi_org": true}'
        ELSE '{}'
    END::jsonb,
    NOW(),
    NOW()
FROM user_data u;

-- For the system admin, also add them to each organization
WITH org_data AS (
    SELECT id FROM public.organizations WHERE metadata->>'environment' = 'staging'
),
admin_user AS (
    SELECT id FROM auth.users WHERE email = '<EMAIL>'
)
INSERT INTO public.user_roles (
    id,
    user_id,
    organization_id,
    role,
    custom_permissions,
    created_at,
    updated_at
)
SELECT
    uuid_generate_v4(),
    admin_user.id,
    org_data.id,
    'system_admin'::user_role,
    '{"all": true}'::jsonb,
    NOW(),
    NOW()
FROM org_data, admin_user;

COMMIT;
