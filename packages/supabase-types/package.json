{"name": "@spritely/supabase-types", "version": "1.0.0", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "scripts": {"build": "tsup src/index.ts --format esm,cjs --dts", "dev": "tsup src/index.ts --format esm,cjs --watch --dts", "dev:no-types": "echo 'Using pre-generated types'", "lint": "eslint src/", "clean": "rm -rf dist"}, "keywords": [], "author": "", "license": "ISC", "description": "TypeScript types for Supabase database schema", "dependencies": {"@supabase/supabase-js": "^2.49.4"}, "devDependencies": {"tsup": "^8.4.0", "typescript": "^5.8.3"}}