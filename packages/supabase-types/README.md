# Supabase Types

This package contains TypeScript type definitions for the Supabase database schema.

## Overview

The types in this package are automatically generated from the Supabase database schema. They provide type safety when interacting with the database through the Supabase client.

## Usage

Import the types in your TypeScript files:

```typescript
import { Database } from "@spritely/supabase-types";

// Use the types with the Supabase client
const supabase = createClient<Database>(supabaseUrl, supabaseKey);

// Now you get type checking for your queries
const { data, error } = await supabase.from("organizations").select("*");
// data will be typed as Organization[]
```

## Generating Types

The types are generated from the local Supabase instance. To update the types:

1. Make sure your local Supabase instance is running:

   ```bash
   supabase start
   ```

2. Run the generate-types script from the root of the project:
   ```bash
   npm run generate-types
   ```

This will:

1. Connect to your local Supabase instance
2. Generate TypeScript types based on the current database schema
3. Save them to `src/generated-types.ts`
4. Build the package

## Important Notes

- Always regenerate types after making changes to the database schema
- The types reflect the schema of your local database, so make sure it's up to date
- If you're working with a remote database, you'll need to update the local schema first

## Troubleshooting

If you encounter issues generating types:

1. Make sure your local Supabase instance is running
2. Check that the database schema is properly applied
3. If needed, reset the database and apply the schema and seed data:

   ```bash
   # Apply schema
   psql postgresql://postgres:postgres@127.0.0.1:54322/postgres -f supabase/schema.sql

   # Apply seed data
   psql postgresql://postgres:postgres@127.0.0.1:54322/postgres -f supabase/seed.sql
   ```
