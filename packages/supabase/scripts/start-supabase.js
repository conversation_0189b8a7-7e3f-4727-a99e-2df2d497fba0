#!/usr/bin/env node

/**
 * This script starts Supabase for local development.
 * It's used by the Turborepo pipeline for the supabase:start task.
 */

const { execSync } = require('child_process');

// Simple colored console output functions since chalk might be ESM-only
const colors = {
  blue: (text) => `\x1b[34m${text}\x1b[0m`,
  green: (text) => `\x1b[32m${text}\x1b[0m`,
  yellow: (text) => `\x1b[33m${text}\x1b[0m`,
  red: (text) => `\x1b[31m${text}\x1b[0m`
};

console.log(colors.blue('🔄 Starting Supabase for local development...'));

// Check if Docker is running
try {
  try {
    execSync('docker info', { stdio: 'pipe' });
  } catch (error) {
    console.error(colors.red('❌ Docker is not running. Please start Docker first.'));
    console.log(colors.yellow('⚠️ Continuing with development server without Supabase...'));
    process.exit(0);
  }

  // Check if Supabase is already running
  try {
    const status = execSync('npx supabase status', { stdio: 'pipe' }).toString();
    if (status.includes('Started')) {
      console.log(colors.green('✅ Supabase is already running'));
      process.exit(0);
    }
  } catch (error) {
    // If status check fails, Supabase is probably not running
    console.log(colors.yellow('⚠️ Supabase is not running, starting it now...'));
  }

  // Start Supabase
  console.log(colors.blue('🚀 Starting Supabase containers...'));
  execSync('npx supabase start', { stdio: 'inherit' });
  console.log(colors.green('✅ Supabase started successfully'));
} catch (error) {
  console.error(colors.red('❌ Failed to start Supabase:'), error.message);
  console.log(colors.yellow('⚠️ Continuing with development server anyway...'));
  // Exit with success code to allow the dev server to start even if Supabase fails
  process.exit(0);
}
