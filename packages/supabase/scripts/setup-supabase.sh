#!/bin/bash

# <PERSON>ript to properly set up Supabase with schema and seed data
echo "🔄 Setting up Supabase..."

# Stop any running Supabase instances
echo "🛑 Stopping any running Supabase instances..."
supabase stop

# Reset Supabase to ensure a clean state
echo "🧹 Resetting Supabase..."
supabase db reset --no-backup

# Start Supabase with migrations only (no seed)
echo "🚀 Starting Supabase..."
supabase start

# Wait for Supabase to be ready
echo "⏳ Waiting for Supabase to be ready..."
sleep 15

# Apply schema
echo "📊 Applying database schema..."
psql postgresql://postgres:postgres@127.0.0.1:54322/postgres -f ../../supabase/schema.sql

# Apply seed data
echo "🌱 Seeding database..."
psql postgresql://postgres:postgres@127.0.0.1:54322/postgres -f ../../supabase/seed.sql

echo "✅ Supabase setup complete!"
echo "🔑 You can now log in with the following test users:"
echo "   - <EMAIL> / password123 (Admin)"
echo "   - <EMAIL> / password123 (Provider)"
echo "   - <EMAIL> / password123 (Nurse)"
echo "   - <EMAIL> / password123 (Staff)"
