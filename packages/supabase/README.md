# Spritely Supabase Integration

This package contains the Supabase client configuration and utilities for the Spritely healthcare management system.

## Overview

This package provides a centralized way to interact with Supabase services across the Spritely monorepo. It includes:

- Supabase client configuration
- Authentication utilities
- Database query helpers
- Storage utilities
- Real-time subscription helpers

## Usage

### Initializing the Supabase Client

```typescript
import { createClient } from "@supabase/supabase-js";
import { supabaseUrl, supabaseAnonKey } from "supabase/config";
import type { Database } from "supabase-types";

const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey);
```

### Authentication

```typescript
import { signIn, signOut, signUp, resetPassword } from "supabase/auth";

// Sign in a user
const { user, error } = await signIn({
  email: "<EMAIL>",
  password: "password123",
});

// Sign out
await signOut();
```

### Database Queries

```typescript
import { getPatients, getOrganizations, createAppointment } from "supabase/db";

// Get patients for the current user's organization
const patients = await getPatients();

// Create a new appointment
const appointment = await createAppointment({
  patientId: "123",
  providerId: "456",
  startTime: new Date(),
  endTime: new Date(Date.now() + 3600000),
  notes: "Regular checkup",
});
```

## Configuration

The package uses environment variables for configuration:

- `SUPABASE_URL`: The URL of your Supabase instance
- `SUPABASE_ANON_KEY`: The anonymous key for your Supabase instance
- `SUPABASE_SERVICE_KEY`: The service key for admin operations (optional)

In development, these are loaded from the local Supabase instance.

## Row Level Security (RLS)

This package works with the Row Level Security policies defined in the Supabase database to ensure proper data access control:

- Patient data is only accessible to users in the same organization
- Provider data is restricted based on organization membership
- Medical records are only accessible to authorized healthcare providers
- Administrative data is restricted to users with admin roles

## Real-time Subscriptions

```typescript
import {
  subscribeToAppointments,
  subscribeToPatients,
} from "supabase/realtime";

// Subscribe to appointment changes
const subscription = subscribeToAppointments((payload) => {
  console.log("Appointment updated:", payload);
});

// Unsubscribe when done
subscription.unsubscribe();
```

## Error Handling

The package provides consistent error handling for Supabase operations:

```typescript
import { handleSupabaseError } from "supabase/errors";

try {
  // Supabase operation
} catch (error) {
  const { message, code, details } = handleSupabaseError(error);
  console.error(`Error ${code}: ${message}`, details);
}
```

## Development

### Local Supabase Setup

For local development, make sure your Supabase instance is running:

```bash
# From the root of the monorepo
npm run supabase:setup
```

### Generating Types

After making changes to the database schema, regenerate the TypeScript types:

```bash
npm run generate-types
```

This will update the types in the `supabase-types` package.
