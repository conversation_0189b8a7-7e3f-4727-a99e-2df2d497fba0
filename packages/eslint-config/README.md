# Spritely ESLint Configuration

This package contains shared ESLint configurations for the Spritely monorepo.

## Overview

This package provides consistent linting rules across all packages and applications in the Spritely project. It includes configurations for:

- TypeScript linting
- React best practices
- Code formatting standards
- Import/export rules
- Accessibility (a11y) guidelines

## Usage

The ESLint configuration is automatically applied to all packages in the monorepo through the Turborepo setup.

If you need to reference it directly in a package:

```js
// eslint.config.js
import { baseConfig } from "eslint-config/base";

export default [
  ...baseConfig,
  // Additional package-specific rules can be added here
];
```

## Customizing Rules

If you need to override or extend rules for a specific package:

1. Create an `eslint.config.js` file in the package directory
2. Import the base configuration
3. Add your custom rules

Example:

```js
// apps/web/eslint.config.js
import { baseConfig } from "eslint-config/base";

export default [
  ...baseConfig,
  {
    files: ["**/*.tsx"],
    rules: {
      // Your custom rules here
      "react/prop-types": "off",
    },
  },
];
```

## Included Plugins

- typescript-eslint
- eslint-plugin-react
- eslint-plugin-react-hooks
- eslint-plugin-import
- eslint-plugin-jsx-a11y
