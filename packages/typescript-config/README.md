# Spritely TypeScript Configuration

This package contains shared TypeScript configurations for the Spritely monorepo.

## Overview

This package provides consistent TypeScript settings across all packages and applications in the Spritely project. It includes configurations for:

- Base TypeScript settings
- React-specific settings
- Node.js settings

## Usage

The TypeScript configurations are automatically applied to packages in the monorepo through the Turborepo setup.

Each package in the monorepo extends one of these base configurations in its `tsconfig.json` file:

```json
{
  "extends": "typescript-config/base.json",
  "compilerOptions": {
    // Package-specific overrides
  },
  "include": ["src/**/*"]
}
```

## Available Configurations

### Base Configuration (`base.json`)

The base configuration includes settings that apply to all TypeScript projects:

- Strict type checking
- Module resolution settings
- Source maps
- Common type definitions

### React Configuration (`react.json`)

Extends the base configuration with React-specific settings:

- JSX support
- DOM type definitions
- React-specific compiler options

### Node.js Configuration (`node.json`)

Extends the base configuration with Node.js-specific settings:

- CommonJS modules
- Node.js type definitions
- File system access

## Customizing Configurations

If you need to override or extend configurations for a specific package:

1. Extend the appropriate base configuration
2. Add your custom settings in the package's `tsconfig.json`

Example:

```json
{
  "extends": "typescript-config/react.json",
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
    },
    "types": ["vitest/globals"]
  },
  "include": ["src/**/*", "tests/**/*"]
}
```

## Best Practices

- Always extend one of the base configurations rather than creating a completely custom one
- Keep package-specific overrides minimal to maintain consistency
- If you find yourself overriding the same settings in multiple packages, consider updating the base configuration
- When adding new dependencies with TypeScript types, make sure to include them in the appropriate base configuration
