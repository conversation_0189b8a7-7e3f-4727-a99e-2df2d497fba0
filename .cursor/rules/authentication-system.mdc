---
description: 
globs: 
alwaysApply: false
---
# Authentication System

## **Key Files**
- [apps/web/src/contexts/AuthContext.tsx](mdc:apps/web/src/contexts/AuthContext.tsx) - Main auth state
- [apps/web/src/lib/auth/organization-service.ts](mdc:apps/web/src/lib/auth/organization-service.ts) - Organization fetching
- [apps/web/src/hooks/useUserRoles.ts](mdc:apps/web/src/hooks/useUserRoles.ts) - Role checking
- [apps/web/src/components/organization/OrganizationSelector.tsx](mdc:apps/web/src/components/organization/OrganizationSelector.tsx) - Org selector

## **User Types**
- **System Admin**: Sees "All Organizations" (`system-admin-no-org`), no org selectors
- **Org Admin**: Can manage their organization  
- **Regular Users**: Limited to assigned organization

## Auth Context
The main authentication logic is handled by [apps/web/src/contexts/AuthContext.tsx](mdc:apps/web/src/contexts/AuthContext.tsx), which provides:
- User authentication state
- Organization selection and switching
- Session management
- Auto-logout on session expiry

## Organization Management
- [apps/web/src/lib/auth/organization-cache-exports.ts](mdc:apps/web/src/lib/auth/organization-cache-exports.ts) - Caching layer for organizations
- [apps/web/src/lib/auth/organization-types.ts](mdc:apps/web/src/lib/auth/organization-types.ts) - TypeScript types

## Organization Selectors
- [apps/web/src/components/organization/OrganizationSelector.tsx](mdc:apps/web/src/components/organization/OrganizationSelector.tsx) - Main organization selector component (handles all user types)

## Route Protection
- [apps/web/src/components/navigation/NavigationManager.tsx](mdc:apps/web/src/components/navigation/NavigationManager.tsx) - Handles route protection, redirects, and navigation logic
- Supports dynamic routes like `/patients/:id` and `/organizations/:id`
- Prevents infinite redirects with safety mechanisms

## Key Auth Patterns
- All API calls use Supabase RLS for data isolation
- Organization switching updates context and localStorage cache
- System admins have special "All Organizations" view (`system-admin-no-org`) and never see organization selectors
- Navigation stays on current page when switching organizations (no unwanted redirects)
