---
description: 
globs: 
alwaysApply: false
---
# Development Workflow & Commands

## Getting Started
```bash
# Install dependencies
npm install

# Start development servers (web + mobile + supabase types)
npm run dev

# Start only web application
cd apps/web && npm run dev

# Generate TypeScript types from Supabase schema
npm run generate-types
```

## Code Quality
```bash
# TypeScript type checking
npx tsc --noEmit

# ESLint checking
npm run lint

# Build for production
npm run build
```

## Database Management
```bash
# Start local Supabase
supabase start

# Reset database
supabase db reset

# Apply migrations
supabase db push

# Generate types
supabase gen types typescript --local --schema public > packages/supabase-types/src/generated-types.ts
```

## Project Structure
- `apps/web/` - Main React application
- `apps/mobile/` - React Native mobile app
- `packages/supabase-types/` - Shared TypeScript types
- `supabase/` - Database schema and migrations

## Common Development Tasks

### Adding New Components
1. Create component in appropriate directory under `apps/web/src/components/`
2. Export from `apps/web/src/components/index.ts` if needed
3. Add to page exports in `apps/web/src/pages/index.ts` for pages

### Database Changes
1. Create migration: `supabase migration new migration_name`
2. Write SQL in the generated file
3. Apply: `supabase db push`
4. Regenerate types: `npm run generate-types`

### Fixing TypeScript Errors
1. Run `npx tsc --noEmit` to see all errors
2. Common fixes:
   - Add null checks: `{user && <Component />}`
   - Convert types: `!!value` for boolean conversion
   - Handle promises: `'key' in object` instead of `object[key]`
   - Add ESLint disables for necessary `any` usage

### Performance Optimization
1. Check network tab for multiple queries
2. Combine related data fetches into single queries
3. Use React.memo for expensive components
4. Implement proper loading states

## Debugging Tips
- Use browser dev tools Network tab to inspect Supabase queries
- Check console for RLS policy violations
- Use `console.debug` for development logging (removed in production)
- Test with different user roles (system admin, org admin, regular user)

# Development Workflow Rules

## **Frontend Restrictions**
- **NEVER start frontend apps** (`npm run dev`, `vite`, etc.) - assume already running on localhost:5173
- **Build commands OK**: `npm run build`, `npm run lint` for testing

## **Pre-Commit Checks**
- **ALWAYS run `npx tsc --noEmit`** before committing any feature to catch TypeScript errors
- **Run `npm run build`** to ensure production build works
- **Fix all TypeScript errors** before committing - never commit broken types

## **React Hook Patterns**
```typescript
// ✅ DO: Exclude dependencies that cause infinite loops
const loadData = useCallback(() => {
  // logic here
}, [userId]); // Don't include authState if it updates from this function

// ❌ DON'T: Blindly fix ESLint dependency warnings
const loadData = useCallback(() => {
  // logic here  
}, [userId, authState]); // This causes infinite loops if loadData updates authState
```

## **System Admin Patterns**
```typescript
// ✅ DO: Handle virtual organization IDs
if (orgId === "system-admin-no-org") {
  return cachedVirtualOrg; // Skip database validation
}

// ✅ DO: Use constants for virtual facility IDs
import { ALL_FACILITIES_ID } from "@/types/facility";
if (facilityId === ALL_FACILITIES_ID) {
  // Don't filter by facility - show all facilities
}

// ❌ DON'T: Validate virtual IDs against database
const { data } = await supabase.from("organizations").select("*").eq("id", "system-admin-no-org");

// ❌ DON'T: Use hardcoded strings for virtual facility IDs
if (facilityId === "system-admin-all-facilities") { // Use ALL_FACILITIES_ID instead
```

## **Component Cleanup**
- **Delete entire directories** when removing components, not just main files
- **Update all imports** after component deletion
- **Clear build cache** (`rm tsconfig.tsbuildinfo`) after major deletions

## **Production System Admin Patterns**

```typescript
// ✅ DO: Implement scoped system admin access with audit trails
interface SystemAdminContext {
  adminUserId: string;
  impersonatedOrgId?: string; // Which org they're currently viewing
  auditTrail: boolean; // Always log system admin actions
  restrictedActions: string[]; // Actions that require additional approval
}

// ✅ DO: Use database functions for system admin access
const { data } = await supabase.rpc('get_system_admin_data', {
  admin_user_id: user.id,
  target_org_id: selectedOrgId,
  action_type: 'view_patients'
});

// ❌ DON'T: Give system admins unrestricted access
const { data } = await supabase.from('patients').select('*'); // No restrictions!
```

### **Database-Level System Admin Controls**

```sql
-- ✅ DO: Create scoped system admin functions
CREATE OR REPLACE FUNCTION get_system_admin_organizations(admin_user_id UUID)
RETURNS TABLE(org_id UUID, org_name TEXT, access_level TEXT)
SECURITY DEFINER
AS $$
BEGIN
  -- Log the access attempt
  INSERT INTO system_admin_audit_log (admin_id, action, timestamp)
  VALUES (admin_user_id, 'list_organizations', NOW());
  
  -- Return organizations with access levels
  RETURN QUERY
  SELECT o.id, o.name, 'full_access'::TEXT
  FROM organizations o
  WHERE EXISTS (
    SELECT 1 FROM user_roles ur 
    WHERE ur.user_id = admin_user_id 
    AND ur.role = 'system_admin'
  );
END;
$$ LANGUAGE plpgsql;

-- ❌ DON'T: Use blanket RLS bypasses for system admins
CREATE POLICY "system_admin_bypass" ON patients 
USING (EXISTS (SELECT 1 FROM user_roles WHERE user_id = auth.uid() AND role = 'system_admin'));
```

### **Facility-Level RLS Policies**

```sql
-- ✅ DO: Implement facility-aware RLS at database level
CREATE POLICY "facility_scoped_appointments" ON appointments
FOR SELECT USING (
  CASE 
    WHEN auth.jwt() ->> 'selected_facility_id' = 'system-admin-all-facilities' THEN
      -- System admin viewing all facilities - check organization access
      EXISTS (
        SELECT 1 FROM user_roles ur 
        JOIN departments d ON d.organization_id = ur.organization_id
        WHERE ur.user_id = auth.uid() 
        AND d.id = appointments.department_id
        AND ur.role IN ('system_admin', 'org_admin')
      )
    ELSE
      -- Regular facility-scoped access
      EXISTS (
        SELECT 1 FROM user_roles ur
        JOIN departments d ON d.facility_id = auth.jwt() ->> 'selected_facility_id'::UUID
        WHERE ur.user_id = auth.uid()
        AND d.id = appointments.department_id
      )
  END
);
```
