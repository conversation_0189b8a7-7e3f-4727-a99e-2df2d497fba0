---
description: 
globs: 
alwaysApply: false
---
# TypeScript Patterns

## **Database Types**
```typescript
// Import generated types
import { Database } from '@spritely/supabase-types';

// Table row types
type PatientRow = Database['public']['Tables']['patients']['Row'];

// Extended types with computed fields
interface PatientWithStats extends PatientRow {
  age: number;
  full_name: string;
}
```

## **Null Safety**
```typescript
// ✅ DO: Handle nulls from database
{user && <Component user={user} />}
const orgId = patient.organization_id || '';
const date = new Date(activity.created_at || new Date().toISOString());

// ✅ DO: Convert to boolean
const isActive = !!value;

// ✅ DO: Check object properties safely
if ('key' in object) { /* ... */ }
```

## **Error Handling**
```typescript
// ✅ DO: Type catch blocks
try {
  // ...
} catch (err: unknown) {
  const error = err instanceof Error ? err : new Error('Unknown error');
}
```

## **Type Assertion Guidelines**
- Use `any` sparingly with ESLint disable comments
- Prefer type guards over assertions when possible
- Document complex type assertions with comments
- Example: `// eslint-disable-next-line @typescript-eslint/no-explicit-any`

## **Common Type Issues & Solutions**
1. **Boolean vs Boolean | null**: Use `!!value` to convert to boolean
2. **Promise conditions**: Use `'key' in object` instead of `object[key]` for Promise checks
3. **Ref assignments**: Use `useRef<Type | null>(null)` for mutable refs
4. **Supabase joins**: Complex joins may require `any` with proper comments

## **Hook Return Types**
```typescript
// Consistent hook return pattern
return {
  data: patients,
  isLoading,
  error,
  refetch
};
```
