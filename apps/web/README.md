# Spritely Web Application

This is the web frontend for the Spritely healthcare management system, built with React, TypeScript, and Vite.

## Features

- **Authentication**: User login, signup, and session management using Supabase Auth
- **Organization Management**: Create and manage healthcare organizations
- **Patient Management**: Track patient information and medical history
- **Appointment Scheduling**: Schedule and manage patient appointments
- **Medical Records**: Create and view patient medical records
- **User Role Management**: Different access levels based on user roles (admin, provider, nurse, staff)

## Tech Stack

- **Framework**: React with TypeScript
- **Build Tool**: Vite
- **Styling**: Tailwind CSS
- **State Management**: React Query and Context API
- **Authentication**: Supabase Auth
- **Database**: Supabase PostgreSQL
- **Form Handling**: React Hook Form
- **UI Components**: Shadcn UI

## Development

### Environment Setup

Create a `.env.local` file in this directory with the following variables:

```
VITE_SUPABASE_URL=http://127.0.0.1:54321
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0
```

### Running the Development Server

From the root of the monorepo:

```bash
# Start just the web app
npm run dev

# Start with Supabase (recommended)
npm run dev:full
```

Or from this directory:

```bash
npm run dev
```

### Building for Production

```bash
npm run build
```

## Project Structure

- `src/components/` - Reusable UI components
- `src/features/` - Feature-specific components and logic
- `src/hooks/` - Custom React hooks
- `src/lib/` - Utility functions and configuration
- `src/pages/` - Page components
- `src/routes/` - Routing configuration
- `src/types/` - TypeScript type definitions
- `src/utils/` - Helper functions

## Authentication Flow

The application implements the following authentication flow:

1. Users sign in at `/login` with their email and password
2. After successful authentication:
   - If the user has no organization, they are redirected to `/setup` to create one
   - If the user has an organization, they are redirected to `/dashboard`
3. Protected routes require authentication and redirect to login if not authenticated
