{"extends": "@repo/typescript-config/vite-react.json", "compilerOptions": {"baseUrl": ".", "paths": {"@/*": ["./src/*"], "@spritely/*": ["../../packages/*/src"]}, "forceConsistentCasingInFileNames": true, "skipLibCheck": true, "noEmit": true, "allowSyntheticDefaultImports": true, "jsx": "react-jsx", "esModuleInterop": true, "noUnusedLocals": false, "noUnusedParameters": false, "strictNullChecks": false, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "module": "ESNext"}, "include": ["src/**/*.ts", "src/**/*.tsx"], "exclude": ["node_modules"]}