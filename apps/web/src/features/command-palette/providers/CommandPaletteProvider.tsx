import { KeyBinding, useShortcuts } from "@/lib/shortcuts";
import React, {
  ReactNode,
  createContext,
  useCallback,
  useContext,
  useMemo,
  useReducer,
} from "react";

interface CommandAction {
  id: string;
  title: string;
  description: string;
  category: string;
  shortcut?: KeyBinding;
  icon?: React.ReactNode;
  handler: () => void;
}

interface CommandPaletteState {
  isOpen: boolean;
  searchQuery: string;
  selectedIndex: number;
  filteredActions: CommandAction[];
}

interface CommandPaletteContextType {
  state: CommandPaletteState;
  actions: CommandAction[];
  registerAction: (action: CommandAction) => void;
  unregisterAction: (actionId: string) => void;
  openPalette: () => void;
  closePalette: () => void;
  executeAction: (actionId: string) => void;
}

// Initial state for the command palette
const initialState: CommandPaletteState = {
  isOpen: false,
  searchQuery: "",
  selectedIndex: 0,
  filteredActions: [],
};

// Action types for the reducer
type ActionType =
  | { type: "OPEN_PALETTE" }
  | { type: "CLOSE_PALETTE" }
  | { type: "SET_SEARCH_QUERY"; payload: string }
  | { type: "SET_SELECTED_INDEX"; payload: number }
  | { type: "SET_FILTERED_ACTIONS"; payload: CommandAction[] };

// Create the context
const CommandPaletteContext = createContext<CommandPaletteContextType | null>(
  null,
);

// Reducer function
const reducer = (
  state: CommandPaletteState,
  action: ActionType,
): CommandPaletteState => {
  switch (action.type) {
    case "OPEN_PALETTE":
      return {
        ...state,
        isOpen: true,
        searchQuery: "",
        selectedIndex: 0,
      };
    case "CLOSE_PALETTE":
      return {
        ...state,
        isOpen: false,
        searchQuery: "",
        selectedIndex: 0,
        filteredActions: [],
      };
    case "SET_SEARCH_QUERY":
      return {
        ...state,
        searchQuery: action.payload,
        selectedIndex: 0,
      };
    case "SET_SELECTED_INDEX":
      return {
        ...state,
        selectedIndex: action.payload,
      };
    case "SET_FILTERED_ACTIONS":
      return {
        ...state,
        filteredActions: action.payload,
      };
    default:
      return state;
  }
};

interface CommandPaletteProviderProps {
  children: ReactNode;
}

export const CommandPaletteProvider: React.FC<CommandPaletteProviderProps> = ({
  children,
}) => {
  const [state, dispatch] = useReducer(reducer, initialState);
  const [actions, setActions] = React.useState<CommandAction[]>([]);
  const { registerShortcut, unregisterShortcut } = useShortcuts();

  // Register a new action
  const registerAction = useCallback(
    (action: CommandAction) => {
      setActions((prev) => {
        const exists = prev.some((a) => a.id === action.id);
        if (exists) return prev;
        if (action.shortcut) {
          registerShortcut(action.id, {
            ...action.shortcut,
            action: action.id,
          });
        }
        return [...prev, action];
      });
    },
    [registerShortcut],
  );

  // Unregister an action
  const unregisterAction = useCallback(
    (actionId: string) => {
      setActions((prev) => {
        const action = prev.find((a) => a.id === actionId);
        if (action?.shortcut) {
          unregisterShortcut(actionId);
        }
        return prev.filter((action) => action.id !== actionId);
      });
    },
    [unregisterShortcut],
  );

  // Open the command palette
  const openPalette = useCallback(() => {
    dispatch({ type: "OPEN_PALETTE" });
  }, []);

  // Close the command palette
  const closePalette = useCallback(() => {
    dispatch({ type: "CLOSE_PALETTE" });
  }, []);

  // Execute an action
  const executeAction = useCallback(
    (actionId: string) => {
      const action = actions.find((a) => a.id === actionId);
      if (action) {
        action.handler();
        closePalette();
      }
    },
    [actions, closePalette],
  );

  // Filter actions based on search query
  const filterActions = useCallback(
    (query: string) => {
      const normalizedQuery = query.toLowerCase();
      return actions.filter((action) => {
        const matchTitle = action.title.toLowerCase().includes(normalizedQuery);
        const matchDescription = action.description
          .toLowerCase()
          .includes(normalizedQuery);
        const matchCategory = action.category
          .toLowerCase()
          .includes(normalizedQuery);
        return matchTitle || matchDescription || matchCategory;
      });
    },
    [actions],
  );

  // Update filtered actions when search query changes
  React.useEffect(() => {
    const filtered = filterActions(state.searchQuery);
    dispatch({ type: "SET_FILTERED_ACTIONS", payload: filtered });
  }, [state.searchQuery, filterActions]);

  // Set up keyboard shortcuts for the command palette
  React.useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (state.isOpen) {
        switch (event.key) {
          case "Escape":
            event.preventDefault();
            closePalette();
            break;
          case "ArrowUp":
            event.preventDefault();
            dispatch({
              type: "SET_SELECTED_INDEX",
              payload: Math.max(0, state.selectedIndex - 1),
            });
            break;
          case "ArrowDown":
            event.preventDefault();
            dispatch({
              type: "SET_SELECTED_INDEX",
              payload: Math.min(
                state.filteredActions.length - 1,
                state.selectedIndex + 1,
              ),
            });
            break;
          case "Enter":
            event.preventDefault();
            if (state.filteredActions[state.selectedIndex]) {
              executeAction(state.filteredActions[state.selectedIndex].id);
            }
            break;
        }
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [
    state.isOpen,
    state.selectedIndex,
    state.filteredActions,
    closePalette,
    executeAction,
  ]);

  // Listen for shortcut events
  React.useEffect(() => {
    const handleShortcut = (
      event: CustomEvent<{ action: string; id: string }>,
    ) => {
      const { id } = event.detail;
      if (id === "app.command_palette") {
        openPalette();
      } else {
        executeAction(id);
      }
    };

    document.addEventListener(
      "shortcut-triggered",
      handleShortcut as EventListener,
    );
    return () =>
      document.removeEventListener(
        "shortcut-triggered",
        handleShortcut as EventListener,
      );
  }, [openPalette, executeAction]);

  // Register command palette shortcut
  React.useEffect(() => {
    registerShortcut("app.command_palette", {
      key: "k",
      modifiers: ["meta"],
      description: "Open command palette",
      action: "app.command_palette",
    });

    return () => unregisterShortcut("app.command_palette");
  }, [registerShortcut, unregisterShortcut]);

  const contextValue = useMemo(
    () => ({
      state,
      actions,
      registerAction,
      unregisterAction,
      openPalette,
      closePalette,
      executeAction,
    }),
    [
      state,
      actions,
      registerAction,
      unregisterAction,
      openPalette,
      closePalette,
      executeAction,
    ],
  );

  return (
    <CommandPaletteContext.Provider value={contextValue}>
      {children}
    </CommandPaletteContext.Provider>
  );
};

// Custom hook to use the command palette context
export const useCommandPalette = () => {
  const context = useContext(CommandPaletteContext);
  if (!context) {
    throw new Error(
      "useCommandPalette must be used within a CommandPaletteProvider",
    );
  }
  return context;
};
