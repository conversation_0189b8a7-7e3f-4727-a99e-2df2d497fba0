export type ModifierKey = "⌘" | "Ctrl" | "⇧" | "⌥";

export interface ShortcutCombo {
  modifiers: Modifier<PERSON><PERSON>[];
  key: string;
}

export interface CommandAction {
  id: string;
  title: string;
  description: string;
  category: string;
  shortcut?: ShortcutCombo;
  icon?: React.ReactNode;
  handler: () => void;
}

export interface CustomBinding {
  actionId: string;
  shortcut: ShortcutCombo;
}

export interface ShortcutConflict {
  shortcut: ShortcutCombo;
  actions: CommandAction[];
}

export interface ShortcutManagerConfig {
  detectSystemConflicts?: boolean;
  persistPreferences?: boolean;
  storageKey?: string;
  platform?: "mac" | "windows" | "linux";
}
