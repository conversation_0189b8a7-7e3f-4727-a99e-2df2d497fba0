import { Organization } from "@/contexts/auth-context-types";
import secureStorage from "@/lib/secure-storage";
import { getUserOrganizations, isUserSystemAdmin } from "@/services/organization-service";
import { User } from "@supabase/supabase-js";
import { create } from "zustand";

interface OrganizationState {
  // Current state
  currentOrg: Organization | null;
  availableOrgs: Organization[];
  isSystemAdmin: boolean;
  isLoading: boolean;
  error: string | null;

  // Actions
  setCurrentOrg: (org: Organization, updateUrl?: boolean) => void;
  loadUserOrganizations: (user: User) => Promise<void>;
  switchOrganization: (orgId: string, updateUrl?: boolean) => Promise<void>;
  restoreFromUrl: (orgId: string) => Promise<void>;
  clearCache: () => void;
}

const CACHE_KEY = "user_organizations_v2"; // v2 to force cache refresh with hierarchy data
const CACHE_DURATION = 24 * 60 * 60 * 1000; // 24 hours

export const useOrganizationStore = create<OrganizationState>((set, get) => ({
  currentOrg: null,
  availableOrgs: [],
  isSystemAdmin: false,
  isLoading: false,
  error: null,

  setCurrentOrg: (org: Organization, updateUrl = true) => {
    set({ currentOrg: org });
    // Cache the selection
    secureStorage.setOrganizationData("current_org", org.id, CACHE_DURATION);

    // Update URL parameters if requested
    if (updateUrl && typeof window !== 'undefined') {
      const url = new URL(window.location.href);
      if (org.id === "system-admin-all-orgs") {
        url.searchParams.set("org", "all");
      } else {
        url.searchParams.set("org", org.id);
      }
      window.history.replaceState({}, '', url.toString());
    }
  },

  loadUserOrganizations: async (user: User) => {
    set({ isLoading: true, error: null });

    try {
      // Check cache first
      const cached = secureStorage.getOrganizationData<{
        orgs: Organization[];
        currentOrgId: string;
        isSystemAdmin: boolean;
        timestamp: number;
      }>(CACHE_KEY);

      if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
        const currentOrg = cached.orgs.find((org: Organization) => org.id === cached.currentOrgId);
        set({
          availableOrgs: cached.orgs,
          currentOrg: currentOrg || cached.orgs[0],
          isSystemAdmin: cached.isSystemAdmin,
          isLoading: false
        });
        return;
      }

      // Fetch from API using the service
      const organizations = await getUserOrganizations(user);
      const systemAdmin = await isUserSystemAdmin(user.id);

      // Debug log to see what organizations we're getting
      console.log("🏥 Organizations loaded:", organizations.map(org => ({
        name: org.name,
        type: org.type,
        hierarchy_level: org.hierarchy_level,
        parent_id: org.parent_id
      })));

      // Get current org from cache or default appropriately
      const cachedCurrentOrgId = secureStorage.getOrganizationData<string>("current_org");
      let currentOrg: Organization | undefined;

      if (cachedCurrentOrgId) {
        currentOrg = organizations.find((org: Organization) => org.id === cachedCurrentOrgId);
        console.log('[ORG_STORE] Restored organization from cache:', currentOrg?.name, cachedCurrentOrgId);
      }

      // If no cached org or cached org not found, use appropriate default
      if (!currentOrg) {
        if (systemAdmin) {
          // For system admins, default to "All Organizations" view
          currentOrg = organizations.find((org: Organization) => org.id === "system-admin-all-orgs") || organizations[0];
          console.log('[ORG_STORE] Using default system admin org:', currentOrg?.name);
        } else {
          // For regular users, default to first organization
          currentOrg = organizations[0];
          console.log('[ORG_STORE] Using default user org:', currentOrg?.name);
        }
      }

      // Cache the results
      secureStorage.setOrganizationData(CACHE_KEY, {
        orgs: organizations,
        currentOrgId: currentOrg?.id,
        isSystemAdmin: systemAdmin,
        timestamp: Date.now()
      }, CACHE_DURATION);

      set({
        availableOrgs: organizations,
        currentOrg,
        isSystemAdmin: systemAdmin,
        isLoading: false
      });

    } catch (err) {
      console.error("Failed to load organizations:", err);
      set({ isLoading: false, error: "Failed to load organizations" });
    }
  },

  switchOrganization: async (orgId: string, updateUrl = true) => {
    const { availableOrgs } = get();
    const org = availableOrgs.find(o => o.id === orgId);
    if (org) {
      get().setCurrentOrg(org, updateUrl);
    }
  },

  restoreFromUrl: async (orgId: string) => {
    const { availableOrgs } = get();
    const org = availableOrgs.find(o => o.id === orgId);
    if (org) {
      // Don't update URL when restoring from URL to avoid loops
      get().setCurrentOrg(org, false);
    }
  },

  clearCache: () => {
    secureStorage.remove(CACHE_KEY);
    secureStorage.remove("current_org");
    // Also clear the request deduplication cache
    import("@/lib/request-deduplication").then(({ requestDeduplicator }) => {
      requestDeduplicator.clearCache("organizations:*");
    });
    set({ currentOrg: null, availableOrgs: [], isSystemAdmin: false, error: null });
  }
}));