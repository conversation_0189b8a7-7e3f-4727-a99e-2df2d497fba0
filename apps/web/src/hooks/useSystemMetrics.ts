import { useAuth } from '@/hooks/useAuth';
import { supabase } from '@/lib/supabase';
import {
    SystemMetrics,
    SystemMetricsQuery
} from '@/types/metrics';
import { useQuery } from '@tanstack/react-query';

// Type definitions for organization data with joins
interface OrganizationInfo {
  id: string;
  name: string;
}

interface PatientWithOrg {
  id: string;
  organization_id: string | null;
  created_at: string | null;
  organizations: OrganizationInfo | null;
}

interface AppointmentWithOrg {
  id: string;
  status: string;
  appointment_date: string;
  organization_id: string | null;
  created_at: string | null;
  organizations: OrganizationInfo | null;
}

interface UserRoleWithOrg {
  user_id: string;
  organization_id: string | null;
  role: string;
  organizations: OrganizationInfo | null;
}

// Accumulator types for reduce operations
type PatientsByOrgAcc = Record<string, { organizationId: string; organizationName: string; patientCount: number }>;
type AppointmentsByOrgAcc = Record<string, { organizationId: string; organizationName: string; appointmentCount: number }>;
type UsersByOrgAcc = Record<string, { organizationId: string; organizationName: string; userCount: number; activeUserCount: number }>;

/**
 * Fetch system metrics from Supabase database
 */
const fetchSystemMetrics = async (): Promise<SystemMetrics> => {
  try {
    // Fetch organizations data
    const { data: organizations, error: orgError } = await supabase
      .from('organizations')
      .select('id, name, type, created_at');

    if (orgError) throw orgError;

    // Fetch patients data with organization info
    const { data: patients, error: patientsError } = await supabase
      .from('patients')
      .select(`
        id,
        organization_id,
        created_at,
        organizations!inner(id, name)
      `);

    if (patientsError) throw patientsError;

    // Fetch appointments data (if any exist)
    const { data: appointments, error: appointmentsError } = await supabase
      .from('appointments')
      .select(`
        id,
        status,
        appointment_date,
        organization_id,
        created_at,
        organizations!inner(id, name)
      `);

    if (appointmentsError) throw appointmentsError;

    // Fetch user roles for user counts
    const { data: userRoles, error: userRolesError } = await supabase
      .from('user_roles')
      .select(`
        user_id,
        organization_id,
        role,
        organizations!inner(id, name)
      `);

    if (userRolesError) throw userRolesError;

    // Fetch activity logs for alerts/activity
    const { data: activityLogs, error: activityError } = await supabase
      .from('activity_logs')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(50);

    if (activityError) throw activityError;

    // Process organizations metrics
    const totalOrgs = organizations?.length || 0;
    const recentlyCreated = organizations?.filter(org => {
      const createdAt = new Date(org.created_at || '');
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      return createdAt > thirtyDaysAgo;
    }).length || 0;

    // Process patients metrics
    const totalPatients = patients?.length || 0;
    const recentPatients = patients?.filter(patient => {
      const createdAt = new Date(patient.created_at || '');
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      return createdAt > thirtyDaysAgo;
    }).length || 0;

    // Group patients by organization
    const patientsByOrg = (patients as PatientWithOrg[])?.reduce((acc, patient) => {
      const orgId = patient.organization_id;
      const orgName = patient.organizations?.name || 'Unknown';

      // Skip patients without organization_id
      if (!orgId) return acc;

      if (!acc[orgId]) {
        acc[orgId] = { organizationId: orgId, organizationName: orgName, patientCount: 0 };
      }
      acc[orgId].patientCount++;
      return acc;
    }, {} as PatientsByOrgAcc) || {};

    // Process appointments metrics
    const totalAppointments = appointments?.length || 0;
    const today = new Date().toISOString().split('T')[0];
    const todayAppointments = appointments?.filter(apt =>
      apt.appointment_date?.startsWith(today)
    ).length || 0;

    const appointmentsByStatus = appointments?.reduce((acc, apt) => {
      acc[apt.status || 'unknown'] = (acc[apt.status || 'unknown'] || 0) + 1;
      return acc;
    }, {} as Record<string, number>) || {};

    // Group appointments by organization
    const appointmentsByOrg = (appointments as AppointmentWithOrg[])?.reduce((acc, appointment) => {
      const orgId = appointment.organization_id;
      const orgName = appointment.organizations?.name || 'Unknown';

      // Skip appointments without organization_id
      if (!orgId) return acc;

      if (!acc[orgId]) {
        acc[orgId] = { organizationId: orgId, organizationName: orgName, appointmentCount: 0 };
      }
      acc[orgId].appointmentCount++;
      return acc;
    }, {} as AppointmentsByOrgAcc) || {};

    // Process user metrics
    const totalUsers = userRoles?.length || 0;
    const usersByOrg = (userRoles as UserRoleWithOrg[])?.reduce((acc, userRole) => {
      const orgId = userRole.organization_id;
      const orgName = userRole.organizations?.name || 'Unknown';

      // Skip user roles without organization_id
      if (!orgId) return acc;

      if (!acc[orgId]) {
        acc[orgId] = {
          organizationId: orgId,
          organizationName: orgName,
          userCount: 0,
          activeUserCount: 0
        };
      }
      acc[orgId].userCount++;
      acc[orgId].activeUserCount++; // Assume all users are active for now
      return acc;
    }, {} as UsersByOrgAcc) || {};

    // Generate some mock alerts based on activity
    const recentActivity = activityLogs?.slice(0, 10) || [];
    const alerts = {
      critical: 0,
      warning: Math.min(recentActivity.length, 3),
      info: Math.min(recentActivity.length, 8),
      total: Math.min(recentActivity.length, 11),
      recentAlerts: recentActivity.slice(0, 3).map((activity, index) => ({
        id: activity.id,
        type: index === 0 ? 'warning' : 'info' as 'critical' | 'warning' | 'info',
        message: `${activity.action_type} activity in ${activity.resource_type}`,
        organizationId: activity.organization_id,
        timestamp: activity.created_at || new Date().toISOString(),
      })),
    };

    const metrics: SystemMetrics = {
      organizations: {
        total: totalOrgs,
        active: totalOrgs, // Assume all are active
        inactive: 0,
        recentlyCreated,
      },
      patients: {
        total: totalPatients,
        activePatients: totalPatients, // Assume all are active
        newPatients: recentPatients,
        byOrganization: Object.values(patientsByOrg),
      },
      appointments: {
        total: totalAppointments,
        scheduled: appointmentsByStatus.scheduled || 0,
        completed: appointmentsByStatus.completed || 0,
        cancelled: appointmentsByStatus.cancelled || 0,
        upcoming: appointmentsByStatus.scheduled || 0,
        todayCount: todayAppointments,
        byOrganization: Object.values(appointmentsByOrg),
      },
      alerts,
      users: {
        totalUsers,
        activeUsers: totalUsers, // Assume all are active
        newUsers: 0, // Would need to track user creation dates
        currentSessions: Math.floor(totalUsers * 0.3), // Estimate 30% are currently active
        byOrganization: Object.values(usersByOrg),
      },
      performance: {
        uptime: 99.9,
        responseTime: 150,
        errorRate: 0.1,
        databaseConnections: 15,
        memoryUsage: 45,
        cpuUsage: 25,
      },
      lastUpdated: new Date().toISOString(),
    };

    return metrics;
  } catch (error) {
    console.error('Error fetching system metrics:', error);
    throw new Error('Failed to fetch system metrics');
  }
};

/**
 * Custom hook for fetching and managing system-wide metrics
 *
 * Features:
 * - React Query integration for caching and background refetching
 * - Automatic refetching every 5 minutes
 * - Error handling and loading states
 * - Only fetches data for system admins viewing "All Organizations"
 *
 * @param query - Optional query parameters for filtering metrics
 * @returns Object containing metrics data, loading state, error state, and refetch function
 */
export function useSystemMetrics(query?: SystemMetricsQuery) {
  const { organization } = useAuth();

  // Only fetch metrics for system admin view
  const isSystemAdminView = organization?.id === "system-admin-all-orgs";

  const {
    data: metrics,
    isLoading,
    isError,
    error,
    refetch,
    isFetching,
    isStale,
  } = useQuery({
    queryKey: ['systemMetrics', query],
    queryFn: () => fetchSystemMetrics(),
    enabled: isSystemAdminView, // Only run query for system admin view
    refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes
    refetchIntervalInBackground: true, // Continue refetching in background
    staleTime: 2 * 60 * 1000, // Data is fresh for 2 minutes
    gcTime: 10 * 60 * 1000, // Keep in cache for 10 minutes
  });

  return {
    // Data
    metrics,

    // Loading states
    isLoading,
    isFetching,
    isStale,

    // Error states
    isError,
    error: error as Error | null,

    // Actions
    refetch,

    // Computed values
    hasData: !!metrics,
    isSystemAdminView,
  };
}

/**
 * Hook for fetching specific metric categories
 * Useful when you only need a subset of metrics
 */
export function useSystemMetricsCategory<T extends keyof SystemMetrics>(
  category: T,
  query?: SystemMetricsQuery
) {
  const { metrics, ...rest } = useSystemMetrics(query);

  return {
    data: metrics?.[category],
    ...rest,
  };
}

/**
 * Hook for getting real-time system alerts
 * Refetches more frequently than other metrics
 */
export function useSystemAlerts(query?: SystemMetricsQuery) {
  const { organization } = useAuth();
  const isSystemAdminView = organization?.id === "system-admin-all-orgs";

  const {
    data: alerts,
    isLoading,
    isError,
    error,
    refetch,
  } = useQuery({
    queryKey: ['systemAlerts', query],
    queryFn: async () => {
      const metrics = await fetchSystemMetrics();
      return metrics.alerts;
    },
    enabled: isSystemAdminView,
    refetchInterval: 30 * 1000, // Refetch every 30 seconds for alerts
    refetchIntervalInBackground: true,
    staleTime: 15 * 1000, // Data is fresh for 15 seconds
  });

  return {
    alerts,
    isLoading,
    isError,
    error: error as Error | null,
    refetch,
    hasAlerts: !!alerts && alerts.total > 0,
    criticalCount: alerts?.critical || 0,
    warningCount: alerts?.warning || 0,
    infoCount: alerts?.info || 0,
  };
}