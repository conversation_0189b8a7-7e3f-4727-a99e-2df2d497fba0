import { useAuth } from "@/hooks/useAuth";
import { useUserRoles } from "@/hooks/useUserRoles";
import { supabase } from "@/lib/supabase";
import { Database } from "@spritely/supabase-types";
import { useCallback, useEffect, useRef, useState } from "react";

type OrganizationRow = Database["public"]["Tables"]["organizations"]["Row"];

export interface OrganizationWithStats {
  id: string;
  name: string;
  type: string;
  settings: Record<string, unknown>;
  created_at: string | null;
  updated_at: string | null;
  parent_id: string | null;
  hierarchy_level: number | null;
  hierarchy_path: string | null;
  user_count: number;
  patient_count: number;
  user_role?: string;
  is_current?: boolean;
  children?: OrganizationWithStats[];
  subscription_tier?: string | null;
}

export interface OrganizationFilters {
  search?: string;
}

export interface UseOrganizationsOptions {
  limit?: number;
  offset?: number;
  searchTerm?: string;
}

export function useOrganizations(options: UseOrganizationsOptions = {}) {
  const { organization } = useAuth();
  const { isSystemAdmin, isLoading: rolesLoading, roles } = useUserRoles();
  const [organizations, setOrganizations] = useState<OrganizationWithStats[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [totalCount, setTotalCount] = useState(0);

  // Track the last processed values to prevent unnecessary fetches
  const lastProcessedRef = useRef<{
    organizationId?: string;
    limit?: number;
    offset?: number;
    searchTerm?: string;
  }>({});

  const {
    limit = 50,
    offset = 0,
    searchTerm = "",
  } = options;

  const buildHierarchicalList = useCallback((orgs: OrganizationWithStats[]) => {
    // Create a map for quick lookup
    const orgMap = new Map(orgs.map(org => [org.id, { ...org, children: [] as OrganizationWithStats[] }]));

    // Build the tree structure
    const roots: OrganizationWithStats[] = [];

    orgs.forEach(org => {
      const orgWithChildren = orgMap.get(org.id)!;

      if (org.parent_id && orgMap.has(org.parent_id)) {
        // Add to parent's children
        const parent = orgMap.get(org.parent_id)!;
        parent.children!.push(orgWithChildren);
      } else {
        // This is a root organization
        roots.push(orgWithChildren);
      }
    });

    // Flatten the tree in hierarchical order
    const flattenHierarchy = (nodes: OrganizationWithStats[], level = 0): OrganizationWithStats[] => {
      const result: OrganizationWithStats[] = [];

      // Sort nodes at current level by name
      const sortedNodes = [...nodes].sort((a, b) => a.name.localeCompare(b.name));

      for (const node of sortedNodes) {
        // Add the node itself
        result.push({ ...node, hierarchy_level: level });

        // Add its children recursively
        if (node.children && node.children.length > 0) {
          result.push(...flattenHierarchy(node.children, level + 1));
        }
      }

      return result;
    };

    return flattenHierarchy(roots);
  }, []);

  const fetchOrganizations = useCallback(async () => {
    if (!organization) {
      setOrganizations([]);
      setIsLoading(false);
      return;
    }

    // Wait for user roles to load before proceeding with organization checks
    // Always wait if roles are still loading or if roles array is empty
    if (rolesLoading || roles.length === 0) {
      return;
    }

    // Check if we need to process (values have changed)
    const lastProcessed = lastProcessedRef.current;
    const hasChanged =
      lastProcessed.organizationId !== organization.id ||
      lastProcessed.limit !== limit ||
      lastProcessed.offset !== offset ||
      lastProcessed.searchTerm !== searchTerm;

    // Update the ref immediately to prevent duplicate processing
    lastProcessedRef.current = {
      organizationId: organization.id,
      limit,
      offset,
      searchTerm,
    };

    if (!hasChanged) {
      return; // No changes, skip processing
    }

    setIsLoading(true);
    setError(null);

    try {
      if (isSystemAdmin) {
        // System admins see all organizations
        // First get total count for pagination
        let countQuery = supabase
          .from("organizations")
          .select("*", { count: "exact", head: true });

        // Apply search filter to count if present
        if (searchTerm.trim()) {
          countQuery = countQuery.or(`name.ilike.%${searchTerm}%`);
        }

        const { count: totalOrgsCount } = await countQuery;
        setTotalCount(totalOrgsCount || 0);

        // Then get paginated data with search filtering
        let query = supabase
          .from("organizations")
          .select("*", { count: "exact" })
          .order("name");

        // Apply search filter if present
        if (searchTerm.trim()) {
          query = query.or(`name.ilike.%${searchTerm}%`);
        }

        const { data: allOrgs, error: orgsError } = await query
          .range(offset, offset + limit - 1);

        if (orgsError) throw new Error(orgsError.message);

        // Get stats for each organization
        const orgsWithStats = await Promise.all(
          (allOrgs || []).map(async (org) => {
            const typedOrg = org as OrganizationRow;

            // Get user count
            const { count: userCount } = await supabase
              .from("user_roles")
              .select("*", { count: "exact", head: true })
              .eq("organization_id", org.id);

            // Get patient count (if patients table exists)
            let patientCount = 0;
            try {
              const result = await supabase
                .from("patients")
                .select("*", { count: "exact", head: true })
                .eq("organization_id", org.id);
              patientCount = result.count || 0;
            } catch {
              // Fallback if patients table doesn't exist
              patientCount = 0;
            }

            return {
              ...typedOrg,
              settings: (typedOrg.settings as Record<string, unknown>) || {},
              parent_id: typedOrg.parent_id || null,
              hierarchy_level: typedOrg.hierarchy_level || null,
              hierarchy_path: typedOrg.hierarchy_path || null,
              user_count: userCount || 0,
              patient_count: patientCount,
              user_role: "system_admin",
              is_current: organization?.id === org.id,
            };
          }),
        );

        const sortedOrgs = buildHierarchicalList(orgsWithStats);
        setOrganizations(sortedOrgs);
      } else {
        // Regular users see only their organizations
        const { data, error } = await supabase
          .from("user_roles")
          .select(
            `
            role,
            organization:organizations (
              id, name, type, settings, created_at, updated_at, parent_id, hierarchy_level, hierarchy_path
            )
          `,
          )
          .eq("user_id", organization.id); // This should be user.id, but we'll use organization for now

        if (error) throw new Error(error.message);

        const rawData = data as unknown as Array<{
          role: string;
          organization: {
            id: string;
            name: string;
            type: string;
            settings: Record<string, unknown>;
            created_at: string;
            updated_at: string;
            parent_id: string | null;
            hierarchy_level: number | null;
            hierarchy_path: string | null;
          } | null;
        }>;

        const userOrgs = rawData.filter((item) => item.organization !== null);
        
        // Apply search filter to user organizations
        const filteredUserOrgs = searchTerm.trim() 
          ? userOrgs.filter(item => 
              item.organization?.name.toLowerCase().includes(searchTerm.toLowerCase())
            )
          : userOrgs;

        setTotalCount(filteredUserOrgs.length);

        // Apply pagination to user organizations
        const paginatedUserOrgs = filteredUserOrgs.slice(offset, offset + limit);

        // Transform and get stats for user's organizations
        const userOrgsWithStats = await Promise.all(
          paginatedUserOrgs.map(async (item) => {
            const org = item.organization!;

            // Get user count
            const { count: userCount } = await supabase
              .from("user_roles")
              .select("*", { count: "exact", head: true })
              .eq("organization_id", org.id);

            // Get patient count
            let patientCount = 0;
            try {
              const result = await supabase
                .from("patients")
                .select("*", { count: "exact", head: true })
                .eq("organization_id", org.id);
              patientCount = result.count || 0;
            } catch {
              // Fallback if patients table doesn't exist
              patientCount = 0;
            }

            return {
              ...org,
              settings: (org.settings as Record<string, unknown>) || {},
              parent_id: org.parent_id || null,
              hierarchy_level: org.hierarchy_level || null,
              hierarchy_path: org.hierarchy_path || null,
              user_count: userCount || 0,
              patient_count: patientCount,
              user_role: item.role,
              is_current: organization?.id === org.id,
            };
          }),
        );

        // Use the same hierarchical sorting for user organizations
        const sortedUserOrgs = buildHierarchicalList(userOrgsWithStats);
        setOrganizations(sortedUserOrgs);
      }
    } catch (error) {
      console.error("Error fetching organizations:", error);
      setError(error as Error);
    } finally {
      setIsLoading(false);
    }
  }, [organization, isSystemAdmin, rolesLoading, roles, limit, offset, searchTerm, buildHierarchicalList]);

  useEffect(() => {
    fetchOrganizations();
  }, [fetchOrganizations]);

  return {
    organizations,
    totalCount,
    isLoading,
    error,
  };
} 