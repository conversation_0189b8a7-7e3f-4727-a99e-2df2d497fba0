import { CommandPaletteContext } from "@/contexts/CommandPaletteContext";
import { useContext } from "react";

// Define the type for the context
interface CommandPaletteContextType {
  open: boolean;
  setOpen: (open: boolean) => void;
}

export const useCommandPalette = (): CommandPaletteContextType => {
  const context = useContext(CommandPaletteContext);
  if (context === undefined) {
    throw new Error(
      "useCommandPalette must be used within a CommandPaletteProvider",
    );
  }
  return context;
};
