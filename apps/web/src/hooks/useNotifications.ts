import { useAuth } from "@/hooks/useAuth";
import { useEffect, useState } from "react";

export interface Notification {
  id: string;
  user_id: string;
  title: string;
  message: string;
  type: "info" | "success" | "warning" | "error";
  read: boolean;
  created_at: string;
}

export function useNotifications() {
  const { user } = useAuth();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch notifications for the current user
  useEffect(() => {
    if (!user) {
      setNotifications([]);
      setUnreadCount(0);
      setIsLoading(false);
      return;
    }

    const fetchNotifications = async () => {
      setIsLoading(true);
      try {
        // In a real app, this would fetch from a notifications table
        // For now, we'll use mock data
        const mockNotifications: Notification[] = [
          {
            id: "1",
            user_id: user.id,
            title: "New appointment request",
            message: "You have a new appointment request from <PERSON>",
            type: "info",
            read: false,
            created_at: new Date().toISOString(),
          },
          {
            id: "2",
            user_id: user.id,
            title: "Lab results available",
            message: "Lab results for patient Jane Smith are now available",
            type: "success",
            read: false,
            created_at: new Date(Date.now() - 3600000).toISOString(),
          },
          {
            id: "3",
            user_id: user.id,
            title: "System maintenance",
            message: "Scheduled maintenance will occur tonight at 2 AM",
            type: "warning",
            read: true,
            created_at: new Date(Date.now() - 86400000).toISOString(),
          },
        ];

        setNotifications(mockNotifications);
        setUnreadCount(mockNotifications.filter((n) => !n.read).length);
      } catch (error) {
        console.error("Error fetching notifications:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchNotifications();
  }, [user]);

  // Mark a notification as read
  const markAsRead = async (notificationId: string) => {
    if (!user) return;

    try {
      // In a real app, this would update the database
      setNotifications((prev: Notification[]) =>
        prev.map((notification: Notification) =>
          notification.id === notificationId
            ? { ...notification, read: true }
            : notification,
        ),
      );

      // Update unread count
      setUnreadCount((prev: number) => Math.max(0, prev - 1));
    } catch (error) {
      console.error("Error marking notification as read:", error);
    }
  };

  // Mark all notifications as read
  const markAllAsRead = async () => {
    if (!user) return;

    try {
      // In a real app, this would update the database
      setNotifications((prev: Notification[]) =>
        prev.map((notification: Notification) => ({
          ...notification,
          read: true,
        })),
      );

      // Update unread count
      setUnreadCount(0);
    } catch (error) {
      console.error("Error marking all notifications as read:", error);
    }
  };

  return {
    notifications,
    unreadCount,
    isLoading,
    markAsRead,
    markAllAsRead,
  };
}
