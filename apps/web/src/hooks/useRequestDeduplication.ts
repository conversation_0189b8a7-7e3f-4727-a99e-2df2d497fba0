import { useRef, useCallback } from "react";

/**
 * Simple hook to prevent duplicate API requests
 * This helps solve the performance issue where the same API calls
 * are made hundreds of times on a single page load
 */

interface PendingRequest<T> {
  promise: Promise<T>;
  timestamp: number;
}

export function useRequestDeduplication() {
  const pendingRequests = useRef(new Map<string, PendingRequest<any>>());
  const cache = useRef(new Map<string, { data: any; timestamp: number }>());
  
  const CACHE_DURATION = 5000; // 5 seconds
  const REQUEST_TIMEOUT = 10000; // 10 seconds

  const deduplicate = useCallback(async <T>(
    key: string,
    requestFn: () => Promise<T>,
    cacheDuration: number = CACHE_DURATION
  ): Promise<T> => {
    // Check cache first
    const cached = cache.current.get(key);
    if (cached && Date.now() - cached.timestamp < cacheDuration) {
      console.log(`[useRequestDeduplication] Cache hit for key: ${key}`);
      return cached.data;
    }

    // Check if request is already pending
    const pending = pendingRequests.current.get(key);
    if (pending && Date.now() - pending.timestamp < REQUEST_TIMEOUT) {
      console.log(`[useRequestDeduplication] Request already pending for key: ${key}`);
      return pending.promise;
    }

    console.log(`[useRequestDeduplication] Making new request for key: ${key}`);

    // Create new request
    const promise = requestFn()
      .then((data) => {
        // Cache the result
        cache.current.set(key, { data, timestamp: Date.now() });
        // Remove from pending
        pendingRequests.current.delete(key);
        return data;
      })
      .catch((error) => {
        // Remove from pending on error
        pendingRequests.current.delete(key);
        throw error;
      });

    // Store as pending
    pendingRequests.current.set(key, { promise, timestamp: Date.now() });

    return promise;
  }, [CACHE_DURATION, REQUEST_TIMEOUT]);

  const clearCache = useCallback((keyOrPattern?: string) => {
    if (!keyOrPattern) {
      cache.current.clear();
      pendingRequests.current.clear();
      return;
    }

    // Clear specific key
    if (!keyOrPattern.includes('*')) {
      cache.current.delete(keyOrPattern);
      pendingRequests.current.delete(keyOrPattern);
      return;
    }

    // Clear by pattern (simple wildcard support)
    const pattern = keyOrPattern.replace('*', '');
    for (const key of cache.current.keys()) {
      if (key.startsWith(pattern)) {
        cache.current.delete(key);
      }
    }
    for (const key of pendingRequests.current.keys()) {
      if (key.startsWith(pattern)) {
        pendingRequests.current.delete(key);
      }
    }
  }, []);

  const getStats = useCallback(() => {
    return {
      cacheSize: cache.current.size,
      pendingRequests: pendingRequests.current.size,
      cacheKeys: Array.from(cache.current.keys()),
      pendingKeys: Array.from(pendingRequests.current.keys())
    };
  }, []);

  return {
    deduplicate,
    clearCache,
    getStats
  };
}
