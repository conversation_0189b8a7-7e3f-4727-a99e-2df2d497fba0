import { useAuth } from "@/hooks/useAuth";
import { useLocations } from "@/hooks/useLocations";
import { useUserRoles } from "@/hooks/useUserRoles";
import { ALL_LOCATIONS_ID } from "@/types/location";
import { useMemo } from "react";

/**
 * Hook to handle location-aware filtering logic for dashboard queries
 * 
 * This extends organization filtering to include location context:
 * - For system admins in "All Organizations" mode, returns null (no filter)
 * - For "All Locations" mode within an org, filters by organization only
 * - For specific location selection, filters by both organization and location
 * - For regular users, filters by their accessible locations
 */
export function useLocationFilter() {
  const { organization } = useAuth();
  const { selectedLocation } = useLocations();
  const { isSystemAdmin, organizationIds } = useUserRoles();

  const filterConfig = useMemo(() => {
    // If no organization is set, don't filter
    if (!organization) {
      return {
        shouldFilterOrganization: false,
        shouldFilterLocation: false,
        organizationId: null,
        locationId: null,
        organizationIds: [],
        isAllOrganizations: false,
        isAllLocations: false,
      };
    }

    // Check if this is the "All Organizations" mode for system admins
    const isAllOrganizations = organization.id === "system-admin-no-org";

    if (isAllOrganizations && isSystemAdmin) {
      // System admin viewing all organizations - don't filter by organization or location
      return {
        shouldFilterOrganization: false,
        shouldFilterLocation: false,
        organizationId: null,
        locationId: null,
        organizationIds: organizationIds, // All orgs the user has access to
        isAllOrganizations: true,
        isAllLocations: true,
      };
    }

    // We're in a specific organization context
    const isAllLocations = selectedLocation?.id === ALL_LOCATIONS_ID;

    if (isAllLocations) {
      // "All Locations" mode within an organization - filter by org only
      return {
        shouldFilterOrganization: true,
        shouldFilterLocation: false,
        organizationId: organization.id,
        locationId: null,
        organizationIds: [organization.id],
        isAllOrganizations: false,
        isAllLocations: true,
      };
    }

    // Specific location selected - filter by both org and location
    return {
      shouldFilterOrganization: true,
      shouldFilterLocation: true,
      organizationId: organization.id,
      locationId: selectedLocation?.id || null,
      organizationIds: [organization.id],
      isAllOrganizations: false,
      isAllLocations: false,
    };
  }, [organization, selectedLocation, isSystemAdmin, organizationIds]);

  return filterConfig;
}

/**
 * Apply location-aware filter to a Supabase query
 * 
 * @param query - The Supabase query builder
 * @param filterConfig - The filter configuration from useLocationFilter
 * @param options - Additional options for filtering
 * @returns The query with appropriate filters applied
 */
export function applyLocationFilter<T>(
  query: T,
  filterConfig: ReturnType<typeof useLocationFilter>,
  options: {
    // For tables that have direct organization_id column
    hasOrganizationId?: boolean;
    // For tables that have direct location_id column
    hasLocationId?: boolean;
    // For tables that connect to locations through departments
    hasDepartmentId?: boolean;
    // Custom join path for complex relationships
    locationJoinPath?: string;
  } = {}
): T {
  const {
    hasOrganizationId = true,
    hasLocationId = false,
    hasDepartmentId = false,
    locationJoinPath,
  } = options;

  let filteredQuery = query;

  // Apply organization filter if needed
  if (filterConfig.shouldFilterOrganization && hasOrganizationId) {
    filteredQuery = (filteredQuery as any).eq("organization_id", filterConfig.organizationId);
  }

  // Apply location filter if needed
  if (filterConfig.shouldFilterLocation && filterConfig.locationId) {
    if (hasLocationId) {
      // Direct location_id column
      filteredQuery = (filteredQuery as any).eq("location_id", filterConfig.locationId);
    } else if (hasDepartmentId) {
      // Filter through departments.location_id
      // Note: This requires the query to include a join with departments
      // The calling code should handle the join, this just adds the filter
      filteredQuery = (filteredQuery as any).eq("departments.location_id", filterConfig.locationId);
    } else if (locationJoinPath) {
      // Custom join path for complex relationships
      filteredQuery = (filteredQuery as any).eq(locationJoinPath, filterConfig.locationId);
    }
  }

  return filteredQuery;
}

/**
 * Helper to build location-aware queries for tables with department relationships
 * 
 * @param baseQuery - The base Supabase query
 * @param filterConfig - The filter configuration
 * @param selectFields - Fields to select from the main table
 * @returns Query with proper joins and filters for location awareness
 */
export function buildLocationAwareQuery<T>(
  baseQuery: T,
  filterConfig: ReturnType<typeof useLocationFilter>,
  selectFields: string = "*"
): T {
  let query = baseQuery;

  // If we need location filtering and the table uses departments
  if (filterConfig.shouldFilterLocation && filterConfig.locationId) {
    // Add join with departments to access location_id
    query = (query as any).select(`
      ${selectFields},
      departments!inner(
        location_id
      )
    `);
    
    // Apply the location filter
    query = (query as any).eq("departments.location_id", filterConfig.locationId);
  } else {
    // No location filtering needed, just select the base fields
    query = (query as any).select(selectFields);
  }

  // Apply organization filter if needed
  if (filterConfig.shouldFilterOrganization) {
    query = (query as any).eq("organization_id", filterConfig.organizationId);
  }

  return query;
} 