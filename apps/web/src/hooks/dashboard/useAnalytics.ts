import { useAuth } from "@/hooks/useAuth";
import { supabase } from "@/lib/supabase";
import { useEffect, useState } from "react";
import { applyOrganizationFilter, useOrganizationFilter } from "./useOrganizationFilter";

export interface DashboardStats {
  todayAppointments: number;
  todayAppointmentsChange: number;
  newPatients: number;
  newPatientsChange: number;
  totalProcedures: number;
  totalProceduresChange: number;
  revenue: number;
  revenueChange: number;
}

export interface PatientDemographics {
  ageGroups: { label: string; value: number }[];
  genderDistribution: { label: string; value: number }[];
}

export interface AppointmentMetrics {
  byStatus: { label: string; value: number }[];
  byDepartment: { label: string; value: number }[];
}

export function useAnalytics() {
  const { organization } = useAuth();
  const organizationFilter = useOrganizationFilter();
  const [stats, setStats] = useState<DashboardStats>({
    todayAppointments: 0,
    todayAppointmentsChange: 0,
    newPatients: 0,
    newPatientsChange: 0,
    totalProcedures: 0,
    totalProceduresChange: 0,
    revenue: 0,
    revenueChange: 0,
  });
  const [patientDemographics, setPatientDemographics] =
    useState<PatientDemographics>({
      ageGroups: [],
      genderDistribution: [],
    });
  const [appointmentMetrics, setAppointmentMetrics] =
    useState<AppointmentMetrics>({
      byStatus: [],
      byDepartment: [],
    });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const fetchAnalytics = async () => {
      if (!organization) {
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        // Fetch today's appointments
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);

        let todayAppointmentsQuery = supabase
          .from("appointments")
          .select("id", { count: "exact" })
          .gte("appointment_date", today.toISOString())
          .lt("appointment_date", tomorrow.toISOString());

        todayAppointmentsQuery = applyOrganizationFilter(
          todayAppointmentsQuery,
          organizationFilter,
        );
        const { data: todayAppointments, error: appointmentsError } =
          await todayAppointmentsQuery;

        if (appointmentsError) throw new Error(appointmentsError.message);

        // Fetch yesterday's appointments for comparison
        const yesterday = new Date(today);
        yesterday.setDate(yesterday.getDate() - 1);
        let yesterdayAppointmentsQuery = supabase
          .from("appointments")
          .select("id", { count: "exact" })
          .gte("appointment_date", yesterday.toISOString())
          .lt("appointment_date", today.toISOString());

        yesterdayAppointmentsQuery = applyOrganizationFilter(
          yesterdayAppointmentsQuery,
          organizationFilter,
        );
        const { count: yesterdayCount } = await yesterdayAppointmentsQuery;

        // Calculate change percentage
        const todayCount = todayAppointments?.length || 0;
        const yesterdayCountValue = yesterdayCount || 1; // Avoid division by zero
        const appointmentsChange = Math.round(
          ((todayCount - yesterdayCountValue) / yesterdayCountValue) * 100,
        );

        // Fetch new patients in the last week
        const lastWeek = new Date();
        lastWeek.setDate(lastWeek.getDate() - 7);

        let newPatientsQuery = supabase
          .from("patients")
          .select("id", { count: "exact" })
          .gte("created_at", lastWeek.toISOString());

        newPatientsQuery = applyOrganizationFilter(
          newPatientsQuery,
          organizationFilter,
        );
        const { data: newPatients, error: patientsError } =
          await newPatientsQuery;

        if (patientsError) throw new Error(patientsError.message);

        // Fetch new patients in the previous week for comparison
        const twoWeeksAgo = new Date(lastWeek);
        twoWeeksAgo.setDate(twoWeeksAgo.getDate() - 7);

        let previousWeekPatientsQuery = supabase
          .from("patients")
          .select("id", { count: "exact" })
          .gte("created_at", twoWeeksAgo.toISOString())
          .lt("created_at", lastWeek.toISOString());

        previousWeekPatientsQuery = applyOrganizationFilter(
          previousWeekPatientsQuery,
          organizationFilter,
        );
        const { count: previousWeekCount } = await previousWeekPatientsQuery;

        // Calculate change percentage
        const newPatientsCount = newPatients?.length || 0;
        const prevWeekCountValue = previousWeekCount || 1; // Avoid division by zero
        const patientsChange = Math.round(
          ((newPatientsCount - prevWeekCountValue) / prevWeekCountValue) * 100,
        );

        // For procedures and revenue, we'll use mock data for now
        // In a real app, you would fetch this from your procedures or billing tables
        const totalProcedures = 132;
        const totalProceduresChange = 3;
        const revenue = 15750;
        const revenueChange = 8;

        // Set the dashboard stats
        setStats({
          todayAppointments: todayCount,
          todayAppointmentsChange: appointmentsChange,
          newPatients: newPatientsCount,
          newPatientsChange: patientsChange,
          totalProcedures,
          totalProceduresChange,
          revenue,
          revenueChange,
        });

        // Fetch patient demographics
        // Age groups
        let patientsQuery = supabase
          .from("patients")
          .select("date_of_birth, gender");

        patientsQuery = applyOrganizationFilter(
          patientsQuery,
          organizationFilter,
        );
        const { data: patients, error: demographicsError } =
          await patientsQuery;

        if (demographicsError) throw new Error(demographicsError.message);

        // Process age groups
        const ageGroups = {
          "0-17": 0,
          "18-34": 0,
          "35-50": 0,
          "51-65": 0,
          "65+": 0,
        };

        const genderCount = {
          male: 0,
          female: 0,
          other: 0,
        };

        patients?.forEach((patient) => {
          // Calculate age
          const birthDate = new Date(patient.date_of_birth);
          const ageDiffMs = Date.now() - birthDate.getTime();
          const ageDate = new Date(ageDiffMs);
          const age = Math.abs(ageDate.getUTCFullYear() - 1970);

          // Increment appropriate age group
          if (age <= 17) ageGroups["0-17"]++;
          else if (age <= 34) ageGroups["18-34"]++;
          else if (age <= 50) ageGroups["35-50"]++;
          else if (age <= 65) ageGroups["51-65"]++;
          else ageGroups["65+"]++;

          // Increment gender count
          if (patient.gender === "male") genderCount.male++;
          else if (patient.gender === "female") genderCount.female++;
          else genderCount.other++;
        });

        // Format for charts
        const ageGroupsFormatted = Object.entries(ageGroups).map(
          ([label, value]) => ({ label, value }),
        );
        const genderDistribution = Object.entries(genderCount).map(
          ([label, value]) => ({
            label: label.charAt(0).toUpperCase() + label.slice(1),
            value,
          }),
        );

        setPatientDemographics({
          ageGroups: ageGroupsFormatted,
          genderDistribution,
        });

        // Fetch appointment metrics
        let appointmentStatsQuery = supabase
          .from("appointments")
          .select("status, department_id");

        appointmentStatsQuery = applyOrganizationFilter(
          appointmentStatsQuery,
          organizationFilter,
        );
        const { data: appointmentStats, error: appointmentStatsError } =
          await appointmentStatsQuery;

        if (appointmentStatsError)
          throw new Error(appointmentStatsError.message);

        // Process appointment status distribution
        const statusCount = {
          scheduled: 0,
          completed: 0,
          cancelled: 0,
          no_show: 0,
          checked_in: 0,
          in_progress: 0,
        };

        const departmentCount: Record<string, number> = {};

        appointmentStats?.forEach((appointment) => {
          // Increment status count
          if (appointment.status in statusCount) {
            statusCount[appointment.status as keyof typeof statusCount]++;
          }

          // Increment department count
          if (appointment.department_id) {
            const deptName = `Department ${appointment.department_id}`;
            departmentCount[deptName] = (departmentCount[deptName] || 0) + 1;
          }
        });

        // Format for charts
        const byStatus = Object.entries(statusCount)
          .filter(([, value]) => value > 0) // Only include non-zero values
          .map(([label, value]) => ({
            label:
              label.charAt(0).toUpperCase() + label.slice(1).replace("_", " "),
            value,
          }));

        const byDepartment = Object.entries(departmentCount)
          .map(([label, value]) => ({ label, value }))
          .sort((a, b) => b.value - a.value) // Sort by count descending
          .slice(0, 5); // Take top 5

        setAppointmentMetrics({
          byStatus,
          byDepartment,
        });

        setIsLoading(false);
      } catch (err) {
        console.error("Error fetching analytics:", err);
        setError(
          err instanceof Error ? err : new Error("Failed to fetch analytics"),
        );
        setIsLoading(false);
      }
    };

    fetchAnalytics();
  }, [organization, organizationFilter]);

  return { stats, patientDemographics, appointmentMetrics, isLoading, error };
}
