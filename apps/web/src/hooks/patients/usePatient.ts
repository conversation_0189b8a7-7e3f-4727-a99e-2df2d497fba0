import { useAuth } from "@/hooks/useAuth";
import { supabase } from "@/lib/supabase";
import { PatientWithStats } from "@/types/patient";
import { useCallback, useEffect, useState } from "react";

export function usePatient(patientId: string | undefined) {
  const { organization } = useAuth();
  const [patient, setPatient] = useState<PatientWithStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const fetchPatient = useCallback(async () => {
    if (!patientId || !organization) {
      setPatient(null);
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const { data, error: fetchError } = await supabase
        .from("patients")
        .select(
          `
          *,
          appointments:appointments(
            id,
            appointment_date,
            status,
            reason,
            notes,
            duration_minutes,
            provider_id
          ),
          medical_records:medical_records(
            id,
            visit_date,
            chief_complaint,
            diagnosis,
            treatment_plan,
            notes,
            provider_id
          ),
          organization:organizations(
            id,
            name,
            type
          )
        `,
        )
        .eq("id", patientId)
        .single();

      if (fetchError) throw new Error(fetchError.message);

      if (
        data &&
        typeof data === "object" &&
        !Array.isArray(data) &&
        "id" in data
      ) {
        // Type assertion for the patient data with appointments and medical records
        // Using any for complex joined data to avoid TypeScript compilation errors
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const patientData = data as any;

        // Calculate age from date of birth if date_of_birth exists
        let age = 0;
        if (patientData.date_of_birth) {
          const dob = new Date(patientData.date_of_birth);
          const ageDifMs = Date.now() - dob.getTime();
          const ageDate = new Date(ageDifMs);
          age = Math.abs(ageDate.getUTCFullYear() - 1970);
        }

        // Process appointments
        const appointmentData = Array.isArray(patientData.appointments)
          ? patientData.appointments
          : [];
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const appointments = appointmentData.sort(
          (a: any, b: any) =>
            new Date(b.appointment_date).getTime() -
            new Date(a.appointment_date).getTime(),
        );

        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const lastVisit = appointments.find(
          (apt: any) =>
            apt.status === "completed" &&
            new Date(apt.appointment_date) < new Date(),
        )?.appointment_date;

        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const nextAppointment = appointments.find(
          (apt: any) =>
            apt.status === "scheduled" &&
            new Date(apt.appointment_date) > new Date(),
        )?.appointment_date;

        // Determine status
        let status: "new" | "active" | "inactive" | "archived" = "new";
        if (appointments.length > 0) {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          const hasRecentActivity = appointments.some((apt: any) => {
            const aptDate = new Date(apt.appointment_date);
            const thirtyDaysAgo = new Date();
            thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
            return aptDate > thirtyDaysAgo;
          });
          status = hasRecentActivity ? "active" : "inactive";
        }

        const processedPatient: PatientWithStats = {
          ...patientData,
          age,
          full_name:
            `${patientData.first_name || ""} ${patientData.last_name || ""}`.trim(),
          last_visit: lastVisit,
          next_appointment: nextAppointment,
          status,
          appointment_count: appointments.length,
          appointments,
        };

        setPatient(processedPatient);
      }
    } catch (err) {
      console.error("Error fetching patient:", err);
      setError(
        err instanceof Error ? err : new Error("Failed to fetch patient"),
      );
    } finally {
      setIsLoading(false);
    }
  }, [patientId, organization]);

  useEffect(() => {
    fetchPatient();
  }, [fetchPatient]);

  const refetch = useCallback(() => {
    fetchPatient();
  }, [fetchPatient]);

  return {
    patient,
    isLoading,
    error,
    refetch,
  };
}
