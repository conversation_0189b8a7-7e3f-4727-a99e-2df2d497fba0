import { useRef, useEffect } from "react";

/**
 * A hook to help track and optimize component renders
 * Logs render counts and tracking in development mode
 */
export function useRenderOptimizer(
  componentName: string,
  trackProps?: Record<string, unknown>,
): void {
  // Always call hooks to maintain order
  const renderCount = useRef(0);
  const lastProps = useRef<Record<string, unknown> | null>(null);

  useEffect(() => {
    // Skip logic in production but hooks must always be called
    if (process.env.NODE_ENV !== "development") return;

    renderCount.current += 1;

    // Log first render differently
    if (renderCount.current === 1) {
      console.debug(`[${componentName}] Initial render`);
      lastProps.current = trackProps || null;
      return;
    }

    // If we're tracking props, find which ones changed
    if (trackProps && lastProps.current) {
      const changedProps: string[] = [];

      // Check each prop for changes
      Object.keys(trackProps).forEach((key) => {
        if (lastProps.current?.[key] !== trackProps[key]) {
          changedProps.push(key);
        }
      });

      // Log which props changed to cause the render
      if (changedProps.length > 0) {
        console.debug(
          `[${componentName}] Render #${renderCount.current} - Changed props: ${changedProps.join(", ")}`,
        );
      } else {
        console.debug(
          `[${componentName}] Render #${renderCount.current} - No tracked props changed (likely parent render)`,
        );
      }

      // Update last props
      lastProps.current = { ...trackProps };
    } else {
      // If not tracking props, just log render count
      console.debug(`[${componentName}] Render #${renderCount.current}`);
    }
  });
}
