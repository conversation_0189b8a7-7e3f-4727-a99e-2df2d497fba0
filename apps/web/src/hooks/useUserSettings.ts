import { useAuth } from "@/hooks/useAuth";
import { secureStorage } from "@/lib/secure-storage";
import { supabase } from "@/lib/supabase";
import {
    AppearanceSettings,
    NotificationSettings,
    SecuritySettings,
    UserProfile,
    UserSettings,
} from "@/types/settings";
import { useEffect, useState } from "react";

const defaultNotificationSettings: NotificationSettings = {
  email: true,
  push: false,
  sms: false,
  inApp: true,
  preferences: {
    appointment_reminder: {
      enabled: true,
      channels: ["email", "inApp"],
    },
    lab_result: {
      enabled: true,
      channels: ["email", "inApp"],
    },
    message: {
      enabled: true,
      channels: ["email", "inApp"],
    },
    task_assignment: {
      enabled: true,
      channels: ["email", "inApp"],
    },
  },
};

const defaultAppearanceSettings: AppearanceSettings = {
  theme: "system",
  compactMode: false,
  reducedMotion: false,
  fontSize: "medium",
  colorScheme: "default",
};

const defaultSecuritySettings: SecuritySettings = {
  twoFactorEnabled: false,
  sessionTimeout: 30,
  loginHistory: [],
};

export function useUserSettings() {
  const { user, organization } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [settings, setSettings] = useState<UserSettings | null>(null);

  useEffect(() => {
    if (!user) {
      setIsLoading(false);
      return;
    }

    async function loadUserSettings() {
      setIsLoading(true);
      setError(null);

      // If user is null, we can't load settings
      if (!user) {
        setIsLoading(false);
        return;
      }

      try {
        // Get user profile data
        const { data: providerData, error: providerError } = await supabase
          .from("healthcare_providers")
          .select("*")
          .eq("user_id", user.id)
          .single();

        if (providerError && providerError.code !== "PGRST116") {
          throw providerError;
        }

        // Get notification preferences
        const { data: notificationData, error: notificationError } =
          await supabase
            .from("notification_preferences")
            .select("*")
            .eq("user_id", user.id);

        if (notificationError) {
          throw notificationError;
        }

        // Build user profile
        const profile: UserProfile = {
          firstName: user.user_metadata?.first_name || "",
          lastName: user.user_metadata?.last_name || "",
          email: user.email || "",
          phone: user.user_metadata?.phone || "",
          title: providerData?.provider_type || "",
          organization: organization || undefined,
          role: providerData?.role || undefined,
        };

        // Build notification settings
        const notificationSettings: NotificationSettings = {
          ...defaultNotificationSettings,
        };

        if (notificationData && notificationData.length > 0) {
          notificationData.forEach((pref) => {
            // Define valid notification types
            const validNotificationTypes = [
              "appointment_reminder",
              "lab_result",
              "prescription_update",
              "medical_record_update",
              "task_assignment",
              "message",
              "alert",
              "system_update",
            ] as const;

            type ValidNotificationType =
              (typeof validNotificationTypes)[number];

            // Check if the type is valid
            const prefType = pref.type as string;
            const isValidType = validNotificationTypes.includes(
              prefType as ValidNotificationType,
            );

            if (prefType && isValidType) {
              // Use type assertion with a known valid type
              const validType = prefType as ValidNotificationType;

              notificationSettings.preferences[validType] = {
                enabled: true,
                channels: [],
              };

              if (pref.email_enabled)
                notificationSettings.preferences[validType]!.channels.push(
                  "email",
                );
              if (pref.push_enabled)
                notificationSettings.preferences[validType]!.channels.push(
                  "push",
                );
              if (pref.sms_enabled)
                notificationSettings.preferences[validType]!.channels.push(
                  "sms",
                );
              if (pref.in_app_enabled)
                notificationSettings.preferences[validType]!.channels.push(
                  "inApp",
                );
            }
          });
        }

        // Get user preferences from secure storage
        const storedAppearance = secureStorage.get("appearance-settings");
        const appearanceSettings: AppearanceSettings = (storedAppearance && typeof storedAppearance === 'object')
          ? storedAppearance as AppearanceSettings
          : defaultAppearanceSettings;

        // Combine all settings
        setSettings({
          profile,
          notifications: notificationSettings,
          appearance: appearanceSettings,
          security: defaultSecuritySettings,
        });
      } catch (err) {
        console.error("Error loading user settings:", err);
        setError(
          err instanceof Error ? err.message : "Failed to load user settings",
        );
      } finally {
        setIsLoading(false);
      }
    }

    loadUserSettings();
  }, [user, organization]);

  const updateProfile = async (updatedProfile: Partial<UserProfile>) => {
    if (!user || !settings) return;

    try {
      // Update user metadata
      const { error: updateError } = await supabase.auth.updateUser({
        data: {
          first_name: updatedProfile.firstName || settings.profile.firstName,
          last_name: updatedProfile.lastName || settings.profile.lastName,
        },
      });

      if (updateError) throw updateError;

      // Update settings state
      setSettings((prev) => {
        if (!prev) return null;
        return {
          ...prev,
          profile: {
            ...prev.profile,
            ...updatedProfile,
          },
        };
      });

      return { success: true };
    } catch (err) {
      console.error("Error updating profile:", err);
      return {
        success: false,
        error: err instanceof Error ? err.message : "Failed to update profile",
      };
    }
  };

  const updateAppearance = (updatedAppearance: Partial<AppearanceSettings>) => {
    if (!settings) return;

    const newAppearance = {
      ...settings.appearance,
      ...updatedAppearance,
    };

    // Save to secure storage
    secureStorage.setUserSetting("appearance-settings", newAppearance);

    // Update state
    setSettings((prev) => {
      if (!prev) return null;
      return {
        ...prev,
        appearance: newAppearance,
      };
    });

    return { success: true };
  };

  return {
    settings,
    isLoading,
    error,
    updateProfile,
    updateAppearance,
  };
}
