/**
 * Table Preferences Utility
 * 
 * Utilities for managing table preferences across the application
 */

import { PageSizeOption } from "@/hooks/usePagination";

// Storage keys for different tables
export const TABLE_STORAGE_KEYS = {
  PATIENTS: "patients-table-page-size",
  APPOINTMENTS: "appointments-table-page-size",
  ORGANIZATIONS: "organizations-table-page-size",
  USERS: "users-table-page-size",
} as const;

/**
 * Clear all table preferences from localStorage
 */
export const clearAllTablePreferences = (): void => {
  try {
    Object.values(TABLE_STORAGE_KEYS).forEach(key => {
      localStorage.removeItem(key);
    });
  } catch (error) {
    console.warn('Failed to clear table preferences:', error);
  }
};

/**
 * Get all current table preferences
 */
export const getAllTablePreferences = (): Record<string, PageSizeOption | null> => {
  const preferences: Record<string, PageSizeOption | null> = {};
  
  try {
    Object.entries(TABLE_STORAGE_KEYS).forEach(([tableName, storageKey]) => {
      const stored = localStorage.getItem(storageKey);
      preferences[tableName] = stored ? (stored as PageSizeOption) : null;
    });
  } catch (error) {
    console.warn('Failed to get table preferences:', error);
  }
  
  return preferences;
};

/**
 * Set a specific table preference
 */
export const setTablePreference = (tableKey: keyof typeof TABLE_STORAGE_KEYS, pageSize: PageSizeOption): void => {
  try {
    const storageKey = TABLE_STORAGE_KEYS[tableKey];
    localStorage.setItem(storageKey, pageSize.toString());
  } catch (error) {
    console.warn(`Failed to set preference for ${tableKey}:`, error);
  }
};

/**
 * Reset a specific table preference to default
 */
export const resetTablePreference = (tableKey: keyof typeof TABLE_STORAGE_KEYS): void => {
  try {
    const storageKey = TABLE_STORAGE_KEYS[tableKey];
    localStorage.removeItem(storageKey);
  } catch (error) {
    console.warn(`Failed to reset preference for ${tableKey}:`, error);
  }
};
