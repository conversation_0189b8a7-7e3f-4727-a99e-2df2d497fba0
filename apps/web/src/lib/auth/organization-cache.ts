import { Organization } from "@/contexts/auth-context-types";
import { secureStorage } from "@/lib/secure-storage";

// Cache configuration - now using secure sessionStorage
const ORG_CACHE_KEY_PREFIX = "org_";
const CACHE_LIFETIME_MINUTES = 60; // 1 hour - reduced for security

// Type for cached organization data
export interface OrganizationCache {
  organization: Organization | null;
  hasOrganization: boolean;
  userId: string;
  lastSelected?: string; // Track the last selected organization ID for multi-tenant users
  availableOrgIds?: string[]; // Store just the IDs of available orgs for multi-org users
}

/**
 * Get the cache key for a specific user
 */
function getUserCacheKey(userId: string): string {
  return `${ORG_CACHE_KEY_PREFIX}${userId}`;
}

/**
 * Save organization data to secure cache
 */
export function cacheOrganizationData(
  userId: string,
  organization: Organization | null,
  options?: { isLastSelected?: boolean },
): void {
  try {
    // Get existing cache to preserve lastSelected if needed
    let existingCache: OrganizationCache | null = null;
    try {
      const cacheKey = getUserCacheKey(userId);
      existingCache = secureStorage.getOrganizationData<OrganizationCache>(cacheKey);
    } catch (e) {
      console.warn("Error parsing existing cache:", e);
    }

    // Create a clean copy of the organization without any circular references
    let cleanOrganization: Organization | null = null;
    if (organization) {
      // Convert enhanced organization to base organization by removing enhanced properties
      cleanOrganization = {
        id: organization.id,
        name: organization.name,
        type: organization.type,
        settings: organization.settings,
        billing_info: organization.billing_info,
        created_at: organization.created_at,
        subscription_tier: organization.subscription_tier,
        updated_at: organization.updated_at,
        owner_id: organization.owner_id,
        parent_id: organization.parent_id || null,
        hierarchy_level: organization.hierarchy_level || null,
        hierarchy_path: organization.hierarchy_path || null,
      };
    }

    const cacheData: OrganizationCache = {
      organization: cleanOrganization,
      hasOrganization: !!cleanOrganization,
      userId,
      // If this is explicitly set as the last selected org, or if we don't have a lastSelected yet
      lastSelected: options?.isLastSelected
        ? cleanOrganization?.id
        : existingCache?.lastSelected || cleanOrganization?.id,
      // Preserve available org IDs
      availableOrgIds: existingCache?.availableOrgIds,
    };

    // Use a user-specific cache key with secure storage
    const cacheKey = getUserCacheKey(userId);
    secureStorage.setOrganizationData(cacheKey, cacheData, CACHE_LIFETIME_MINUTES);

    // Also store the last selected organization ID for quick access
    if (cleanOrganization?.id) {
      try {
        secureStorage.setOrganizationData("last_org", cleanOrganization.id, CACHE_LIFETIME_MINUTES);
        secureStorage.setOrganizationData("last_org_name", cleanOrganization.name, CACHE_LIFETIME_MINUTES);
        secureStorage.setOrganizationData("last_org_user", userId, CACHE_LIFETIME_MINUTES);
      } catch (e) {
        console.warn("Error storing last selected organization:", e);
      }
    }

    // Only log in development environment and limit frequency
    if (process.env.NODE_ENV !== "production" && Math.random() < 0.01) {
      console.debug(`Organization cached securely for user ${userId}`);
    }
  } catch (error) {
    console.warn("Failed to cache organization data:", error);
  }
}

/**
 * Get organization data from secure cache if valid
 * @returns Cache data if valid, null otherwise
 */
export function getOrganizationFromCache(
  userId: string,
): OrganizationCache | null {
  try {
    // Use a user-specific cache key
    const cacheKey = getUserCacheKey(userId);
    const cachedData = secureStorage.getOrganizationData<OrganizationCache>(cacheKey);

    if (!cachedData) {
      // Check if we have a last selected organization for this user
      const lastOrgUser = secureStorage.getOrganizationData<string>("last_org_user");
      const lastOrgId = secureStorage.getOrganizationData<string>("last_org");
      const lastOrgName = secureStorage.getOrganizationData<string>("last_org_name");

      if (lastOrgUser === userId && lastOrgId && lastOrgName) {
        // Create a minimal cache entry with the last selected organization
        const minimalCache: OrganizationCache = {
          organization: {
            id: lastOrgId,
            name: lastOrgName,
            type: "unknown", // We don't have this information
            settings: {},
            billing_info: {},
            created_at: new Date().toISOString(),
            subscription_tier: "free",
            updated_at: new Date().toISOString(),
            owner_id: userId, // Add the required owner_id field
            parent_id: null,
            hierarchy_level: null,
            hierarchy_path: null,
          },
          hasOrganization: true,
          userId,
          lastSelected: lastOrgId,
        };

        // Save this minimal cache for future use
        try {
          secureStorage.setOrganizationData(cacheKey, minimalCache, CACHE_LIFETIME_MINUTES);
        } catch (e) {
          console.warn("Error saving minimal cache:", e);
        }

        return minimalCache;
      }

      return null;
    }

    // Validate cache - must be for current user
    const isValid = cachedData.userId === userId;

    if (!isValid) {
      return null;
    }

    return cachedData;
  } catch (error) {
    // If any error parsing cache, consider it invalid
    console.warn("Failed to retrieve organization cache:", error);
    return null;
  }
}

/**
 * Clear the organization cache for a specific user or all users
 */
export function clearOrganizationCache(userId?: string): void {
  try {
    if (userId) {
      // Clear cache for a specific user
      const cacheKey = getUserCacheKey(userId);
      secureStorage.remove(cacheKey);

      // Also clear the last selected organization if it belongs to this user
      const lastOrgUser = secureStorage.getOrganizationData<string>("last_org_user");
      if (lastOrgUser === userId) {
        secureStorage.remove("last_org");
        secureStorage.remove("last_org_name");
        secureStorage.remove("last_org_user");
      }
    } else {
      // Clear all organization data from secure storage
      secureStorage.clearOrganizationData();
    }
  } catch (error) {
    console.warn("Failed to clear organization cache:", error);
  }
}
