import { isUserSystemAdmin } from "@/services/organization-service";
import { User } from "@supabase/supabase-js";

/**
 * Check if a user is a system administrator
 */
export async function checkIsSystemAdmin(user: User | null): Promise<boolean> {
  if (!user) return false;
  return await isUserSystemAdmin(user.id);
}

/**
 * Check if the current context represents a system admin viewing all organizations
 * This replaces the old "system-admin-no-org" pattern
 */
export function isSystemAdminViewingAll(_user: User | null, organizationCount: number): boolean {
  // If user has access to multiple organizations, they're likely a system admin
  // This is a temporary check until we implement proper role checking in components
  return organizationCount > 1;
}

/**
 * Legacy function for backward compatibility
 * @deprecated Use checkIsSystemAdmin instead
 */
export function isSystemAdminNoOrg(organizationId: string): boolean {
  return organizationId === "system-admin-no-org";
}