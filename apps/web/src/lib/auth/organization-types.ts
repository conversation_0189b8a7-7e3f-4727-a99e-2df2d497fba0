import { Organization } from "@/contexts/auth-context-types";

/**
 * Enhanced organization type that includes additional properties for multi-organization users
 */
export type EnhancedOrganization = Organization & {
  /**
   * Flag indicating if the user has access to multiple organizations
   */
  hasMultipleOrgs?: boolean;

  /**
   * List of all organizations the user has access to
   */
  availableOrgs?: Organization[];
};
