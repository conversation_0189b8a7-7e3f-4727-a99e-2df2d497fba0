import { LucideProps } from "lucide-react";

export type SearchableEntityType =
  | "patient"
  | "appointment"
  | "clinical_note"
  | "document"
  | "task"
  | "medication"
  | "lab_result"
  | "referral"
  | "billing_code"
  | "healthcare_provider"
  | "location"
  | "team"
  | "workflow"
  | "inventory_item"
  | "education_material"
  | "template"
  | "organization"
  | "department"
  | "insurance_provider"
  | "conversation"
  | "notification"
  | "analytics"
  | "settings";

export interface SearchResult {
  id: string;
  type: SearchableEntityType;
  title: string;
  description?: string;
  icon?: React.ComponentType<LucideProps>;
  url: string;
  score: number;
  metadata?: Record<string, any>;
  permissions?: string[];
}

export interface SearchFilter {
  type?: SearchableEntityType[];
  dateRange?: {
    start: Date;
    end: Date;
  };
  status?: string[];
  tags?: string[];
  permissions?: string[];
}

export interface SearchOptions {
  filters?: SearchFilter;
  limit?: number;
  offset?: number;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

export interface SearchIndex {
  type: SearchableEntityType;
  title: string;
  description: string;
  icon: React.ComponentType<LucideProps>;
  searchableFields: string[];
  defaultSort: string;
  permissions: string[];
}

export interface SearchProvider {
  search(query: string, options?: SearchOptions): Promise<SearchResult[]>;
  getSearchableTypes(): SearchableEntityType[];
  getSearchIndex(type: SearchableEntityType): SearchIndex;
}
