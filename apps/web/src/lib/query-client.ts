import { QueryClient } from '@tanstack/react-query';

/**
 * React Query client configuration
 * 
 * Configured for optimal performance with:
 * - 5 minute stale time for system metrics (data doesn't change frequently)
 * - 10 minute cache time to keep data in memory
 * - Background refetching enabled for fresh data
 * - Retry logic for failed requests
 */
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // Data is considered fresh for 5 minutes
      staleTime: 5 * 60 * 1000, // 5 minutes
      
      // Data stays in cache for 10 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
      
      // Refetch data when window regains focus
      refetchOnWindowFocus: true,
      
      // Refetch data when reconnecting to the internet
      refetchOnReconnect: true,
      
      // Retry failed requests up to 3 times
      retry: 3,
      
      // Exponential backoff for retries
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    },
    mutations: {
      // Retry failed mutations once
      retry: 1,
    },
  },
}); 