import { useCallback, useEffect } from "react";
import { shortcutManager } from "./shortcutManager";
import { KeyBinding, ShortcutHook } from "./types";

export function useShortcuts(): ShortcutHook {
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    const modifiers: string[] = [];
    if (event.ctrlKey) modifiers.push("ctrl");
    if (event.altKey) modifiers.push("alt");
    if (event.shiftKey) modifiers.push("shift");
    if (event.metaKey) modifiers.push("meta");

    const key = event.key.toLowerCase();
    const bindings = shortcutManager.getBindings();

    Object.entries(bindings).forEach(([id, binding]) => {
      const bindingModifiers = binding.modifiers.map((m) => m.toLowerCase());
      if (
        key === binding.key.toLowerCase() &&
        modifiers.length === bindingModifiers.length &&
        modifiers.every((m) => bindingModifiers.includes(m))
      ) {
        event.preventDefault();
        document.dispatchEvent(
          new CustomEvent("shortcut-triggered", {
            detail: { action: binding.action, id },
          }),
        );
      }
    });
  }, []);

  useEffect(() => {
    document.addEventListener("keydown", handleKeyDown);
    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [handleKeyDown]);

  const registerShortcut = useCallback((id: string, binding: KeyBinding) => {
    shortcutManager.setBinding(id, binding);
  }, []);

  const unregisterShortcut = useCallback((id: string) => {
    shortcutManager.removeBinding(id);
  }, []);

  return {
    getActions: shortcutManager.getActions.bind(shortcutManager),
    getBindings: shortcutManager.getBindings.bind(shortcutManager),
    setBinding: shortcutManager.setBinding.bind(shortcutManager),
    removeBinding: shortcutManager.removeBinding.bind(shortcutManager),
    checkConflicts: shortcutManager.checkConflicts.bind(shortcutManager),
    registerShortcut,
    unregisterShortcut,
  };
}
