import { KeyBinding } from "./types";

// Helper to create action IDs
export const createActionId = (entity: string, action: string) =>
  `${entity}.${action}`;

// Default keyboard shortcuts for common actions
export const defaultBindings: Record<string, KeyBinding> = {
  [createActionId("app", "search")]: {
    key: "k",
    modifiers: ["meta"],
    description: "Search",
    action: "app.search",
  },
  [createActionId("app", "help")]: {
    key: "/",
    modifiers: ["meta"],
    description: "Show Help",
    action: "app.help",
  },
  [createActionId("app", "settings")]: {
    key: ",",
    modifiers: ["meta"],
    description: "Open Settings",
    action: "app.settings",
  },
  [createActionId("file", "new")]: {
    key: "n",
    modifiers: ["meta"],
    description: "New File",
    action: "file.new",
  },
  [createActionId("file", "save")]: {
    key: "s",
    modifiers: ["meta"],
    description: "Save File",
    action: "file.save",
  },
  [createActionId("edit", "undo")]: {
    key: "z",
    modifiers: ["meta"],
    description: "Undo",
    action: "edit.undo",
  },
  [createActionId("edit", "redo")]: {
    key: "z",
    modifiers: ["meta", "shift"],
    description: "Redo",
    action: "edit.redo",
  },
};
