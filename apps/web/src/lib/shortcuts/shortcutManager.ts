import { KeyBinding, ShortcutManager } from "./types";
import { secureStorage } from "@/lib/secure-storage";

class ShortcutManagerImpl implements ShortcutManager {
  private actions: Record<string, KeyBinding> = {};
  private bindings: Record<string, KeyBinding> = {};

  constructor() {
    this.loadFromStorage();
  }

  private loadFromStorage() {
    try {
      const savedBindings = secureStorage.get("shortcutBindings");
      if (savedBindings && typeof savedBindings === 'object') {
        this.bindings = savedBindings as Record<string, KeyBinding>;
      }
    } catch (error) {
      console.error("Failed to load shortcuts from storage:", error);
    }
  }

  private saveToStorage() {
    try {
      secureStorage.setUIPreference("shortcutBindings", this.bindings, 60 * 24 * 7); // 1 week TTL
    } catch (error) {
      console.error("Failed to save shortcuts to storage:", error);
    }
  }

  getActions(): Record<string, KeyBinding> {
    return { ...this.actions };
  }

  getBindings(): Record<string, KeyBinding> {
    return { ...this.bindings };
  }

  setBinding(id: string, binding: KeyBinding): void {
    this.bindings[id] = binding;
    this.saveToStorage();
  }

  removeBinding(id: string): void {
    delete this.bindings[id];
    this.saveToStorage();
  }

  checkConflicts(binding: KeyBinding): string[] {
    const conflicts: string[] = [];
    const bindingKey = this.normalizeBinding(binding);

    Object.entries(this.bindings).forEach(([id, existingBinding]) => {
      if (this.normalizeBinding(existingBinding) === bindingKey) {
        conflicts.push(id);
      }
    });

    return conflicts;
  }

  private normalizeBinding(binding: KeyBinding): string {
    const sortedModifiers = [...binding.modifiers].sort();
    return `${sortedModifiers.join("+")}+${binding.key.toLowerCase()}`;
  }
}

export const shortcutManager = new ShortcutManagerImpl();
