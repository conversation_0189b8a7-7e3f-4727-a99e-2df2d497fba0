/**
 * Global request cache to prevent duplicate API calls
 * This is a simple in-memory cache that prevents the same request from being made multiple times
 */

interface CachedRequest<T> {
  promise: Promise<T>;
  timestamp: number;
}

class GlobalRequestCache {
  private cache = new Map<string, CachedRequest<any>>();
  private readonly CACHE_DURATION = 5000; // 5 seconds

  /**
   * Get or create a cached request
   */
  async getOrCreate<T>(key: string, requestFn: () => Promise<T>): Promise<T> {
    const cached = this.cache.get(key);
    
    // Check if we have a valid cached request
    if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
      return cached.promise;
    }

    // Create new request
    const promise = requestFn().finally(() => {
      // Remove from cache after completion
      setTimeout(() => {
        this.cache.delete(key);
      }, this.CACHE_DURATION);
    });

    // Store in cache
    this.cache.set(key, {
      promise,
      timestamp: Date.now()
    });

    return promise;
  }

  /**
   * Clear all cached requests
   */
  clear() {
    this.cache.clear();
  }

  /**
   * Get cache stats for debugging
   */
  getStats() {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    };
  }
}

// Global instance
export const globalRequestCache = new GlobalRequestCache();

/**
 * Helper to create cache keys for common patterns
 */
export const cacheKeys = {
  userRoles: (userId: string) => `user_roles:${userId}`,
  appointments: (orgId: string, filters: string) => `appointments:${orgId}:${filters}`,
  patients: (orgId: string, filters: string) => `patients:${orgId}:${filters}`,
  tasks: (orgId: string, filters: string) => `tasks:${orgId}:${filters}`,
  activityLogs: (orgId: string, filters: string) => `activity_logs:${orgId}:${filters}`,
  locations: (userId: string, orgId: string) => `locations:${userId}:${orgId}`,
};
