import { NavigateFunction } from "react-router-dom";

/**
 * Navigate to a new path while preserving current URL parameters
 * This ensures that organization and location filters are maintained across page navigation
 */
export function navigateWithParams(navigate: NavigateFunction, newPath: string) {
  if (typeof window === 'undefined') {
    navigate(newPath);
    return;
  }

  const currentUrl = new URL(window.location.href);
  const currentParams = currentUrl.searchParams;
  
  // Create new URL with the new path but preserve parameters
  const newUrl = new URL(newPath, window.location.origin);
  
  // Copy all current parameters to the new URL
  currentParams.forEach((value, key) => {
    newUrl.searchParams.set(key, value);
  });
  
  // Navigate to the new path with preserved parameters
  navigate(newUrl.pathname + newUrl.search);
}

/**
 * Get current URL parameters as an object
 */
export function getCurrentUrlParams(): Record<string, string> {
  if (typeof window === 'undefined') {
    return {};
  }
  
  const params: Record<string, string> = {};
  const urlParams = new URLSearchParams(window.location.search);
  
  urlParams.forEach((value, key) => {
    params[key] = value;
  });
  
  return params;
}

/**
 * Create a URL with preserved parameters
 */
export function createUrlWithParams(path: string, additionalParams?: Record<string, string>): string {
  if (typeof window === 'undefined') {
    return path;
  }
  
  const currentParams = getCurrentUrlParams();
  const allParams = { ...currentParams, ...additionalParams };
  
  const url = new URL(path, window.location.origin);
  
  Object.entries(allParams).forEach(([key, value]) => {
    if (value) {
      url.searchParams.set(key, value);
    }
  });
  
  return url.pathname + url.search;
}
