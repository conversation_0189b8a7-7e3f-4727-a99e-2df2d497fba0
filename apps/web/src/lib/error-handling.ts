import { toast } from "sonner";

// Error severity levels
export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

// Error context interface
export interface ErrorContext {
  userId?: string;
  organizationId?: string;
  locationId?: string;
  action?: string;
  component?: string;
  url?: string;
  userAgent?: string;
  timestamp?: string;
  [key: string]: unknown;
}

// Error details interface
export interface ErrorDetails {
  message: string;
  error: Error | unknown;
  context?: ErrorContext;
  severity?: ErrorSeverity;
  userMessage?: string;
  shouldNotifyUser?: boolean;
  shouldReport?: boolean;
}

// Logger interface (will be implemented with <PERSON> later)
interface Logger {
  error: (message: string, meta?: Record<string, unknown>) => void;
  warn: (message: string, meta?: Record<string, unknown>) => void;
  info: (message: string, meta?: Record<string, unknown>) => void;
  debug: (message: string, meta?: Record<string, unknown>) => void;
}

// Simple console logger for now (will be replaced with Winston)
const logger: Logger = {
  error: (message: string, meta?: Record<string, unknown>) => {
    console.error(`[ERROR] ${message}`, meta);
  },
  warn: (message: string, meta?: Record<string, unknown>) => {
    console.warn(`[WARN] ${message}`, meta);
  },
  info: (message: string, meta?: Record<string, unknown>) => {
    console.info(`[INFO] ${message}`, meta);
  },
  debug: (message: string, meta?: Record<string, unknown>) => {
    console.debug(`[DEBUG] ${message}`, meta);
  },
};

// Error reporting service (will be replaced with Sentry)
const reportError = async (errorDetails: ErrorDetails): Promise<void> => {
  // For now, just log to console
  // Later this will send to Sentry or similar service
  logger.error('Error reported to monitoring service', {
    message: errorDetails.message,
    error: errorDetails.error,
    context: errorDetails.context,
    severity: errorDetails.severity,
  });
};

// Get current context information
const getCurrentContext = (): Partial<ErrorContext> => {
  return {
    url: window.location.href,
    userAgent: navigator.userAgent,
    timestamp: new Date().toISOString(),
  };
};

// Main error handling function
export const handleError = async (errorDetails: ErrorDetails): Promise<void> => {
  const {
    message,
    error,
    context = {},
    severity = ErrorSeverity.MEDIUM,
    userMessage,
    shouldNotifyUser = true,
    shouldReport = true,
  } = errorDetails;

  // Enrich context with current information
  const enrichedContext: ErrorContext = {
    ...getCurrentContext(),
    ...context,
  };

  // Log the error
  logger.error(message, {
    error: error instanceof Error ? {
      name: error.name,
      message: error.message,
      stack: error.stack,
    } : error,
    context: enrichedContext,
    severity,
  });

  // Report to monitoring service if enabled
  if (shouldReport) {
    try {
      await reportError({
        message,
        error,
        context: enrichedContext,
        severity,
      });
    } catch (reportingError) {
      logger.error('Failed to report error to monitoring service', {
        originalError: message,
        reportingError,
      });
    }
  }

  // Notify user if enabled
  if (shouldNotifyUser) {
    const displayMessage = userMessage || getDefaultUserMessage(severity);
    
    switch (severity) {
      case ErrorSeverity.CRITICAL:
        toast.error(displayMessage, {
          duration: 10000,
          action: {
            label: 'Report Issue',
            onClick: () => {
              // Open support form or email
              window.open('mailto:<EMAIL>?subject=Critical Error Report');
            },
          },
        });
        break;
      case ErrorSeverity.HIGH:
        toast.error(displayMessage, { duration: 8000 });
        break;
      case ErrorSeverity.MEDIUM:
        toast.warning(displayMessage, { duration: 5000 });
        break;
      case ErrorSeverity.LOW:
        toast.info(displayMessage, { duration: 3000 });
        break;
    }
  }
};

// Get default user-friendly messages based on severity
const getDefaultUserMessage = (severity: ErrorSeverity): string => {
  switch (severity) {
    case ErrorSeverity.CRITICAL:
      return 'A critical error occurred. Our team has been notified and is working on a fix.';
    case ErrorSeverity.HIGH:
      return 'An error occurred while processing your request. Please try again.';
    case ErrorSeverity.MEDIUM:
      return 'Something went wrong. Please try again or contact support if the issue persists.';
    case ErrorSeverity.LOW:
      return 'A minor issue occurred. The operation may not have completed as expected.';
    default:
      return 'An unexpected error occurred. Please try again.';
  }
};

// Convenience functions for different error types
export const handleLocationError = (error: Error | unknown, context: Partial<ErrorContext> = {}) => {
  return handleError({
    message: 'Location operation failed',
    error,
    context: {
      ...context,
      component: 'location-management',
    },
    severity: ErrorSeverity.MEDIUM,
    userMessage: 'Unable to complete location operation. Please try again.',
  });
};

export const handleAuthError = (error: Error | unknown, context: Partial<ErrorContext> = {}) => {
  return handleError({
    message: 'Authentication operation failed',
    error,
    context: {
      ...context,
      component: 'authentication',
    },
    severity: ErrorSeverity.HIGH,
    userMessage: 'Authentication failed. Please sign in again.',
  });
};

export const handleDataFetchError = (error: Error | unknown, context: Partial<ErrorContext> = {}) => {
  return handleError({
    message: 'Data fetch operation failed',
    error,
    context: {
      ...context,
      component: 'data-fetch',
    },
    severity: ErrorSeverity.MEDIUM,
    userMessage: 'Unable to load data. Please refresh the page.',
  });
};

// Error boundary helper
export const createErrorBoundaryHandler = (componentName: string) => {
  return (error: Error, errorInfo: { componentStack: string }) => {
    handleError({
      message: `Component error in ${componentName}`,
      error,
      context: {
        component: componentName,
        componentStack: errorInfo.componentStack,
      },
      severity: ErrorSeverity.HIGH,
      userMessage: 'A component error occurred. The page will attempt to recover.',
      shouldNotifyUser: true,
      shouldReport: true,
    });
  };
};

export { logger };
