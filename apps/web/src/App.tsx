import { ErrorBoundary } from "@/components/ErrorBoundary";
import { ProtectedRoute } from "@/components/auth/ProtectedRoute";
import { LoadingStateManager } from "@/components/loading/LoadingStateManager";
import { NavigationManager } from "@/components/navigation/NavigationManager";
import { AuthLayout, DashboardLayout, MainLayout } from "@/layouts";
import {
    AppointmentsManagePage,
    DashboardPage,
    ForgotPasswordPage,
    LoginPage,
    NotFoundPage,
    OrganizationSettingsPage, PatientDetailsPage,
    PatientEditPage,
    PatientsManagePage,
    RegisterPage,
    ResetPasswordPage,
    SettingsPage,
    SetupRouter
} from "@/pages";
import { LandingPage } from "@/pages/home/<USER>";
import { Route, Routes } from "react-router-dom";
import "./App.css";
import { OrganizationsManagePage } from "./pages/organizations/OrganizationsManagePage";



function App() {
  return (
    <ErrorBoundary>
      <NavigationManager>
        <Routes>
          {/* Landing page route */}
          <Route path="/" element={<LandingPage />} />

          {/* Auth routes */}
          <Route element={<AuthLayout />}>
            <Route path="/login" element={<LoginPage />} />
            <Route path="/register" element={<RegisterPage />} />
            <Route path="/forgot-password" element={<ForgotPasswordPage />} />
            <Route path="/reset-password" element={<ResetPasswordPage />} />
          </Route>

          {/* Protected routes - wrapped in LoadingStateManager */}
          <Route
            element={
              <LoadingStateManager>
                <DashboardLayout />
              </LoadingStateManager>
            }
          >
            <Route
              path="/dashboard/*"
              element={
                <ErrorBoundary>
                  <DashboardPage />
                </ErrorBoundary>
              }
            />

            {/* Organization Management */}
            <Route
              path="/organizations"
              element={
                <ErrorBoundary>
                  <OrganizationsManagePage />
                </ErrorBoundary>
              }
            />



            <Route
              path="/organizations/:orgId/settings"
              element={
                <ErrorBoundary>
                  <OrganizationSettingsPage />
                </ErrorBoundary>
              }
            />

            <Route
              path="/settings"
              element={
                <ErrorBoundary>
                  <SettingsPage />
                </ErrorBoundary>
              }
            />

            {/* Appointment Management */}
            <Route
              path="/appointments"
              element={
                <ErrorBoundary>
                  <AppointmentsManagePage />
                </ErrorBoundary>
              }
            />

            {/* Patient Management */}
            <Route
              path="/patients"
              element={
                <ErrorBoundary>
                  <PatientsManagePage />
                </ErrorBoundary>
              }
            />

            <Route
              path="/patients/:patientId"
              element={
                <ErrorBoundary>
                  <PatientDetailsPage />
                </ErrorBoundary>
              }
            />

            <Route
              path="/patients/:patientId/edit"
              element={
                <ErrorBoundary>
                  <PatientEditPage />
                </ErrorBoundary>
              }
            />
          </Route>

          {/* Setup route - only for users without organizations */}
          <Route
            path="/setup/*"
            element={
              <LoadingStateManager>
                <ProtectedRoute requireOrganization={false}>
                  <ErrorBoundary>
                    <SetupRouter />
                  </ErrorBoundary>
                </ProtectedRoute>
              </LoadingStateManager>
            }
          />

          {/* Other main app routes */}
          <Route element={<MainLayout />}>
            {/* Catch unauthorized access attempts */}
            <Route
              path="/unauthorized"
              element={<div>You are not authorized to access this page</div>}
            />
          </Route>

          {/* Handle 404 errors */}
          <Route path="*" element={<NotFoundPage />} />
        </Routes>
      </NavigationManager>
    </ErrorBoundary>
  );
}

export default App;


