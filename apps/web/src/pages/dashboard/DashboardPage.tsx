import { DashboardContainer } from "@/components/dashboard/DashboardContainer";
import { LoadingScreen } from "@/components/ui/loading-screen";
import { useAuth } from "@/hooks/useAuth";
import { useEffect } from "react";
import { useNavigate } from "react-router-dom";

export function DashboardPage() {
  const { user, organization, isLoading } = useAuth();
  const navigate = useNavigate();

  // Redirect to setup if no organization after loading
  useEffect(() => {
    if (!isLoading && user && !organization) {
      navigate("/setup");
    }
  }, [isLoading, user, organization, navigate]);

  // LoadingStateManager handles all loading logic at the app level
  // No need for additional loading checks here

  // If we reach this point, auth is loaded and user is authenticated
  // Just handle the organization redirect case
  if (user && !organization && !isLoading) {
    // This will be handled by the useEffect above, but we can show a brief message
    return <LoadingScreen message="Redirecting to setup..." />;
  }

  // Render the dashboard container component
  return <DashboardContainer />;
}
