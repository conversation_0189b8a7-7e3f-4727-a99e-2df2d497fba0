import { Button } from "@/components/ui/button";
import { motion } from "framer-motion";
import { useNavigate } from "react-router-dom";

const fadeIn = {
  initial: { opacity: 0 },
  whileInView: { opacity: 1 },
  viewport: { once: true },
  transition: { duration: 0.6 },
};

export function CTA() {
  const navigate = useNavigate();

  return (
    <section className="py-20 bg-primary/5">
      <div className="container mx-auto px-4 text-center">
        <motion.div
          initial="initial"
          whileInView="whileInView"
          viewport={{ once: true }}
        >
          <motion.h2
            variants={fadeIn}
            className="text-4xl font-bold mb-6 bg-gradient-to-r from-emerald-500 to-blue-600 bg-clip-text text-transparent"
          >
            Ready to Transform Your Practice?
          </motion.h2>
          <motion.p
            variants={fadeIn}
            className="text-xl mb-8 text-muted-foreground max-w-2xl mx-auto"
          >
            Join thousands of healthcare providers who have already modernized
            their practice with our platform.
          </motion.p>
          <motion.div variants={fadeIn} className="flex gap-4 justify-center">
            <Button
              size="lg"
              className="text-lg"
              onClick={() => navigate("/register")}
            >
              Start Free Trial
            </Button>
            <Button
              size="lg"
              variant="outline"
              className="text-lg"
              onClick={() => navigate("/contact")}
            >
              Contact Sales
            </Button>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
