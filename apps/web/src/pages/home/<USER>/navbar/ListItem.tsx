import { NavigationMenuLink } from "@/components/ui/navigation-menu";
import { ChevronRight } from "lucide-react";
import { useNavigate } from "react-router-dom";

type ListItemProps = {
  href: string;
  icon?: React.ComponentType<{ className?: string }>;
  title: string;
  description?: string;
  onClick?: () => void;
};

export const ListItem = ({
  href,
  icon: Icon,
  title,
  description,
  onClick,
}: ListItemProps) => {
  const navigate = useNavigate();

  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    if (onClick) {
      onClick();
    } else {
      navigate(href);
    }
  };

  return (
    <li>
      <NavigationMenuLink asChild>
        <a
          href={href}
          onClick={handleClick}
          className="block select-none rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent/50 focus:bg-accent/50 w-full"
        >
          <div className="flex items-center gap-3">
            {Icon && (
              <div className="flex-shrink-0 p-2 rounded-md bg-primary/10 transition-colors group-hover:bg-primary/20">
                <Icon className="h-5 w-5 text-primary" />
              </div>
            )}
            <div className="flex-1 min-w-0">
              <div className="text-sm font-medium text-foreground">{title}</div>
              {description && (
                <p className="line-clamp-2 text-sm leading-snug text-muted-foreground mt-1">
                  {description}
                </p>
              )}
            </div>
            <ChevronRight className="h-4 w-4 text-muted-foreground/50 transition-transform group-hover:translate-x-0.5" />
          </div>
        </a>
      </NavigationMenuLink>
    </li>
  );
};
