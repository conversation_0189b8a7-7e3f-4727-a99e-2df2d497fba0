import { NavigationMenuLink } from "@/components/ui/navigation-menu";
import { Check } from "lucide-react";

export interface PricingItemProps {
  title: string;
  price?: string;
  description: string;
  features: string[];
  onClick: () => void;
}

export const PricingItem = ({
  title,
  price,
  description,
  features,
  onClick,
}: PricingItemProps) => {
  // Determine color based on title
  const getColor = () => {
    if (title.toLowerCase().includes("starter")) return "green";
    if (title.toLowerCase().includes("professional")) return "blue";
    return "purple";
  };

  const color = getColor();
  const isPopular = title.toLowerCase().includes("starter");

  return (
    <li>
      <NavigationMenuLink asChild>
        <a
          href="#"
          onClick={(e) => {
            e.preventDefault();
            onClick();
          }}
          className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent/50 focus:bg-accent/50 w-full"
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div
                className={`flex h-5 w-5 items-center justify-center rounded-full ${
                  color === "green"
                    ? "bg-green-100"
                    : color === "blue"
                      ? "bg-blue-100"
                      : "bg-purple-100"
                }`}
              >
                <Check
                  className={`h-3 w-3 ${
                    color === "green"
                      ? "text-green-600"
                      : color === "blue"
                        ? "text-blue-600"
                        : "text-purple-600"
                  }`}
                />
              </div>
              <div className="text-sm font-medium">{title}</div>
            </div>
            {isPopular && (
              <span className="rounded-full bg-green-100 px-2 py-0.5 text-xs text-green-600">
                Popular
              </span>
            )}
          </div>

          {price && (
            <div className="mt-1 text-sm font-semibold text-primary">
              {price}
            </div>
          )}

          <p className="line-clamp-2 text-sm leading-snug text-muted-foreground mt-1">
            {description}
          </p>

          <ul className="mt-2 space-y-1">
            {features.map((feature, idx) => (
              <li
                key={idx}
                className="flex items-start text-xs text-muted-foreground"
              >
                <Check className="mr-1 mt-0.5 h-3 w-3 flex-shrink-0 text-primary" />
                <span>{feature}</span>
              </li>
            ))}
          </ul>
        </a>
      </NavigationMenuLink>
    </li>
  );
};
