import {
  <PERSON>eth<PERSON><PERSON>,
  ClipboardList,
  Users,
  Brain,
  Hospital,
  BarChart,
  Sparkles,
  FileText,
  BarChartHorizontal,
  MessageSquare,
  LifeBuoy,
  Newspaper,
  Landmark,
  Shield,
  Award,
  BookOpen,
  Headphones,
} from "lucide-react";

export const features = [
  {
    title: "Clinical Documentation",
    description: "AI-powered documentation and clinical decision support",
    icon: ClipboardList,
    href: "/features/documentation",
  },
  {
    title: "Practice Management",
    description: "Streamline operations and resource optimization",
    icon: BarChart,
    href: "/features/management",
  },
  {
    title: "Patient Engagement",
    description: "Enhanced patient portal and communication tools",
    icon: Users,
    href: "/features/engagement",
  },
  {
    title: "AI Assistance",
    description: "Smart diagnostic tools and real-time clinical suggestions",
    icon: Sparkles,
    href: "/features/ai",
  },
  {
    title: "Analytics Dashboard",
    description: "Comprehensive reporting and performance insights",
    icon: BarChartHorizontal,
    href: "/features/analytics",
  },
  {
    title: "Secure Messaging",
    description: "HIPAA-compliant communication between providers and patients",
    icon: MessageSquare,
    href: "/features/messaging",
  },
];

export const solutions = [
  {
    title: "Primary Care",
    description: "Comprehensive solutions for primary care practices",
    icon: Stethoscope,
    href: "/solutions/primary-care",
  },
  {
    title: "Specialty Care",
    description: "Specialized tools for various medical specialties",
    icon: Hospital,
    href: "/solutions/specialty-care",
  },
  {
    title: "Mental Health",
    description: "Integrated mental health care solutions",
    icon: Brain,
    href: "/solutions/mental-health",
  },
  {
    title: "Telehealth",
    description: "Virtual care platforms for remote patient consultations",
    icon: Headphones,
    href: "/solutions/telehealth",
  },
];

export const resources = [
  {
    title: "Documentation",
    description: "Detailed guides and API references",
    icon: FileText,
    href: "/resources/docs",
  },
  {
    title: "Help Center",
    description: "Troubleshooting and support resources",
    icon: LifeBuoy,
    href: "/resources/help",
  },
  {
    title: "Blog",
    description: "Industry insights and product updates",
    icon: Newspaper,
    href: "/resources/blog",
  },
  {
    title: "Case Studies",
    description: "Real-world implementation success stories",
    icon: BookOpen,
    href: "/resources/case-studies",
  },
];

export const company = [
  {
    title: "About Us",
    description: "Our mission, vision, and team",
    icon: Landmark,
    href: "/company/about",
  },
  {
    title: "Security",
    description: "Our commitment to data protection",
    icon: Shield,
    href: "/company/security",
  },
  {
    title: "Careers",
    description: "Join our growing team",
    icon: Award,
    href: "/company/careers",
  },
];

export const plans = [
  {
    name: "Starter",
    description: "Perfect for small practices",
    features: [
      "Basic EHR functionality",
      "Up to 5 providers",
      "Standard support",
    ],
    popular: true,
    color: "green",
    href: "/pricing/starter",
  },
  {
    name: "Professional",
    description: "For growing medical groups",
    features: [
      "Advanced EHR features",
      "Up to 25 providers",
      "Priority support",
    ],
    popular: false,
    color: "blue",
    href: "/pricing/professional",
  },
  {
    name: "Enterprise",
    description: "Custom solutions for large organizations",
    features: [
      "Full-featured platform",
      "Unlimited providers",
      "24/7 dedicated support",
    ],
    popular: false,
    color: "purple",
    href: "/pricing/enterprise",
  },
];
