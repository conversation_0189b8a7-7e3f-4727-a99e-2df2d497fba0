import {
  Card,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON>er,
  CardTitle,
} from "@/components/ui/card";
import { motion } from "framer-motion";
import {
  Activity,
  ChartBar,
  Clock,
  HeadphonesIcon,
  Shield,
  Star,
  Users,
} from "lucide-react";

const stats = [
  {
    value: "1M+",
    label: "Patients Served",
    description: "Trusted by healthcare providers nationwide",
    icon: Users,
    color: "from-chart-1 to-chart-2",
    metric: {
      icon: Activity,
      text: "20% monthly growth",
    },
  },
  {
    value: "98%",
    label: "Satisfaction Rate",
    description: "From both providers and patients",
    icon: Star,
    color: "from-chart-2 to-chart-3",
    metric: {
      icon: ChartBar,
      text: "4.8/5 average rating",
    },
  },
  {
    value: "50%",
    label: "Time Saved",
    description: "On administrative tasks",
    icon: Clock,
    color: "from-chart-3 to-chart-4",
    metric: {
      icon: Shield,
      text: "HIPAA compliant",
    },
  },
  {
    value: "24/7",
    label: "Support",
    description: "Always here when you need us",
    icon: HeadphonesIcon,
    color: "from-chart-4 to-chart-5",
    metric: {
      icon: Users,
      text: "Global team coverage",
    },
  },
];

const fadeInUp = {
  initial: { opacity: 0, y: 20 },
  whileInView: { opacity: 1, y: 0 },
  viewport: { once: true },
  transition: { duration: 0.5 },
};

const container = {
  hidden: { opacity: 0 },
  show: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
    },
  },
};

const numberAnimation = {
  initial: { opacity: 0, scale: 0.5 },
  whileInView: {
    opacity: 1,
    scale: 1,
    transition: {
      type: "spring",
      stiffness: 100,
      damping: 10,
    },
  },
  viewport: { once: true },
};

export function Stats() {
  return (
    <section className="py-24 bg-muted/30 dark:bg-muted/10">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <span className="text-primary dark:text-primary-foreground font-medium">
            Our Impact
          </span>
          <h2 className="text-4xl font-bold mt-2 mb-4 bg-gradient-to-r from-emerald-500 to-blue-600 bg-clip-text text-transparent">
            Transforming Healthcare Delivery
          </h2>
          <p className="text-xl text-muted-foreground dark:text-muted-foreground/90 max-w-2xl mx-auto">
            See how we're making a difference in healthcare practices across the
            globe
          </p>
        </motion.div>

        <motion.div
          variants={container}
          initial="hidden"
          whileInView="show"
          viewport={{ once: true }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
        >
          {stats.map((stat, index) => {
            const Icon = stat.icon;
            const MetricIcon = stat.metric.icon;
            return (
              <motion.div key={index} variants={fadeInUp}>
                <Card className="relative overflow-hidden group hover:shadow-lg transition-all duration-300 border-primary/10 dark:border-primary/20 bg-card/50 dark:bg-card/50 backdrop-blur-sm">
                  <div
                    className={`absolute inset-0 bg-gradient-to-br ${stat.color} opacity-0 group-hover:opacity-5 dark:group-hover:opacity-10 transition-opacity duration-500`}
                  />
                  <CardHeader className="space-y-1 pb-2">
                    <motion.div
                      variants={numberAnimation}
                      initial="initial"
                      whileInView="whileInView"
                      viewport={{ once: true }}
                      className="flex items-center gap-2"
                    >
                      <div className="p-2 rounded-lg bg-primary/10 dark:bg-primary/5">
                        <Icon
                          className={`w-6 h-6 text-primary dark:text-primary-foreground`}
                        />
                      </div>
                      <CardTitle
                        className={`text-5xl font-bold bg-gradient-to-r ${stat.color} bg-clip-text text-transparent`}
                      >
                        {stat.value}
                      </CardTitle>
                    </motion.div>
                    <CardDescription className="text-lg font-medium text-foreground dark:text-foreground/90">
                      {stat.label}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground dark:text-muted-foreground/80 mb-3">
                      {stat.description}
                    </p>
                    <div className="flex items-center gap-2 text-sm text-primary dark:text-primary-foreground/90">
                      <MetricIcon className="w-4 h-4" />
                      <span>{stat.metric.text}</span>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            );
          })}
        </motion.div>
      </div>
    </section>
  );
}
