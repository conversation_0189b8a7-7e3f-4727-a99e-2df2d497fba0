import {
    Calendar,
    CalendarPlus,
    ChartBar,
    CheckCircle2,
    ClipboardList,
    Cloud,
    Globe,
    HelpCircle,
    LayoutDashboard,
    Settings,
    Shield,
    UserPlus,
    Users,
    Zap,
} from "lucide-react";

// Custom Icons
export const AppleIcon = ({ className }: { className?: string }) => (
  <svg viewBox="0 0 384 512" className={className} fill="currentColor">
    <path d="M318.7 268.7c-.2-36.7 16.4-64.4 50-84.8-18.8-26.9-47.2-41.7-84.7-44.6-35.5-2.8-74.3 20.7-88.5 20.7-15 0-49.4-19.7-76.4-19.7C63.3 141.2 4 184.8 4 273.5q0 39.3 14.4 81.2c12.8 36.7 59 126.7 107.2 125.2 25.2-.6 43-17.9 75.8-17.9 31.8 0 48.3 17.9 76.4 17.9 48.6-.7 90.4-82.5 102.6-119.3-65.2-30.7-61.7-90-61.7-91.9zm-56.6-164.2c27.3-32.4 24.8-61.9 24-72.5-24.1 1.4-52 16.4-67.9 34.9-17.5 19.8-27.8 44.3-25.6 71.9 26.1 2 49.9-11.4 69.5-34.3z" />
  </svg>
);

export const AndroidIcon = ({ className }: { className?: string }) => (
  <svg viewBox="0 0 24 24" className={className} fill="currentColor">
    <path d="M17.523 15.3414c-.5511 0-.9993-.4486-.9993-.9997s.4482-.9993.9993-.9993c.5511 0 .9993.4482.9993.9993s-.4482.9997-.9993.9997m-11.046 0c-.5511 0-.9993-.4486-.9993-.9997s.4482-.9993.9993-.9993c.5511 0 .9993.4482.9993.9993s-.4482.9997-.9993.9997m11.4045-6.02l1.9973-3.4592a.416.416 0 00-.1521-.5676.416.416 0 00-.5676.1521l-2.0223 3.503C15.5902 8.4094 13.8533 8 12 8s-3.5902.4094-5.1367.9597L4.841 5.4565a.4161.4161 0 00-.5677-.1521.4157.4157 0 00-.1521.5676l1.9973 3.4592C2.6889 11.1867.3432 14.6589 0 18.75h24c-.3432-4.0911-2.6889-7.5633-6.1185-9.4286" />
  </svg>
);

export const platforms = [
  { icon: Globe, text: "Web App", description: "Access from any browser" },
  { icon: AppleIcon, text: "iOS App", description: "Native iPhone & iPad app" },
  { icon: AndroidIcon, text: "Android App", description: "Native Android experience" },
];

export const features = [
  { icon: Shield, text: "HIPAA Compliant", description: "Enterprise-grade security" },
  { icon: CheckCircle2, text: "ONC-ACB Certified", description: "Meets federal standards" },
  { icon: Cloud, text: "Real-time Sync", description: "Instant updates across devices" },
  { icon: Zap, text: "Fast & Reliable", description: "99.9% uptime guarantee" },
];

export const mockupFeatures = [
  {
    title: "Patient Management",
    icon: Users,
    stats: "2.5k+ Active Patients",
    color: "from-blue-500/20 to-blue-500/5",
    gradient: "hover:from-blue-500/30 hover:to-blue-500/10",
  },
  {
    title: "Appointments",
    icon: Calendar,
    stats: "150+ Daily Sessions",
    color: "from-emerald-500/20 to-emerald-500/5",
    gradient: "hover:from-emerald-500/30 hover:to-emerald-500/10",
  },
  {
    title: "Clinical Notes",
    icon: ClipboardList,
    stats: "98% Completion Rate",
    color: "from-purple-500/20 to-purple-500/5",
    gradient: "hover:from-purple-500/30 hover:to-purple-500/10",
  },
  {
    title: "Analytics",
    icon: ChartBar,
    stats: "Real-time Insights",
    color: "from-orange-500/20 to-orange-500/5",
    gradient: "hover:from-orange-500/30 hover:to-orange-500/10",
  },
];

export const shortcuts = [
  { keys: "⌘ K", description: "Quick search" },
  { keys: "⌘ J", description: "New patient" },
  { keys: "⌘ /", description: "Show shortcuts" },
];

export const notifications = [
  "New appointment request",
  "Lab results ready",
  "Prescription renewal",
];

export const searchCommands = [
  {
    id: "patients",
    name: "Search Patients",
    shortcut: ["p"],
    action: () => window.open("/dashboard?tab=patients", "_blank"),
    icon: Users,
    description: "Find and manage patient records",
  },
  {
    id: "appointments",
    name: "View Appointments",
    shortcut: ["a"],
    action: () => window.open("/dashboard?tab=appointments", "_blank"),
    icon: Calendar,
    description: "Schedule and manage appointments",
  },
  {
    id: "notes",
    name: "Clinical Notes",
    shortcut: ["n"],
    action: () => window.open("/dashboard?tab=notes", "_blank"),
    icon: ClipboardList,
    description: "Create and edit clinical documentation",
  },
  {
    id: "analytics",
    name: "Analytics Dashboard",
    shortcut: ["d"],
    action: () => window.open("/dashboard?tab=analytics", "_blank"),
    icon: ChartBar,
    description: "View practice performance metrics",
  },
];

export const quickActions = [
  {
    id: "new-patient",
    name: "New Patient",
    shortcut: ["j"],
    action: () => window.open("/dashboard/patients/new", "_blank"),
    icon: UserPlus,
    description: "Register a new patient",
  },
  {
    id: "new-appointment",
    name: "New Appointment",
    shortcut: ["b"],
    action: () => window.open("/dashboard/appointments/new", "_blank"),
    icon: CalendarPlus,
    description: "Schedule a new appointment",
  },
];

export const navigateCommands = [
  {
    id: "dashboard",
    name: "Go to Dashboard",
    shortcut: ["g", "d"],
    action: () => window.open("/dashboard", "_blank"),
    icon: LayoutDashboard,
    description: "View your main dashboard",
  },
  {
    id: "settings",
    name: "Settings",
    shortcut: ["g", "s"],
    action: () => window.open("/settings", "_blank"),
    icon: Settings,
    description: "Configure application settings",
  },
  {
    id: "help",
    name: "Help & Support",
    shortcut: ["g", "h"],
    action: () => window.open("/help", "_blank"),
    icon: HelpCircle,
    description: "Get help and support",
  },
];
