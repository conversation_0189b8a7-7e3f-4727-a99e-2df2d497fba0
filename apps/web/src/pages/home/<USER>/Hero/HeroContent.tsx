import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { motion } from "framer-motion";
import { ArrowRight } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { features, platforms } from "./constants";

const fadeInUp = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
  transition: { duration: 0.8 },
};

const stagger = {
  animate: {
    transition: {
      staggerChildren: 0.2,
    },
  },
};

export function HeroContent() {
  const navigate = useNavigate();

  return (
    <motion.div
      initial="initial"
      animate="animate"
      variants={stagger}
      className="max-w-2xl"
    >
      <motion.div variants={fadeInUp} className="flex flex-wrap gap-2 mb-6">
        {platforms.map((platform, index) => (
          <Badge
            key={index}
            variant="outline"
            className="py-2 px-4 text-sm bg-primary/5 border-primary/20 flex items-center gap-2 group hover:bg-primary/10 hover:border-emerald-500/40 hover:shadow-[0_0_10px_rgba(16,185,129,0.15)] transition-all duration-300"
          >
            <platform.icon className="w-6 h-6 shrink-0" />
            <div>
              <div className="font-medium relative">
                <span className="relative z-10 transition-opacity duration-300 group-hover:opacity-0">
                  {platform.text}
                </span>
                <span className="absolute inset-0 bg-gradient-to-r from-emerald-500 to-blue-600 bg-clip-text text-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  {platform.text}
                </span>
              </div>
              <div className="text-xs text-muted-foreground relative">
                <span className="relative z-10 transition-opacity duration-300 group-hover:opacity-0">
                  {platform.description}
                </span>
                <span className="absolute inset-0 bg-gradient-to-r from-emerald-500 to-blue-600 bg-clip-text text-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  {platform.description}
                </span>
              </div>
            </div>
          </Badge>
        ))}
      </motion.div>

      <motion.h1
        variants={fadeInUp}
        className="text-5xl sm:text-6xl lg:text-7xl font-bold mb-6 bg-gradient-to-r from-emerald-500 to-blue-600 bg-clip-text text-transparent"
      >
        Enterprise EHR Platform for Modern Healthcare
      </motion.h1>

      <motion.p
        variants={fadeInUp}
        className="text-xl mb-8 text-muted-foreground leading-relaxed"
      >
        A comprehensive healthcare platform that unifies web and mobile
        experiences. Built for performance, security, and seamless
        cross-device synchronization.
      </motion.p>

      <motion.div variants={fadeInUp} className="grid grid-cols-2 gap-4 mb-8">
        {features.map((feature, index) => (
          <div
            key={index}
            className="flex items-start gap-3 p-3 rounded-lg bg-primary/5 border border-primary/10 group hover:border-emerald-500/40 hover:shadow-[0_0_10px_rgba(16,185,129,0.15)] transition-all duration-300"
          >
            <feature.icon className="w-5 h-5 text-primary mt-0.5" />
            <div>
              <div className="font-medium relative">
                <span className="relative z-10 transition-opacity duration-300 group-hover:opacity-0">
                  {feature.text}
                </span>
                <span className="absolute inset-0 bg-gradient-to-r from-emerald-500 to-blue-600 bg-clip-text text-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  {feature.text}
                </span>
              </div>
              <div className="text-sm text-muted-foreground relative">
                <span className="relative z-10 transition-opacity duration-300 group-hover:opacity-0">
                  {feature.description}
                </span>
                <span className="absolute inset-0 bg-gradient-to-r from-emerald-500 to-blue-600 bg-clip-text text-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  {feature.description}
                </span>
              </div>
            </div>
          </div>
        ))}
      </motion.div>

      <motion.div
        variants={fadeInUp}
        className="flex flex-col sm:flex-row gap-4"
      >
        <Button
          size="lg"
          className="text-lg px-8 py-6 bg-gradient-to-r from-emerald-500 to-blue-600 text-black font-medium hover:opacity-90 shadow-lg hover:shadow-xl transition-all duration-300 group"
          onClick={() => navigate("/register")}
        >
          Start Free Trial
          <ArrowRight className="ml-2 w-5 h-5 transition-transform group-hover:translate-x-1 text-black" />
        </Button>
        <Button
          size="lg"
          variant="outline"
          className="text-lg px-8 py-6 border-primary/20 hover:bg-primary/5 shadow-lg hover:shadow-xl transition-all duration-300 group overflow-hidden"
          onClick={() => navigate("/demo")}
        >
          <span className="relative inline-block">
            <span className="relative z-10 transition-opacity duration-300 group-hover:opacity-0">
              Watch Demo
            </span>
            <span className="absolute inset-0 bg-gradient-to-r from-emerald-500 to-blue-600 bg-clip-text text-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              Watch Demo
            </span>
          </span>
        </Button>
      </motion.div>
    </motion.div>
  );
}
