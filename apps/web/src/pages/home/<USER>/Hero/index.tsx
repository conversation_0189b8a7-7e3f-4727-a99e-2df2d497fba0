import { useAuth } from "@/hooks/useAuth";
import { motion } from "framer-motion";
import { useEffect, useState } from "react";
import { DemoCommandDialog } from "./DemoCommandDialog";
import { HeroContent } from "./HeroContent";
import { HeroMockup } from "./HeroMockup";

// Unified animation variants for smooth coordination
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      duration: 0.6,
      staggerChildren: 0.2,
      delayChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.8,
      ease: "easeOut",
    },
  },
};

export function Hero() {
  const [isMac, setIsMac] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);
  const [isCommandOpen, setIsCommandOpen] = useState(false);
  useAuth();

  useEffect(() => {
    // Detect OS using modern userAgent approach
    const userAgent = navigator.userAgent.toLowerCase();
    setIsMac(userAgent.includes("mac") || userAgent.includes("darwin"));
    // Set loaded after a brief delay to ensure smooth initial render
    const timer = setTimeout(() => setIsLoaded(true), 100);
    return () => clearTimeout(timer);
  }, []);

  // Add global keyboard shortcut listener for cmd+k
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.metaKey || e.ctrlKey) && e.key.toLowerCase() === 'k') {
        e.preventDefault();
        setIsCommandOpen(true);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  const commandKey = isMac ? "⌘" : "Ctrl";

  // Context-aware command palette handler
  const handleCommandOpen = () => {
    setIsCommandOpen(true);
  };

  return (
    <>
      <section
        className="relative min-h-screen flex items-center justify-center pt-16 overflow-hidden bg-gradient-to-b from-background to-background/95"
        role="region"
        aria-label="Hero section"
      >
        {/* Grid Background */}
        <div className="absolute inset-0 bg-grid-white/[0.02] bg-[size:75px_75px]" />

        {/* Animated Background Gradients */}
        <div className="absolute inset-0 flex items-center justify-center overflow-hidden">
          <motion.div
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.2, 0.3, 0.2],
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              ease: "easeInOut",
              times: [0, 0.5, 1],
            }}
            className="absolute inset-auto w-[600px] h-[600px] bg-gradient-to-tr from-emerald-500/30 to-blue-600/30 rounded-full blur-3xl"
          />
          <motion.div
            animate={{
              scale: [1.2, 1, 1.2],
              opacity: [0.2, 0.3, 0.2],
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              ease: "easeInOut",
              times: [0, 0.5, 1],
            }}
            className="absolute inset-auto w-[500px] h-[500px] bg-gradient-to-bl from-blue-600/30 to-emerald-500/30 rounded-full blur-3xl"
          />
          <motion.div
            animate={{
              scale: [1.1, 1.3, 1.1],
              opacity: [0.15, 0.25, 0.15],
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              ease: "easeInOut",
              times: [0, 0.5, 1],
            }}
            className="absolute inset-auto w-[400px] h-[400px] bg-gradient-to-tl from-emerald-500/20 to-blue-600/20 rounded-full blur-3xl"
          />
        </div>

        {/* Particle Effect */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          {Array.from({ length: 20 }).map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-1 h-1 bg-primary/20 rounded-full"
              animate={{
                x: [
                  Math.random() * window.innerWidth,
                  Math.random() * window.innerWidth,
                ],
                y: [
                  Math.random() * window.innerHeight,
                  Math.random() * window.innerHeight,
                ],
                opacity: [0.2, 0.8, 0.2],
                scale: [1, 1.5, 1],
              }}
              transition={{
                duration: Math.random() * 10 + 10,
                repeat: Infinity,
                ease: "linear",
              }}
              style={{
                left: Math.random() * 100 + "%",
                top: Math.random() * 100 + "%",
              }}
            />
          ))}
        </div>

        <motion.div
          className="container relative z-10 mx-auto px-4"
          variants={containerVariants}
          initial="hidden"
          animate={isLoaded ? "visible" : "hidden"}
        >
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Left Column */}
            <motion.div variants={itemVariants}>
              <HeroContent />
            </motion.div>

            {/* Right Column */}
            <motion.div variants={itemVariants}>
              <HeroMockup
                commandKey={commandKey}
                onCommandOpen={handleCommandOpen}
              />
            </motion.div>
          </div>
        </motion.div>
      </section>

      <DemoCommandDialog
        isOpen={isCommandOpen}
        onOpenChange={setIsCommandOpen}
        commandKey={commandKey}
      />
    </>
  );
}
