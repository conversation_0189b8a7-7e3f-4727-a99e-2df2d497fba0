import { motion } from "framer-motion";
import { Bell, Command, Search, Settings } from "lucide-react";
import { useState, useEffect } from "react";
import { mockupFeatures, shortcuts, notifications } from "./constants";

interface HeroMockupProps {
  commandKey: string;
  onCommandOpen: () => void;
}

const fadeIn = {
  initial: { opacity: 0 },
  animate: { opacity: 1 },
  transition: { duration: 1.2 },
};

const slideUpStagger = {
  initial: { opacity: 0, y: 20 },
  animate: (i: number) => ({
    opacity: 1,
    y: 0,
    transition: {
      delay: i * 0.1,
      duration: 0.5,
      ease: "easeOut",
    },
  }),
};

const pulseAnimation = {
  initial: { opacity: 0.5 },
  animate: {
    opacity: 1,
    transition: {
      duration: 2,
      repeat: Infinity,
      repeatType: "reverse" as const,
    },
  },
};

export function HeroMockup({ commandKey, onCommandOpen }: HeroMockupProps) {
  const [scrollY, setScrollY] = useState(0);

  useEffect(() => {
    const handleScroll = () => setScrollY(window.scrollY);
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return (
    <motion.div
      variants={fadeIn}
      initial="initial"
      animate="animate"
      className="relative hidden lg:block"
      style={{
        transform: `translateY(${scrollY * 0.1}px)`,
      }}
    >
      <div className="relative grid grid-cols-2 gap-4">
        {mockupFeatures.map((feature, index) => (
          <motion.div
            key={index}
            custom={index}
            variants={slideUpStagger}
            whileHover={{ scale: 1.02 }}
            className={`relative overflow-hidden rounded-xl border border-primary/10 shadow-lg transition-colors duration-300 cursor-pointer group`}
            role="button"
            aria-label={`${feature.title} feature card showing ${feature.stats}`}
          >
            <div
              className={`absolute inset-0 bg-gradient-to-br ${feature.color} transition-colors duration-300 ${feature.gradient}`}
            />
            <div className="relative p-4">
              <div className="flex items-center gap-3 mb-3">
                <motion.div
                  whileHover={{ rotate: 15 }}
                  className="p-2 rounded-lg bg-gradient-to-r from-emerald-500/20 to-blue-600/20 shadow-sm transition-transform duration-300 group-hover:scale-110 group-hover:from-emerald-500/30 group-hover:to-blue-600/30"
                >
                  <feature.icon className="w-5 h-5 text-primary" />
                </motion.div>
                <div>
                  <div className="font-medium text-sm">{feature.title}</div>
                  <div className="text-xs text-muted-foreground">
                    {feature.stats}
                  </div>
                </div>
              </div>
              <motion.div variants={pulseAnimation} className="space-y-2">
                <div className="h-2 bg-primary/10 rounded-full w-full" />
                <div className="h-2 bg-primary/10 rounded-full w-2/3" />
                <div className="h-2 bg-primary/10 rounded-full w-1/2" />
              </motion.div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Interface Preview */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4, duration: 0.5 }}
        className="mt-4 rounded-xl overflow-hidden border border-primary/10 shadow-lg bg-background/95 backdrop-blur-sm p-4"
      >
        {/* Command Bar */}
        <motion.div
          className="flex items-center gap-3 mb-4 p-2 rounded-lg bg-primary/5 cursor-pointer hover:bg-primary/10 transition-colors"
          whileHover={{ scale: 1.01 }}
          onClick={onCommandOpen}
        >
          <Search className="w-4 h-4 text-muted-foreground" />
          <span className="text-sm text-muted-foreground">
            Quick search...
          </span>
          <div className="ml-auto flex items-center gap-2">
            <kbd className="px-2 py-1 text-xs rounded bg-background border">
              {commandKey} K
            </kbd>
          </div>
        </motion.div>

        {/* Top Bar */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <motion.div
              whileHover={{ scale: 1.2 }}
              className="w-2 h-2 rounded-full bg-red-500"
            />
            <motion.div
              whileHover={{ scale: 1.2 }}
              className="w-2 h-2 rounded-full bg-yellow-500"
            />
            <motion.div
              whileHover={{ scale: 1.2 }}
              className="w-2 h-2 rounded-full bg-green-500"
            />
          </div>
          <div className="flex items-center gap-3">
            <motion.div whileHover={{ scale: 1.1 }} className="relative">
              <Bell className="w-4 h-4 text-primary" />
              <span className="absolute -top-1 -right-1 w-2 h-2 bg-red-500 rounded-full" />
            </motion.div>
            <motion.div
              whileHover={{ rotate: 180 }}
              className="p-1 rounded-md bg-primary/10 transition-transform duration-300"
            >
              <Settings className="w-3 h-3 text-primary" />
            </motion.div>
          </div>
        </div>

        {/* Content Area */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <motion.div
              variants={pulseAnimation}
              className="h-8 bg-primary/5 rounded-lg w-48"
            />
            <div className="flex gap-2">
              {shortcuts.map((shortcut, i) => (
                <motion.div
                  key={i}
                  whileHover={{ y: -2 }}
                  className="flex items-center gap-2 px-2 py-1 rounded-md bg-primary/5 text-xs"
                >
                  <kbd className="px-1.5 py-0.5 rounded bg-background border text-muted-foreground">
                    {shortcut.keys}
                  </kbd>
                  <span className="text-muted-foreground">
                    {shortcut.description}
                  </span>
                </motion.div>
              ))}
            </div>
          </div>

          {/* Feature Grid */}
          <div className="grid grid-cols-3 gap-2">
            {[0, 1, 2].map((i) => (
              <motion.div
                key={i}
                custom={i}
                variants={slideUpStagger}
                whileHover={{ scale: 1.02 }}
                className="relative h-20 bg-primary/5 rounded-lg hover:bg-primary/10 transition-colors duration-300 cursor-pointer p-3 overflow-hidden group"
              >
                <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                <div className="relative z-10">
                  <div className="flex items-center gap-2 mb-2">
                    <Command className="w-3 h-3 text-primary" />
                    <span className="text-xs font-medium">
                      Quick Action {i + 1}
                    </span>
                  </div>
                  <div className="text-xs text-muted-foreground truncate">
                    {notifications[i]}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
}
