import { Badge } from "@/components/ui/badge";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { motion } from "framer-motion";
import {
  Activity,
  Bot,
  Brain,
  Calendar,
  ChartBar,
  ClipboardCheck,
  MessageSquare,
  Shield,
  Sparkles,
  Stethoscope,
  Users,
  VideoIcon,
} from "lucide-react";

import { LucideIcon } from "lucide-react";

type FeatureItem = {
  badge: string;
  text: string;
  icon: LucideIcon;
};

type FeatureSection = {
  title: string;
  description: string;
  icon: LucideIcon;
  items: FeatureItem[];
};

type FeatureCategory = {
  title: string;
  icon: LucideIcon;
  image: string;
  features: FeatureSection[];
};

const featureData: Record<string, FeatureCategory> = {
  clinical: {
    title: "Clinical Excellence",
    icon: Stethoscope,
    image:
      "https://images.pexels.com/photos/5407212/pexels-photo-5407212.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
    features: [
      {
        title: "Smart Clinical Documentation",
        description: "AI-powered documentation that learns from your workflow",
        icon: ClipboardCheck,
        items: [
          { badge: "AI", text: "Voice-to-text documentation", icon: Sparkles },
          { badge: "Smart", text: "Automated coding suggestions", icon: Bot },
          { badge: "Efficient", text: "Template management", icon: Calendar },
        ],
      },
      {
        title: "Clinical Decision Support",
        description: "Evidence-based recommendations at your fingertips",
        icon: Activity,
        items: [
          { badge: "Real-time", text: "Drug interaction alerts", icon: Shield },
          { badge: "Smart", text: "Treatment protocols", icon: Brain },
          { badge: "AI", text: "Diagnostic assistance", icon: Bot },
        ],
      },
    ],
  },
  operational: {
    title: "Operational Efficiency",
    icon: ChartBar,
    image:
      "https://images.pexels.com/photos/8376326/pexels-photo-8376326.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
    features: [
      {
        title: "Practice Management",
        description: "Streamline your practice operations",
        icon: Calendar,
        items: [
          { badge: "Smart", text: "Automated scheduling", icon: Calendar },
          {
            badge: "Analytics",
            text: "Revenue cycle management",
            icon: ChartBar,
          },
          { badge: "Efficient", text: "Resource allocation", icon: Brain },
        ],
      },
      {
        title: "Resource Optimization",
        description: "Maximize efficiency and reduce waste",
        icon: Brain,
        items: [
          { badge: "AI", text: "Predictive resource allocation", icon: Bot },
          { badge: "Real-time", text: "Inventory management", icon: Activity },
          { badge: "Smart", text: "Staff scheduling", icon: Calendar },
        ],
      },
    ],
  },
  patient: {
    title: "Patient Experience",
    icon: Users,
    image:
      "https://images.pexels.com/photos/7195374/pexels-photo-7195374.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
    features: [
      {
        title: "Patient Portal",
        description: "Empower patients with self-service tools",
        icon: Users,
        items: [
          { badge: "Mobile", text: "Appointment scheduling", icon: Calendar },
          { badge: "Secure", text: "Medical records access", icon: Shield },
          { badge: "Easy", text: "Bill payment", icon: ChartBar },
        ],
      },
      {
        title: "Patient Engagement",
        description: "Build stronger patient relationships",
        icon: MessageSquare,
        items: [
          { badge: "Smart", text: "Automated reminders", icon: Calendar },
          { badge: "Care", text: "Telehealth integration", icon: VideoIcon },
          { badge: "Personal", text: "Care plan tracking", icon: Activity },
        ],
      },
    ],
  },
};

const fadeInUp = {
  initial: { opacity: 0, y: 20 },
  whileInView: { opacity: 1, y: 0 },
  viewport: { once: true },
  transition: { duration: 0.5 },
};

function FeatureCard({
  title,
  description,
  items,
  icon: Icon,
}: {
  title: string;
  description: string;
  items: Array<{ badge: string; text: string; icon: LucideIcon }>;
  icon: LucideIcon;
}) {
  return (
    <Card className="group hover:shadow-lg transition-all duration-300 border-primary/10 dark:border-primary/20 bg-card/50 dark:bg-card/50 backdrop-blur-sm">
      <CardHeader className="space-y-1">
        <div className="flex items-center gap-2 mb-2">
          <div className="p-2 rounded-lg bg-primary/10 dark:bg-primary/5">
            <Icon className="w-5 h-5 text-primary dark:text-primary-foreground" />
          </div>
          <CardTitle className="text-xl group-hover:text-primary transition-colors dark:group-hover:text-primary-foreground">
            {title}
          </CardTitle>
        </div>
        <CardDescription className="text-base text-muted-foreground dark:text-muted-foreground/90">
          {description}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ul className="space-y-3">
          {items.map((item, index) => (
            <li key={index} className="flex items-center gap-3 group/item">
              <Badge
                variant="outline"
                className="bg-primary/5 dark:bg-primary/10 hover:bg-primary/10 dark:hover:bg-primary/20 border-primary/20 dark:border-primary/30 text-primary dark:text-primary-foreground"
              >
                <item.icon className="w-3 h-3 mr-1" />
                {item.badge}
              </Badge>
              <span className="text-foreground/80 dark:text-foreground/70 group-hover/item:text-foreground dark:group-hover/item:text-foreground">
                {item.text}
              </span>
            </li>
          ))}
        </ul>
      </CardContent>
    </Card>
  );
}

export function Features() {
  return (
    <section className="py-24 bg-muted/30 dark:bg-muted/10">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <motion.span
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="inline-block text-primary dark:text-primary-foreground font-medium mb-4"
          >
            Features
          </motion.span>
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.1 }}
            className="text-4xl font-bold mb-4 bg-gradient-to-r from-emerald-500 to-blue-600 bg-clip-text text-transparent"
          >
            Powerful Features for Modern Healthcare
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.2 }}
            className="text-xl text-muted-foreground dark:text-muted-foreground/90 max-w-2xl mx-auto"
          >
            Experience a comprehensive suite of tools designed to transform your
            practice
          </motion.p>
        </div>

        <Tabs defaultValue="clinical" className="w-full max-w-5xl mx-auto">
          <TabsList className="grid w-full grid-cols-3 mb-8 bg-card/50 dark:bg-card/50 backdrop-blur-sm">
            {Object.entries(featureData).map(([key, { title, icon: Icon }]) => (
              <TabsTrigger
                key={key}
                value={key}
                className="gap-2 text-base data-[state=active]:bg-white/5 data-[state=active]:text-emerald-500 data-[state=active]:border data-[state=active]:border-border/10 relative"
              >
                <Icon className="w-5 h-5" />
                {title}
              </TabsTrigger>
            ))}
          </TabsList>

          {Object.entries(featureData).map(([key, { features, image }]) => (
            <TabsContent key={key} value={key}>
              <motion.div
                initial="initial"
                whileInView="whileInView"
                viewport={{ once: true }}
                variants={fadeInUp}
                className="mb-12 rounded-xl overflow-hidden shadow-xl dark:shadow-primary/5 relative group"
              >
                <div className="absolute inset-0 bg-gradient-to-t from-background/80 via-background/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                <img
                  src={image}
                  alt={`${key} features`}
                  className="w-full h-80 object-cover transition-transform duration-700 group-hover:scale-105"
                />
              </motion.div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                {features.map((feature, index) => (
                  <motion.div
                    key={index}
                    variants={fadeInUp}
                    initial="initial"
                    whileInView="whileInView"
                    viewport={{ once: true }}
                  >
                    <FeatureCard {...feature} />
                  </motion.div>
                ))}
              </div>
            </TabsContent>
          ))}
        </Tabs>
      </div>
    </section>
  );
}
