import { Button } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { usePatient } from "@/hooks/patients/usePatient";
import { ArrowLeft, Save } from "lucide-react";
import { useNavigate, useParams } from "react-router-dom";

export function PatientEditPage() {
  const { patientId } = useParams<{ patientId: string }>();
  const navigate = useNavigate();
  const { patient, isLoading, error } = usePatient(patientId);

  const handleBack = () => {
    navigate(`/patients/${patientId}`);
  };

  const handleSave = () => {
    // TODO: Implement patient update logic
    console.log("Save patient changes");
  };

  if (isLoading) {
    return (
      <div className="container py-8 max-w-7xl">
        <div className="animate-pulse space-y-6">
          <div className="flex items-center gap-4">
            <div className="h-8 w-8 bg-muted rounded" />
            <div className="h-8 bg-muted rounded w-48" />
          </div>
          <div className="h-96 bg-muted rounded" />
        </div>
      </div>
    );
  }

  if (error || !patient) {
    return (
      <div className="container py-8 max-w-7xl">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Patient Not Found</h1>
          <p className="text-muted-foreground mb-4">
            {error?.message ||
              "The patient you are looking for does not exist."}
          </p>
          <Button onClick={() => navigate("/patients")}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Patients
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container py-8 max-w-7xl">
      {/* Header */}
      <div className="flex items-center gap-4 mb-6">
        <Button variant="ghost" size="icon" onClick={handleBack}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div className="flex-1">
          <h1 className="text-3xl font-bold">Edit {patient.full_name}</h1>
          <p className="text-muted-foreground">Update patient information</p>
        </div>
        <Button onClick={handleSave}>
          <Save className="mr-2 h-4 w-4" />
          Save Changes
        </Button>
      </div>

      {/* Edit Form */}
      <Card>
        <CardHeader>
          <CardTitle>Patient Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-12 text-muted-foreground">
            <p className="text-lg mb-2">Patient Edit Form</p>
            <p>This feature will be implemented in a future update.</p>
            <p className="text-sm mt-4">
              Current patient: {patient.full_name} (ID: {patient.id})
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
