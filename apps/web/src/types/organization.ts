import { Organization } from "@/contexts/auth-context-types";

export interface OrganizationWithHierarchy {
  id: string;
  name: string;
  type: string;
  settings: any;
  billing_info: any;
  created_at: string | null;
  subscription_tier: string | null;
  updated_at: string | null;
  owner_id: string | null;
  parent_id: string | null;
  hierarchy_level: number | null;
  hierarchy_path: string | null;
  children?: OrganizationWithHierarchy[];
  location_count?: number;
}

export interface OrganizationTreeNode extends OrganizationWithHierarchy {
  children: OrganizationTreeNode[];
  ancestors?: OrganizationWithHierarchy[];
  descendants?: OrganizationWithHierarchy[];
}

export interface OrganizationHierarchyStats {
  totalOrganizations: number;
  maxDepth: number;
  rootOrganizations: number;
  organizationsWithLocations: number;
  totalLocations: number;
}

export interface UserOrganizationAccess {
  organizationId: string;
  role: string;
  permissions: string[];
  isActive: boolean;
}

export interface OrganizationCache {
  organizations: Organization[];
  currentOrgId: string;
  timestamp: number;
  userId: string;
}