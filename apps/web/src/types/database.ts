export interface Organization {
  id: string;
  name: string;
  type: string;
  settings: Record<string, unknown>;
  billing_info: Record<string, unknown>;
  subscription_tier: string;
  owner_id: string;
  created_at: string;
  updated_at: string;
}

export interface UserRole {
  id: string;
  user_id: string;
  organization_id: string;
  role: string;
  invitation_status: "pending" | "accepted" | "declined" | null;
  organization: Organization;
  created_at: string;
  updated_at: string;
}

export interface OrganizationInvite {
  id: string;
  organization_id: string;
  email: string;
  role: string;
  status: "pending" | "accepted" | "declined";
  organization: Organization;
  expires_at: string;
  created_at: string;
  updated_at: string;
}

// Use a single Json type definition
export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[];

export type Database = {
  public: {
    Tables: {
      organizations: Organization;
      user_roles: UserRole;
      organization_invites: OrganizationInvite;
      notification_preferences: {
        Row: {
          id: string;
          user_id: string;
          type: string;
          email_enabled: boolean | null;
          sms_enabled: boolean | null;
          in_app_enabled: boolean | null;
          created_at: string | null;
          updated_at: string | null;
        };
        Insert: {
          id?: string;
          user_id: string;
          type: string;
          email_enabled?: boolean | null;
          sms_enabled?: boolean | null;
          in_app_enabled?: boolean | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
        Update: {
          id?: string;
          user_id?: string;
          type?: string;
          email_enabled?: boolean | null;
          sms_enabled?: boolean | null;
          in_app_enabled?: boolean | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
      };
      patients: {
        Row: {
          id: string;
          first_name: string;
          last_name: string;
          date_of_birth: string;
          gender: string;
          email: string | null;
          phone: string | null;
          address: string | null;
          organization_id: string;
          created_at: string | null;
          updated_at: string | null;
        };
      };
      // ... other tables
    };
    Functions: {
      accept_organization_invite: (invite_id: string) => boolean;
      decline_organization_invite: (invite_id: string) => boolean;
      // ... other functions
    };
    Enums: {
      notification_type:
        | "appointment_reminder"
        | "new_message"
        | "lab_result"
        | "prescription_update"
        | "billing_update"
        | "system_update";
      appointment_status:
        | "scheduled"
        | "completed"
        | "cancelled"
        | "no_show"
        | "checked_in"
        | "in_progress";
      allergy_severity: "mild" | "moderate" | "severe";
      allergy_status: "active" | "inactive";
      user_role:
        | "admin"
        | "provider"
        | "nurse"
        | "staff"
        | "billing"
        | "patient";
    };
  };
};
