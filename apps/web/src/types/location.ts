import { Database } from "@spritely/supabase-types";

export type LocationRow = Database["public"]["Tables"]["locations"]["Row"];

export type Location = LocationRow;

export interface LocationWithStats extends Location {
  department_count?: number;
  user_count?: number;
  patient_count?: number;
}

// Special location for "All Locations" view (similar to system-admin-no-org)
export const ALL_LOCATIONS_ID = "system-admin-all-locations";

export interface AllLocationsLocation {
  id: typeof ALL_LOCATIONS_ID;
  organization_id: string;
  name: "All Locations";
  type: "system";
  address: Record<string, unknown>;
  contact_info: Record<string, unknown>;
  operating_hours: null;
  settings: Record<string, unknown>;
  created_at: string;
  updated_at: string;
}

// Production-ready system admin context
export interface SystemAdminContext {
  isSystemAdmin: boolean;
  impersonatedOrgId?: string;
  impersonatedLocationId?: string;
  auditTrail: boolean;
  restrictedActions: string[];
  sessionId: string;
}

// Enhanced session context for multi-tenant security
export interface SessionContext {
  userId: string;
  organizationId: string;
  locationId?: string;
  role: string;
  permissions: string[];
  systemAdminContext?: SystemAdminContext;
  lastActivity: Date;
  sessionExpiry: Date;
}

// Virtual ID validation
export const VIRTUAL_IDS = {
  SYSTEM_ADMIN_ALL_ORGS: "system-admin-all-orgs", // System admin organization ID
  ALL_LOCATIONS: ALL_LOCATIONS_ID,
} as const;

export type VirtualId = typeof VIRTUAL_IDS[keyof typeof VIRTUAL_IDS];

export function isVirtualId(id: string): id is VirtualId {
  return Object.values(VIRTUAL_IDS).includes(id as VirtualId);
}

// Legacy aliases for backward compatibility during migration
export const ALL_FACILITIES_ID = ALL_LOCATIONS_ID;
export type Facility = Location;
export type FacilityWithStats = LocationWithStats;
export type AllFacilitiesFacility = AllLocationsLocation;