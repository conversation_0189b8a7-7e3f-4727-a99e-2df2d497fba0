/**
 * System Metrics Types
 * 
 * TypeScript interfaces for system-wide metrics data structures
 * used in the system dashboard for aggregated data across all organizations
 */

/**
 * Organization count metrics
 */
export interface OrganizationMetrics {
  total: number;
  active: number;
  inactive: number;
  recentlyCreated: number; // Created in last 30 days
}

/**
 * Patient count metrics aggregated across all organizations
 */
export interface PatientMetrics {
  total: number;
  activePatients: number; // Patients with appointments in last 90 days
  newPatients: number; // Patients created in last 30 days
  byOrganization: Array<{
    organizationId: string;
    organizationName: string;
    patientCount: number;
  }>;
}

/**
 * Appointment metrics aggregated across all organizations
 */
export interface AppointmentMetrics {
  total: number;
  scheduled: number;
  completed: number;
  cancelled: number;
  upcoming: number; // Next 7 days
  todayCount: number;
  byOrganization: Array<{
    organizationId: string;
    organizationName: string;
    appointmentCount: number;
  }>;
}

/**
 * System alerts and notifications
 */
export interface SystemAlerts {
  critical: number;
  warning: number;
  info: number;
  total: number;
  recentAlerts: Array<{
    id: string;
    type: 'critical' | 'warning' | 'info';
    message: string;
    organizationId?: string;
    organizationName?: string;
    timestamp: string;
  }>;
}

/**
 * User activity metrics across the system
 */
export interface UserActivityMetrics {
  totalUsers: number;
  activeUsers: number; // Users active in last 30 days
  newUsers: number; // Users created in last 30 days
  currentSessions: number;
  byOrganization: Array<{
    organizationId: string;
    organizationName: string;
    userCount: number;
    activeUserCount: number;
  }>;
}

/**
 * System performance metrics
 */
export interface SystemPerformanceMetrics {
  uptime: number; // Percentage
  responseTime: number; // Average in milliseconds
  errorRate: number; // Percentage
  databaseConnections: number;
  memoryUsage: number; // Percentage
  cpuUsage: number; // Percentage
}

/**
 * Comprehensive system metrics combining all metric types
 */
export interface SystemMetrics {
  organizations: OrganizationMetrics;
  patients: PatientMetrics;
  appointments: AppointmentMetrics;
  alerts: SystemAlerts;
  users: UserActivityMetrics;
  performance: SystemPerformanceMetrics;
  lastUpdated: string; // ISO timestamp
}

/**
 * API response wrapper for system metrics
 */
export interface SystemMetricsResponse {
  success: boolean;
  data: SystemMetrics;
  error?: string;
  timestamp: string;
}

/**
 * Query parameters for fetching system metrics
 */
export interface SystemMetricsQuery {
  dateRange?: {
    start: string; // ISO date
    end: string; // ISO date
  };
  organizationIds?: string[]; // Filter by specific organizations
  includeInactive?: boolean; // Include inactive organizations/users
  refreshCache?: boolean; // Force refresh of cached data
}

/**
 * Metric trend data for charts and analytics
 */
export interface MetricTrend {
  date: string; // ISO date
  value: number;
  change?: number; // Percentage change from previous period
}

/**
 * Time series data for metric trends
 */
export interface MetricTimeSeries {
  organizationCount: MetricTrend[];
  patientCount: MetricTrend[];
  appointmentCount: MetricTrend[];
  userCount: MetricTrend[];
  period: 'daily' | 'weekly' | 'monthly';
  dateRange: {
    start: string;
    end: string;
  };
} 