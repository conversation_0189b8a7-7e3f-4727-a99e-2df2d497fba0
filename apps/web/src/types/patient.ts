import { Database } from "@spritely/supabase-types";

// Base patient type from database
export type PatientRecord = Database["public"]["Tables"]["patients"]["Row"];
export type AppointmentRecord =
  Database["public"]["Tables"]["appointments"]["Row"];

// Enhanced patient interface with computed fields and stats
export interface PatientWithStats extends PatientRecord {
  // Computed fields
  age: number;
  full_name: string;
  last_visit?: string | null;
  next_appointment?: string | null;
  status: "new" | "active" | "inactive" | "archived";
  appointment_count: number;

  // Related data
  appointments?: AppointmentRecord[];

  // Organization info (for system admin view)
  organization?: {
    id: string;
    name: string;
  };
}

// Patient list filters
export interface PatientFilters {
  search: string;
  status?: "new" | "active" | "inactive" | "archived" | "all";
  organizationId?: string;
  ageRange?: {
    min?: number;
    max?: number;
  };
  gender?: "male" | "female" | "other" | "prefer_not_to_say" | "all";
  hasRecentVisit?: boolean;
  hasUpcomingAppointment?: boolean;
}

// Patient statistics
export interface PatientStats {
  total: number;
  new: number;
  active: number;
  inactive: number;
  recentlyAdded: number; // Added in last 30 days
  withUpcomingAppointments: number;
  averageAge: number;
  genderDistribution: {
    male: number;
    female: number;
    other: number;
    prefer_not_to_say: number;
  };
}

// Hook options for patient data fetching
export interface UsePatientOptions {
  limit?: number;
  offset?: number;
  filters?: PatientFilters;
  includeStats?: boolean;
  includeAppointments?: boolean;
  includeOrganization?: boolean; // For system admin view
}

// Patient form data
export interface PatientFormData {
  first_name: string;
  last_name: string;
  date_of_birth: string;
  gender: "male" | "female" | "other" | "prefer_not_to_say";
  phone?: string;
  email?: string;
  address?: string;
  emergency_contact?: string;
  insurance_info?: Record<string, unknown>;
  medical_history?: Record<string, unknown>;
}
