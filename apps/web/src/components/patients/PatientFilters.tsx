import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { PatientFilters as PatientFiltersType } from "@/types/patient";
import { Search, X } from "lucide-react";
import { useCallback } from "react";

interface PatientFiltersProps {
  filters: PatientFiltersType;
  onFiltersChange: (filters: PatientFiltersType) => void;
  className?: string;
}

export function PatientFilters({
  filters,
  onFiltersChange,
  className = "",
}: PatientFiltersProps) {
  // Removed organization filter as it's handled in the navbar

  const updateFilter = useCallback(
    (key: keyof PatientFiltersType, value: string | undefined) => {
      onFiltersChange({
        ...filters,
        [key]: value,
      });
    },
    [filters, onFiltersChange],
  );

  const clearFilters = useCallback(() => {
    onFiltersChange({
      search: "",
      status: "all",
      gender: "all",
      ageRange: undefined,
      hasRecentVisit: undefined,
      hasUpcomingAppointment: undefined,
    });
  }, [onFiltersChange]);

  const hasActiveFilters =
    filters.search ||
    (filters.status && filters.status !== "all") ||
    (filters.gender && filters.gender !== "all") ||
    filters.ageRange ||
    filters.hasRecentVisit ||
    filters.hasUpcomingAppointment;

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Search and primary filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        {/* Search - takes up available space */}
        <div className="relative flex-1 min-w-0">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Search patients..."
            value={filters.search}
            onChange={(e) => updateFilter("search", e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Filters row */}
        <div className="flex flex-wrap gap-3 flex-shrink-0">
          {/* Status filter */}
          <Select
            value={filters.status || "all"}
            onValueChange={(value) =>
              updateFilter("status", value === "all" ? undefined : value)
            }
          >
            <SelectTrigger className="w-full sm:w-[140px]">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="new">New</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="inactive">Inactive</SelectItem>
              <SelectItem value="archived">Archived</SelectItem>
            </SelectContent>
          </Select>

          {/* Gender filter */}
          <Select
            value={filters.gender || "all"}
            onValueChange={(value) =>
              updateFilter("gender", value === "all" ? undefined : value)
            }
          >
            <SelectTrigger className="w-full sm:w-[140px]">
              <SelectValue placeholder="Gender" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Genders</SelectItem>
              <SelectItem value="male">Male</SelectItem>
              <SelectItem value="female">Female</SelectItem>
              <SelectItem value="other">Other</SelectItem>
              <SelectItem value="prefer_not_to_say">Prefer not to say</SelectItem>
            </SelectContent>
          </Select>


        </div>
      </div>

      {/* Clear filters button */}
      {hasActiveFilters && (
        <div className="flex justify-end">
          <Button
            variant="ghost"
            size="sm"
            onClick={clearFilters}
            className="h-8 px-2 lg:px-3"
          >
            <X className="mr-2 h-4 w-4" />
            Clear filters
          </Button>
        </div>
      )}
    </div>
  );
}
