import { CommandPalette } from "@/components/CommandPalette";
import { CommandPaletteContext } from "@/contexts/CommandPaletteContext";
import { useShortcuts } from "@/lib/shortcuts";
import React, { useEffect, useState } from "react";

interface ShortcutEvent extends CustomEvent {
  detail: {
    action: string;
    id: string;
  };
}

interface CommandPaletteProviderProps {
  children: React.ReactNode;
}

export function CommandPaletteProvider({
  children,
}: CommandPaletteProviderProps) {
  const [open, setOpen] = useState(false);
  const { registerShortcut } = useShortcuts();

  useEffect(() => {
    // Register the command palette toggle action
    registerShortcut("command-palette.toggle", {
      key: "k",
      modifiers: ["meta"],
      description: "Toggle Command Palette",
      action: "command-palette.toggle",
    });

    const handler = (e: ShortcutEvent) => {
      if (e.detail.action === "command-palette.toggle") {
        setOpen((prev) => !prev);
      }
    };

    document.addEventListener("shortcut-triggered", handler as EventListener);
    return () => {
      document.removeEventListener(
        "shortcut-triggered",
        handler as EventListener,
      );
    };
  }, [registerShortcut]);

  return (
    <CommandPaletteContext.Provider value={{ open, setOpen }}>
      <CommandPalette open={open} onOpenChange={setOpen} />
      {children}
    </CommandPaletteContext.Provider>
  );
}
