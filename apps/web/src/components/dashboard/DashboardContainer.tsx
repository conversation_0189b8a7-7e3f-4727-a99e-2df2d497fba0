import { useAuth } from "@/hooks/useAuth";
import { Dashboard } from "./Dashboard";
import { SystemDashboard } from "./SystemDashboard";

/**
 * DashboardContainer - Smart container component that routes between
 * system-level and organization-specific dashboard views based on user context
 */
export function DashboardContainer() {
  const { organization } = useAuth();

  // Determine if user is viewing system-level dashboard
  // System admins with "All Organizations" view have organization.id === "system-admin-all-orgs"
  const isSystemAdminView = organization?.id === "system-admin-all-orgs";

  return (
    <div className="dashboard-container w-full">
      {isSystemAdminView ? (
        // Render system-level dashboard for system admins viewing "All Organizations"
        <SystemDashboard />
      ) : (
        // Render organization-specific dashboard for regular users or
        // system admins with a specific organization selected
        <Dashboard />
      )}
    </div>
  );
}