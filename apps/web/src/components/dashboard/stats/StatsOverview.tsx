import { Badge } from "@/components/ui/badge";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { useAnalytics } from "@/hooks/dashboard/useAnalytics";
import { Calendar, DollarSign, Stethoscope, Users } from "lucide-react";

export function StatsOverview() {
  // Use the main analytics hook - it should handle location filtering internally
  const { stats, isLoading, error } = useAnalytics();

  if (error) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <Card className="shadow-sm">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              Error Loading Stats
            </CardTitle>
          </CardHeader>
          <CardContent className="p-4">
            <p className="text-sm text-red-500">
              Failed to load dashboard statistics
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i} className="shadow-sm">
            <CardHeader className="pb-2">
              <Skeleton className="h-4 w-24" />
            </CardHeader>
            <CardContent className="p-4">
              <Skeleton className="h-8 w-16 mb-2" />
              <Skeleton className="h-4 w-32" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <Card className="shadow-sm hover:shadow-md transition-all">
          <CardHeader className="pb-2 flex flex-row items-center justify-between space-y-0">
            <CardTitle className="text-sm font-medium">
              Today's Appointments
            </CardTitle>
            <Calendar className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent className="p-4">
            <div className="text-3xl font-bold text-blue-600 dark:text-blue-400">
              {stats.todayAppointments}
            </div>
            <Badge
              variant="outline"
              className={`mt-2 ${
                stats.todayAppointmentsChange >= 0
                  ? "bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-300"
                  : "bg-red-100 text-red-600 dark:bg-red-900 dark:text-red-300"
              }`}
            >
              {stats.todayAppointmentsChange >= 0 ? "+" : ""}
              {stats.todayAppointmentsChange}% from yesterday
            </Badge>
          </CardContent>
        </Card>

        <Card className="shadow-sm hover:shadow-md transition-all">
          <CardHeader className="pb-2 flex flex-row items-center justify-between space-y-0">
            <CardTitle className="text-sm font-medium">New Patients</CardTitle>
            <Users className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent className="p-4">
            <div className="text-3xl font-bold text-green-600 dark:text-green-400">
              {stats.newPatients}
            </div>
            <Badge
              variant="outline"
              className={`mt-2 ${
                stats.newPatientsChange >= 0
                  ? "bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-300"
                  : "bg-red-100 text-red-600 dark:bg-red-900 dark:text-red-300"
              }`}
            >
              {stats.newPatientsChange >= 0 ? "+" : ""}
              {stats.newPatientsChange}% from last week
            </Badge>
          </CardContent>
        </Card>

        <Card className="shadow-sm hover:shadow-md transition-all">
          <CardHeader className="pb-2 flex flex-row items-center justify-between space-y-0">
            <CardTitle className="text-sm font-medium">
              Total Procedures
            </CardTitle>
            <Stethoscope className="h-4 w-4 text-violet-500" />
          </CardHeader>
          <CardContent className="p-4">
            <div className="text-3xl font-bold text-violet-600 dark:text-violet-400">
              {stats.totalProcedures}
            </div>
            <Badge
              variant="outline"
              className={`mt-2 ${
                stats.totalProceduresChange >= 0
                  ? "bg-violet-100 text-violet-600 dark:bg-violet-900 dark:text-violet-300"
                  : "bg-red-100 text-red-600 dark:bg-red-900 dark:text-red-300"
              }`}
            >
              {stats.totalProceduresChange >= 0 ? "+" : ""}
              {stats.totalProceduresChange}% from last month
            </Badge>
          </CardContent>
        </Card>

        <Card className="shadow-sm hover:shadow-md transition-all">
          <CardHeader className="pb-2 flex flex-row items-center justify-between space-y-0">
            <CardTitle className="text-sm font-medium">Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-amber-500" />
          </CardHeader>
          <CardContent className="p-4">
            <div className="text-3xl font-bold text-amber-600 dark:text-amber-400">
              ${stats.revenue.toLocaleString()}
            </div>
            <Badge
              variant="outline"
              className={`mt-2 ${
                stats.revenueChange >= 0
                  ? "bg-amber-100 text-amber-600 dark:bg-amber-900 dark:text-amber-300"
                  : "bg-red-100 text-red-600 dark:bg-red-900 dark:text-red-300"
              }`}
            >
              {stats.revenueChange >= 0 ? "+" : ""}
              {stats.revenueChange}% from last month
            </Badge>
          </CardContent>
        </Card>
      </div>
  );
}
