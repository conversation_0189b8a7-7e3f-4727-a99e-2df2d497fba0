import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useDashboardData } from "@/hooks/dashboard/useDashboardData";
import { formatDate } from "@/lib/utils";
import { Users } from "lucide-react";
import { useNavigate } from "react-router-dom";

export function RecentPatients() {
  const { data, isLoading, error } = useDashboardData();
  const navigate = useNavigate();

  if (error) {
    return (
      <Card className="shadow-sm">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg font-medium">Recent Patients</CardTitle>
          <CardDescription>Failed to load recent patients</CardDescription>
        </CardHeader>
        <CardContent className="p-4">
          <p className="text-sm text-red-500">Error loading patient data</p>
        </CardContent>
      </Card>
    );
  }

  if (isLoading || !data) {
    return (
      <Card className="shadow-sm">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg font-medium">Recent Patients</CardTitle>
          <CardDescription>Loading recent patients...</CardDescription>
        </CardHeader>
        <CardContent className="p-4">
          <div className="animate-pulse space-y-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="h-12 bg-muted rounded-md" />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (data.patients.length === 0) {
    return (
      <Card className="shadow-sm">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg font-medium">Recent Patients</CardTitle>
          <CardDescription>No recent patients found</CardDescription>
        </CardHeader>
        <CardContent className="p-4 flex items-center justify-center">
          <div className="flex flex-col items-center justify-center gap-4">
            <Users className="h-12 w-12 text-muted-foreground" />
            <p className="text-muted-foreground text-center">
              No patients have been added yet
            </p>
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigate("/patients/new")}
            >
              Add New Patient
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="shadow-sm">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-medium">Recent Patients</CardTitle>
          <Button
            variant="ghost"
            size="sm"
            className="h-8 text-xs"
            onClick={() => navigate("/patients")}
          >
            View All
          </Button>
        </div>
        <CardDescription>Recently added patients</CardDescription>
      </CardHeader>
      <CardContent className="p-4">
        <div className="space-y-4">
          {data.patients.map((patient) => (
            <div
              key={patient.id}
              className="flex items-center justify-between p-2 rounded-md hover:bg-muted/50 cursor-pointer"
              onClick={() => navigate(`/patients/${patient.id}`)}
            >
              <div>
                <p className="font-medium">{patient.first_name} {patient.last_name}</p>
                <p className="text-sm text-muted-foreground">
                  Added {formatDate(patient.created_at)}
                </p>
              </div>
              <Button variant="ghost" size="sm" className="h-8">
                View
              </Button>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
