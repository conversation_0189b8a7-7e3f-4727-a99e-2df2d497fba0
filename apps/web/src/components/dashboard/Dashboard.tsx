import { UpcomingAppointments } from "@/components/dashboard/appointments/UpcomingAppointments";
import { RecentPatients } from "@/components/dashboard/patients/RecentPatients";
import { TasksList } from "@/components/dashboard/tasks/TasksList";
import { useDashboardData } from "@/hooks/dashboard/useDashboardData";
import { lazy, Suspense } from "react";

// Lazy load dashboard components to prevent simultaneous API calls
const StatsOverview = lazy(() => import("./stats/StatsOverview").then(m => ({ default: m.StatsOverview })));
const RecentActivity = lazy(() => import("./activity/RecentActivity").then(m => ({ default: m.RecentActivity })));
const PatientDemographics = lazy(() => import("./analytics/PatientDemographics").then(m => ({ default: m.PatientDemographics })));

export function Dashboard() {
  useDashboardData(); // eslint-disable-line @typescript-eslint/no-unused-vars

  return (
    <div className="space-y-4 sm:space-y-6">
      <Suspense fallback={<div>Loading stats...</div>}>
        <StatsOverview />
      </Suspense>

      <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 gap-4 sm:gap-6">
        <RecentPatients />
        <UpcomingAppointments />
        <TasksList />
      </div>

      <div className="grid grid-cols-1 xl:grid-cols-2 gap-4 sm:gap-6">
        <Suspense fallback={<div>Loading activity...</div>}>
          <RecentActivity />
        </Suspense>
        <Suspense fallback={<div>Loading demographics...</div>}>
          <PatientDemographics />
        </Suspense>
      </div>
    </div>
  );
}
