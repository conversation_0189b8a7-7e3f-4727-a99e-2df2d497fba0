import {
    <PERSON><PERSON><PERSON><PERSON>b,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { useAuth } from "@/hooks/useAuth";
import { Building2, Home, Settings } from "lucide-react";
import { Link, useLocation } from "react-router-dom";

/**
 * DashboardBreadcrumb - Breadcrumb navigation component for dashboard views
 * Shows current context (system or organization) and current page
 */
export function DashboardBreadcrumb() {
  const { organization } = useAuth();
  const location = useLocation();

  // Determine if user is viewing system-level dashboard
  const isSystemAdminView = organization?.id === "system-admin-all-orgs";

  // Get current page from pathname
  const getCurrentPage = () => {
    const path = location.pathname;

    if (path === "/dashboard" || path === "/dashboard/") {
      return isSystemAdminView ? "System Dashboard" : "Dashboard";
    }

    // Handle other dashboard sub-routes
    if (path.startsWith("/dashboard/")) {
      const subPath = path.replace("/dashboard/", "");
      switch (subPath) {
        case "analytics":
          return "Analytics";
        case "reports":
          return "Reports";
        case "settings":
          return "Settings";
        default:
          return "Dashboard";
      }
    }

    return "Dashboard";
  };

  const currentPage = getCurrentPage();

  return (
    <Breadcrumb className="mb-4">
      <BreadcrumbList>
        <BreadcrumbItem>
          <BreadcrumbLink asChild>
            <Link to="/dashboard" className="flex items-center gap-2">
              <Home className="h-4 w-4" />
              <span>Home</span>
            </Link>
          </BreadcrumbLink>
        </BreadcrumbItem>

        <BreadcrumbSeparator />

        <BreadcrumbItem>
          <BreadcrumbLink asChild>
            <Link to="/dashboard" className="flex items-center gap-2">
              {isSystemAdminView ? (
                <>
                  <Settings className="h-4 w-4" />
                  <span>System Administration</span>
                </>
              ) : (
                <>
                  <Building2 className="h-4 w-4" />
                  <span>{organization?.name || "Organization"}</span>
                </>
              )}
            </Link>
          </BreadcrumbLink>
        </BreadcrumbItem>

        {currentPage !== (isSystemAdminView ? "System Dashboard" : "Dashboard") && (
          <>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>{currentPage}</BreadcrumbPage>
            </BreadcrumbItem>
          </>
        )}
      </BreadcrumbList>
    </Breadcrumb>
  );
}