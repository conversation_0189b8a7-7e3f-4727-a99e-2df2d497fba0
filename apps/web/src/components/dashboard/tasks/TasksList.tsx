import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useDashboardData } from "@/hooks/dashboard/useDashboardData";
import { formatDate } from "@/lib/utils";
import { CheckSquare } from "lucide-react";
import { useNavigate } from "react-router-dom";

export function TasksList() {
  const { data, isLoading, error } = useDashboardData();
  const navigate = useNavigate();

  if (error) {
    return (
      <Card className="shadow-sm">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg font-medium">Tasks</CardTitle>
          <CardDescription>Failed to load tasks</CardDescription>
        </CardHeader>
        <CardContent className="p-4">
          <p className="text-sm text-red-500">Error loading task data</p>
        </CardContent>
      </Card>
    );
  }

  if (isLoading || !data) {
    return (
      <Card className="shadow-sm">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg font-medium">Tasks</CardTitle>
          <CardDescription>Loading tasks...</CardDescription>
        </CardHeader>
        <CardContent className="p-4">
          <div className="animate-pulse space-y-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="h-12 bg-muted rounded-md" />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (data.tasks.length === 0) {
    return (
      <Card className="shadow-sm">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg font-medium">Tasks</CardTitle>
          <CardDescription>No pending tasks</CardDescription>
        </CardHeader>
        <CardContent className="p-4 flex items-center justify-center">
          <div className="flex flex-col items-center justify-center gap-4">
            <CheckSquare className="h-12 w-12 text-muted-foreground" />
            <p className="text-muted-foreground text-center">
              All tasks are completed
            </p>
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigate("/tasks/new")}
            >
              Create Task
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="shadow-sm">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-medium">Tasks</CardTitle>
          <Button
            variant="ghost"
            size="sm"
            className="h-8 text-xs"
            onClick={() => navigate("/tasks")}
          >
            View All
          </Button>
        </div>
        <CardDescription>Pending tasks</CardDescription>
      </CardHeader>
      <CardContent className="p-4">
        <div className="space-y-4">
          {data.tasks.map((task) => (
            <div
              key={task.id}
              className="flex items-center justify-between p-2 rounded-md hover:bg-muted/50 cursor-pointer"
              onClick={() => navigate(`/tasks/${task.id}`)}
            >
              <div>
                <p className="font-medium">{task.title}</p>
                <p className="text-sm text-muted-foreground">
                  Due {formatDate(task.due_date)} • {task.priority} Priority
                </p>
              </div>
              <Button variant="ghost" size="sm" className="h-8">
                View
              </Button>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
