import { secureStorage } from "@/lib/secure-storage";
import React, { createContext, useContext, useEffect, useState } from "react";

// Define theme types
type Theme = "dark" | "light";

type ThemeProviderProps = {
  children: React.ReactNode;
  defaultTheme?: Theme;
  storageKey?: string;
};

type ThemeProviderState = {
  theme: Theme;
  setTheme: (theme: Theme) => void;
};

// Create initial state
const initialState: ThemeProviderState = {
  theme: "dark",
  setTheme: () => null,
};

// Create context without type argument
const ThemeProviderContext = createContext(initialState);

// Theme provider component
export function ThemeProvider({
  children,
  defaultTheme = "dark",
  storageKey = "spritely-theme",
}: ThemeProviderProps) {
  // Initialize theme state without type argument
  const [theme, setTheme] = useState<Theme>(() => {
    if (typeof window === "undefined") return defaultTheme;

    try {
      const storedTheme = secureStorage.get(storageKey);
      return (storedTheme && typeof storedTheme === 'string') ? (storedTheme as Theme) : defaultTheme;
    } catch {
      return defaultTheme;
    }
  });

  // Apply theme to document root
  useEffect(() => {
    const root = window.document.documentElement;
    const html = document.documentElement;

    // Prevent layout shift by always showing scrollbar
    html.style.overflow = "scroll";

    root.classList.remove("light", "dark");
    root.classList.add(theme);
    root.style.colorScheme = theme;

    // Add transition styles to specific properties only
    document.body.style.setProperty(
      "transition",
      "background-color 0.2s ease, color 0.2s ease, border-color 0.2s ease",
    );

    // Clean up
    return () => {
      document.body.style.removeProperty("transition");
    };
  }, [theme]);

  // Create value object for context
  const value = {
    theme,
    setTheme: (newTheme: Theme) => {
      secureStorage.setUIPreference(storageKey, newTheme);
      setTheme(newTheme);
    },
  };

  return (
    <ThemeProviderContext.Provider value={value}>
      {children}
    </ThemeProviderContext.Provider>
  );
}

// Hook for accessing theme context
export const useTheme = () => {
  const context = useContext(ThemeProviderContext);

  if (context === undefined) {
    throw new Error("useTheme must be used within a ThemeProvider");
  }

  return context;
};
