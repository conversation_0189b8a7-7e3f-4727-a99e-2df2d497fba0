import { useEffect, useRef } from "react";
import { useLocation } from "react-router-dom";

interface PageTransitionProps {
  children: React.ReactNode;
}

export function PageTransition({ children }: PageTransitionProps) {
  const location = useLocation();
  const contentRef = useRef<HTMLDivElement>(null);
  const prevLocationRef = useRef(location.pathname);

  useEffect(() => {
    const currentPath = location.pathname;
    const prevPath = prevLocationRef.current;

    // If location changed, trigger transition
    if (currentPath !== prevPath) {
      if (contentRef.current) {
        // Add enter animation
        contentRef.current.classList.add('page-transition-enter');

        // Remove animation class after animation completes
        const timer = setTimeout(() => {
          if (contentRef.current) {
            contentRef.current.classList.remove('page-transition-enter');
          }
        }, 200); // Match --duration-fast (0.2s)

        // Update previous path
        prevLocationRef.current = currentPath;

        return () => clearTimeout(timer);
      }
    }
  }, [location.pathname]);

  // Initial mount animation
  useEffect(() => {
    if (contentRef.current) {
      contentRef.current.classList.add('page-transition-enter');

      const timer = setTimeout(() => {
        if (contentRef.current) {
          contentRef.current.classList.remove('page-transition-enter');
        }
      }, 200);

      return () => clearTimeout(timer);
    }
  }, []);

  return (
    <div
      ref={contentRef}
      className="w-full h-full"
      key={location.pathname}
    >
      {children}
    </div>
  );
}
