"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useLocations } from "@/hooks/useLocations";
import { ALL_LOCATIONS_ID } from "@/types/location";
import type { Json } from "@spritely/supabase-types";
import { Building2, ChevronDown } from "lucide-react";
import { useMemo, useState } from "react";

/**
 * LocationSelector component for switching between user's accessible locations
 * Includes "All Locations" option for users with access to multiple locations
 */
export function LocationSelector() {
  const {
    locations,
    selectedLocation,
    isLoading,
    canSwitchLocations,
    shouldShowLocationSelector,
    handleLocationSelect,
  } = useLocations();
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  // Create options including "All Locations" if user has multiple locations
  const locationOptions = useMemo(() => {
    const options = [...locations];

    // Add "All Locations" option if user has access to multiple locations
    if (canSwitchLocations) {
      const allLocationsOption = {
        id: ALL_LOCATIONS_ID,
        organization_id: selectedLocation?.organization_id || "",
        name: "All Locations",
        type: "system" as const,
        address: {},
        contact_info: {},
        operating_hours: null as Json,
        settings: {},
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      // Add at the beginning of the list
      options.unshift(allLocationsOption);
    }

    return options;
  }, [locations, canSwitchLocations, selectedLocation?.organization_id]);

  // Get display name for selected location
  const selectedLocationName = useMemo(() => {
    if (!selectedLocation) return "Select Location";

    if (selectedLocation.id === ALL_LOCATIONS_ID) {
      return "All Locations";
    }

    // Clean up location name by removing redundant organization name
    let displayName = selectedLocation.name;

    // If location name contains " - ", take the part after the dash for shorter display
    if (displayName.includes(" - ")) {
      const parts = displayName.split(" - ");
      // Use the last part (most specific location identifier)
      displayName = parts[parts.length - 1];
    }

    return displayName;
  }, [selectedLocation]);

  // Handle location selection
  const handleLocationClick = (location: typeof locationOptions[0]) => {
    setIsDropdownOpen(false);
    handleLocationSelect(location, true); // true = update URL
  };

  // Don't render if location selector shouldn't be shown
  if (!shouldShowLocationSelector) {
    return null;
  }

  // Loading state
  if (isLoading) {
    return (
      <Button
        variant="outline"
        className="h-9 px-3 py-2 flex items-center gap-2 w-full lg:w-[220px] xl:w-[280px] border-input"
        disabled
        title="Loading locations..."
      >
        <div className="flex items-center gap-2 min-w-0 flex-1">
          <Building2 className="h-5 w-5 text-muted-foreground flex-shrink-0" />
          <span className="truncate font-medium text-left">Loading...</span>
        </div>
      </Button>
    );
  }

  // No locations available
  if (locations.length === 0) {
    return (
      <Button
        variant="outline"
        className="h-9 px-3 py-2 flex items-center gap-2 w-full lg:w-[220px] xl:w-[280px] border-input"
        disabled
        title="No locations available"
      >
        <div className="flex items-center gap-2 min-w-0 flex-1">
          <Building2 className="h-5 w-5 text-muted-foreground flex-shrink-0" />
          <span className="truncate font-medium text-left">No Locations</span>
        </div>
      </Button>
    );
  }

  // Single location (no dropdown needed, but show current location)
  if (!canSwitchLocations) {
    return (
      <Button
        variant="outline"
        className="h-9 px-3 py-2 flex items-center gap-2 w-full lg:w-[220px] xl:w-[280px] border-input"
        disabled
        title={selectedLocation?.name || "Select Location"} // Add tooltip with full name
      >
        <div className="flex items-center gap-2 min-w-0 flex-1">
          <Building2 className="h-5 w-5 text-muted-foreground flex-shrink-0" />
          <span className="truncate font-medium text-left">{selectedLocationName}</span>
        </div>
      </Button>
    );
  }

  // Multiple locations (show dropdown)
  return (
    <DropdownMenu open={isDropdownOpen} onOpenChange={setIsDropdownOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          className="h-9 px-3 py-2 flex items-center justify-between gap-2 w-full lg:w-[220px] xl:w-[280px] border-input hover:ring-1 hover:ring-ring/30 hover:ring-offset-0 hover:bg-accent/50 focus-visible:ring-1 focus-visible:ring-ring/30 focus-visible:ring-offset-0 data-[state=open]:ring-1 data-[state=open]:ring-ring/30 data-[state=open]:ring-offset-0 data-[state=open]:bg-accent/50"
          title={selectedLocation?.name || "Select Location"} // Add tooltip with full name
        >
          <div className="flex items-center gap-2 min-w-0 flex-1">
            <Building2 className="h-5 w-5 text-muted-foreground flex-shrink-0" />
            <span className="truncate font-medium text-left">{selectedLocationName}</span>
          </div>
          <ChevronDown className="h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="start"
        className="w-[calc(100vw-2rem)] max-w-[380px] min-w-[300px] border-input shadow-sm"
        sideOffset={4}
      >
        <DropdownMenuLabel>Switch Location</DropdownMenuLabel>
        <DropdownMenuSeparator className="opacity-50" />

        {/* Locations List */}
        <div className="max-h-[300px] overflow-y-auto">
          {locationOptions.map((location) => {
            const isCurrent = selectedLocation && selectedLocation.id === location.id;
            return (
              <DropdownMenuItem
                key={location.id}
                disabled={isLoading || !!isCurrent}
                className="flex items-center justify-between data-[highlighted]:bg-muted/50 hover:!bg-muted/50"
                onSelect={(e) => {
                  e.preventDefault();
                  handleLocationClick(location);
                }}
              >
                <div className="flex items-center flex-1">
                  <span className="truncate">{location.name}</span>
                  {isCurrent && (
                    <span className="ml-2 text-xs text-emerald-500/60 font-medium">
                      (Current)
                    </span>
                  )}
                </div>
              </DropdownMenuItem>
            );
          })}
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
