import type { Database } from "@spritely/supabase-types";
import { useEffect, useState } from "react";
import { supabase } from "../lib/supabase.ts";

type Patient = Database["public"]["Tables"]["patients"]["Row"];

export default function PatientList() {
  const [patients, setPatients] = useState<Patient[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchPatients() {
      try {
        setLoading(true);
        const { data, error } = await supabase
          .from("patients")
          .select("*")
          .order("last_name", { ascending: true });

        if (error) {
          throw error;
        }

        setPatients(data || []);
      } catch (err) {
        setError(err instanceof Error ? err.message : "Unknown error occurred");
        console.error("Error fetching patients:", err);
      } finally {
        setLoading(false);
      }
    }

    fetchPatients();
  }, []);

  if (loading) return <div>Loading patients...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div className="patient-list">
      <h2>Patients</h2>
      {patients.length === 0 ? (
        <p>No patients found</p>
      ) : (
        <ul>
          {patients.map((patient) => (
            <li key={patient.id}>
              {patient.first_name} {patient.last_name} - {patient.date_of_birth}
            </li>
          ))}
        </ul>
      )}
    </div>
  );
}
