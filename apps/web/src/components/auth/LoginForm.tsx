import {
  Accordion,
  Accordion<PERSON>ontent,
  Accordion<PERSON><PERSON>,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useToast } from "@/components/ui/use-toast";
import { useAuth } from "@/hooks/useAuth";
import { zodResolver } from "@hookform/resolvers/zod";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { useNavigate } from "react-router-dom";
import * as z from "zod";

const loginSchema = z.object({
  email: z.string().email("Please enter a valid email address"),
  password: z.string().min(1, "Password is required"),
});

type LoginFormValues = z.infer<typeof loginSchema>;

export function LoginForm() {
  const [isLoading, setIsLoading] = useState(false);
  const { signIn } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();

  const form = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  // Test user credentials
  const testUsers = [
    {
      email: "<EMAIL>",
      password: "password123",
      role: "Admin",
    },
    {
      email: "<EMAIL>",
      password: "password123",
      role: "Provider",
    },
    {
      email: "<EMAIL>",
      password: "password123",
      role: "Provider",
    },
    {
      email: "<EMAIL>",
      password: "password123",
      role: "Nurse",
    },
    {
      email: "<EMAIL>",
      password: "password123",
      role: "Nurse",
    },
    {
      email: "<EMAIL>",
      password: "password123",
      role: "Staff",
    },
    {
      email: "<EMAIL>",
      password: "password123",
      role: "Billing",
    },
  ];

  // Function to fill in credentials when a test user is selected
  const fillCredentials = (userEmail: string, userPassword: string) => {
    form.setValue("email", userEmail);
    form.setValue("password", userPassword);
  };

  const onSubmit = async (values: LoginFormValues) => {
    try {
      setIsLoading(true);

      const { error } = await signIn(values.email, values.password);

      if (error) {
        toast({
          variant: "error",
          title: "Authentication Error",
          description:
            error.message || "Invalid credentials. Please try again.",
        });
        return;
      }

      // Don't navigate manually - let NavigationManager handle it
      // The auth state will update and NavigationManager will redirect appropriately
      toast({
        variant: "success",
        title: "Welcome back!",
        description: "You have been successfully signed in.",
      });
    } catch (err) {
      toast({
        variant: "error",
        title: "Error",
        description: "An unexpected error occurred. Please try again.",
      });
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="w-full">
      <Card className="border-border/40 dark:border-border/50 shadow-xl bg-card backdrop-blur-sm ring-1 ring-black/[0.03] dark:ring-white/10">
        <CardHeader className="space-y-1 pb-8">
          <CardTitle className="text-2xl font-bold text-center bg-gradient-to-r from-emerald-500 to-blue-600 bg-clip-text text-transparent">
            Welcome Back
          </CardTitle>
          <CardDescription className="text-center text-muted-foreground/90 dark:text-muted-foreground/80">
            Enter your credentials to access your account
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-foreground/90">Email</FormLabel>
                    <FormControl>
                      <Input
                        type="email"
                        placeholder="<EMAIL>"
                        autoComplete="email"
                        className="h-11 bg-background border-primary/10 dark:border-primary/20 transition-colors"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <div className="flex items-center justify-between">
                      <FormLabel className="text-foreground/90">
                        Password
                      </FormLabel>
                      <Button
                        variant="link"
                        className="px-0 h-auto text-sm bg-gradient-to-r from-emerald-500 to-blue-600 bg-clip-text text-transparent hover:from-emerald-600 hover:to-blue-700"
                        type="button"
                        onClick={() => navigate("/forgot-password")}
                      >
                        Forgot password?
                      </Button>
                    </div>
                    <FormControl>
                      <Input
                        type="password"
                        autoComplete="current-password"
                        className="h-11 bg-background border-primary/10 dark:border-primary/20 transition-colors"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <Button
                type="submit"
                className="w-full h-11 bg-gradient-to-r from-emerald-500 to-blue-600 hover:from-emerald-600 hover:to-blue-700 text-white transition-all duration-300 shadow-lg hover:shadow-xl disabled:opacity-50"
                disabled={isLoading}
              >
                {isLoading ? (
                  <span className="flex items-center justify-center">
                    <svg
                      className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                    Signing in...
                  </span>
                ) : (
                  "Sign In"
                )}
              </Button>
            </form>
          </Form>

          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-primary/10 dark:border-primary/20"></div>
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-white/80 dark:bg-card px-2 text-muted-foreground/80 dark:text-muted-foreground/70">
                Or continue with
              </span>
            </div>
          </div>

          <Accordion type="single" collapsible className="w-full">
            <AccordionItem
              value="test-accounts"
              className="border-primary/10 dark:border-primary/20"
            >
              <AccordionTrigger className="py-2 text-sm bg-gradient-to-r from-emerald-500 to-blue-600 bg-clip-text text-transparent hover:no-underline hover:from-emerald-600 hover:to-blue-700">
                Use a test account
              </AccordionTrigger>
              <AccordionContent className="pb-0">
                <div className="rounded-lg border border-primary/10 dark:border-white/10 overflow-hidden bg-white/50 dark:bg-slate-900/90">
                  <Table>
                    <TableHeader className="bg-muted/20 dark:bg-slate-800/80">
                      <TableRow className="border-b border-border/50 dark:border-white/10">
                        <TableHead className="w-[120px] text-muted-foreground/90 dark:text-white/90 font-semibold">
                          Role
                        </TableHead>
                        <TableHead className="text-muted-foreground/90 dark:text-white/90 font-semibold">
                          Email
                        </TableHead>
                        <TableHead className="text-right text-muted-foreground/90 dark:text-white/90 font-semibold">
                          Action
                        </TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {testUsers.map((user) => (
                        <TableRow
                          key={user.email}
                          className="hover:bg-slate-100/80 dark:hover:bg-slate-700/50 border-b border-border/50 dark:border-white/5 transition-colors"
                        >
                          <TableCell className="font-medium text-foreground dark:text-white/95">
                            {user.role}
                          </TableCell>
                          <TableCell className="text-muted-foreground/90 dark:text-white/70">
                            {user.email}
                          </TableCell>
                          <TableCell className="text-right">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() =>
                                fillCredentials(user.email, user.password)
                              }
                              className="text-xs h-7 text-primary hover:text-primary/90 hover:bg-primary/10 dark:hover:bg-primary/20 dark:text-primary dark:hover:text-primary/90 transition-colors"
                            >
                              Use
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </CardContent>
      </Card>
    </div>
  );
}
