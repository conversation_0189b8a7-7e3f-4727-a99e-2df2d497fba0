import { LoadingScreen } from "@/components/ui/loading-screen";
import { useAuth } from "@/hooks/useAuth";
import secureStorage from "@/lib/secure-storage";
import { ReactNode } from "react";
import { Navigate, useLocation } from "react-router-dom";

/**
 * Check if the user has any existing organizations by querying the database
 * @param userId The user ID to check
 * @returns True if the user has existing organizations, false otherwise
 */
function checkForExistingOrganizations(userId: string): boolean {
  // First check secure storage for cached organization data
  try {
    // Check for the last selected organization
    const lastOrgUser = secureStorage.getOrganizationData<string>("last_org_user");
    const lastOrgId = secureStorage.getOrganizationData<string>("last_org");

    if (lastOrgUser === userId && lastOrgId) {
      return true;
    }

    // Check for any organization data in secure storage
    const keys = Object.keys(sessionStorage);
    const orgKeys = keys.filter(
      (key) => key.startsWith("spritely_secure_org_") && key.includes(userId),
    );

    if (orgKeys.length > 0) {
      // User has organization data in secure storage
      return true;
    }

    // Also check for the user_roles key which might indicate existing organizations
    const userRolesKey = `user_roles_${userId}`;
    if (secureStorage.get(userRolesKey)) {
      return true;
    }
  } catch (e) {
    console.warn("Error checking secure storage for organizations:", e);
  }

  // If no cached data, we'll return false and let the component handle the API call
  return false;
}

type ProtectedRouteProps = {
  children: ReactNode;
  requireOrganization?: boolean;
};

export function ProtectedRoute({
  children,
  requireOrganization = true,
}: ProtectedRouteProps) {
  const { user, organization, isLoading } = useAuth();
  const location = useLocation();

  if (isLoading) {
    return <LoadingScreen />;
  }

  if (!user) {
    // Redirect to login if not authenticated
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // If organization is required but not found, check if we should go to setup or organization selection
  if (requireOrganization && !organization) {
    // First, check if we're already on the setup or organizations page to avoid redirect loops
    if (
      location.pathname === "/setup" ||
      location.pathname === "/organizations"
    ) {
      // Already on the correct page, don't redirect
      return <>{children}</>;
    }

    // Check if the user has existing organizations by looking at secure storage
    const hasExistingOrgs = checkForExistingOrganizations(user.id);

    if (hasExistingOrgs) {
      // If user has organizations but none selected, go to organization selection
      return <Navigate to="/organizations" replace />;
    } else {
      // If user has no organizations, go to setup to create one
      return <Navigate to="/setup" replace />;
    }
  }

  return <>{children}</>;
}
