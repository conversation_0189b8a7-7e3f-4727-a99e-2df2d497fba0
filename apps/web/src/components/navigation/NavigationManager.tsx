import { useAuth } from "@/hooks/useAuth";
import { useUserRoles } from "@/hooks/useUserRoles";
import { supabase } from "@/lib/supabase";
import { useOrganizationStore } from "@/stores/organization-store";
import { useEffect, useRef } from "react";
import { useLocation, useNavigate } from "react-router-dom";

type NavigationManagerProps = {
  children: React.ReactNode;
};

export function NavigationManager({ children }: NavigationManagerProps) {
  const { user, organization, isLoading } = useAuth();
  const { isSystemAdmin } = useUserRoles();
  const { currentOrg } = useOrganizationStore();
  const location = useLocation();
  const navigate = useNavigate();

  const currentPath = location.pathname;
  const effectiveOrganization = organization || currentOrg;

  // Track previous user state to detect auth changes
  const prevUserRef = useRef(user);
  const hasInitialized = useRef(false);

  // Listen directly to Supabase auth changes for immediate navigation
  useEffect(() => {
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        // Handle immediate navigation on auth events
        if (event === "SIGNED_IN" && session?.user) {
          const authRoutes = ["/login", "/register", "/forgot-password", "/reset-password"];
          if (authRoutes.includes(currentPath)) {
            navigate("/dashboard", { replace: true });
          }
        } else if (event === "SIGNED_OUT") {
          const publicRoutes = ["/", "/login", "/register", "/forgot-password", "/reset-password"];
          if (!publicRoutes.includes(currentPath)) {
            navigate("/login", { replace: true });
          }
        }
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, [currentPath, navigate]);

  // Handle auth state changes with imperative navigation
  useEffect(() => {
    const prevUser = prevUserRef.current;
    const currentUser = user;

    // Update ref for next comparison
    prevUserRef.current = currentUser;

    // Skip initial render
    if (!hasInitialized.current) {
      hasInitialized.current = true;
      return;
    }

    // Skip if still loading
    if (isLoading) {
      return;
    }

    // User just logged in - redirect from auth pages to dashboard
    if (!prevUser && currentUser) {
      const authRoutes = ["/login", "/register", "/forgot-password", "/reset-password"];
      if (authRoutes.includes(currentPath)) {
        navigate("/dashboard", { replace: true });
        return;
      }
    }

    // User just logged out - redirect from protected routes to login
    if (prevUser && !currentUser) {
      const publicRoutes = ["/", "/login", "/register", "/forgot-password", "/reset-password"];
      if (!publicRoutes.includes(currentPath)) {
        navigate("/login", { replace: true });
        return;
      }
    }
  }, [user, isLoading, currentPath, navigate]);

  // Handle initial route protection (only on first load, not auth changes)
  useEffect(() => {
    // Skip if still loading
    if (isLoading) {
      return;
    }

    // Skip if this is the first render
    if (!hasInitialized.current) {
      hasInitialized.current = true;
      return;
    }

    const authRoutes = ["/login", "/register", "/forgot-password", "/reset-password"];
    const protectedRoutes = ["/dashboard", "/settings", "/profile", "/organizations"];
    const protectedPatterns = ["/patients/", "/organizations/"];

    const isAuthRoute = authRoutes.includes(currentPath);
    const isProtectedRoute = protectedRoutes.includes(currentPath) ||
      protectedPatterns.some(pattern => currentPath.startsWith(pattern));

    // If user is logged in and on auth page, redirect to dashboard
    if (user && isAuthRoute) {
      navigate("/dashboard", { replace: true });
      return;
    }

    // If user is not logged in and on protected route, redirect to login
    if (!user && isProtectedRoute) {
      navigate("/login", { replace: true });
      return;
    }

    // Only redirect for organization check if we're not on a patient-related page
    // This prevents the redirect loop on patient pages
    // Skip redirect if user is a system admin - they can work without an organization selected
    if (user && !effectiveOrganization && isProtectedRoute &&
        !currentPath.startsWith("/patients") &&
        currentPath !== "/setup" && currentPath !== "/organizations" &&
        !isSystemAdmin) {
      // Only redirect non-system admins to setup
      navigate("/setup", { replace: true });
      return;
    }
  }, [user, organization, currentOrg, isSystemAdmin, currentPath, navigate, isLoading, effectiveOrganization]);

  // Always render children - navigation happens via useEffect
  return <>{children}</>;
}
