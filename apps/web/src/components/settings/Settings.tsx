import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>eader,
  CardTitle,
} from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { useAuth } from "@/hooks/useAuth";
import { Bell, Palette, Shield, User } from "lucide-react";
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import AppearanceSettings from "./AppearanceSettings";
import NotificationSettings from "./NotificationSettings";
import ProfileSettings from "./ProfileSettings";
import SecuritySettings from "./SecuritySettings";

export default function Settings() {
  const { user, organization, signOut, updatePassword } = useAuth();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState("account");

  return (
    <div className="w-full max-w-full">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-6 gap-4">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Settings</h1>
          <p className="text-muted-foreground">
            Manage your account settings and preferences
          </p>
        </div>
      </div>

      <Card className="shadow-sm overflow-hidden">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg font-medium">User Settings</CardTitle>
          </div>
          <CardDescription>
            Manage your account settings and preferences
          </CardDescription>
        </CardHeader>
        <CardContent className="p-0">
          <Tabs
            defaultValue="account"
            value={activeTab}
            onValueChange={setActiveTab}
            className="w-full"
          >
            <div className="border-b">
              <div className="px-4">
                <TabsList className="h-10 w-full justify-start rounded-none border-b-0 bg-transparent p-0">
                  <TabsTrigger
                    value="account"
                    className="rounded-none border-b-2 border-b-transparent data-[state=active]:border-b-primary data-[state=active]:bg-transparent"
                  >
                    <User className="mr-2 h-4 w-4" />
                    Account
                  </TabsTrigger>
                  <TabsTrigger
                    value="security"
                    className="rounded-none border-b-2 border-b-transparent data-[state=active]:border-b-primary data-[state=active]:bg-transparent"
                  >
                    <Shield className="mr-2 h-4 w-4" />
                    Security
                  </TabsTrigger>
                  <TabsTrigger
                    value="notifications"
                    className="rounded-none border-b-2 border-b-transparent data-[state=active]:border-b-primary data-[state=active]:bg-transparent"
                  >
                    <Bell className="mr-2 h-4 w-4" />
                    Notifications
                  </TabsTrigger>
                  <TabsTrigger
                    value="appearance"
                    className="rounded-none border-b-2 border-b-transparent data-[state=active]:border-b-primary data-[state=active]:bg-transparent"
                  >
                    <Palette className="mr-2 h-4 w-4" />
                    Appearance
                  </TabsTrigger>
                </TabsList>
              </div>
            </div>

            <TabsContent value="account" className="p-4 sm:p-6 space-y-6">
              {user && (
                <ProfileSettings user={user} organization={organization} />
              )}
            </TabsContent>

            <TabsContent value="security" className="p-4 sm:p-6 space-y-6">
              <SecuritySettings updatePassword={updatePassword} />
            </TabsContent>

            <TabsContent value="notifications" className="p-4 sm:p-6 space-y-6">
              {user && <NotificationSettings userId={user.id} />}
            </TabsContent>

            <TabsContent value="appearance" className="p-4 sm:p-6 space-y-6">
              <AppearanceSettings />
            </TabsContent>
          </Tabs>
        </CardContent>
        <CardFooter className="border-t p-4 bg-muted/50 flex justify-between">
          <Button variant="outline" onClick={() => navigate("/dashboard")}>
            Back to Dashboard
          </Button>
          <Button variant="destructive" onClick={() => signOut()}>
            Sign Out
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
