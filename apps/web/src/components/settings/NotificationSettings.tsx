import { Separator } from "@/components/ui/separator";
import { Switch } from "@/components/ui/switch";
import { supabase } from "@/lib/supabase";
import { Database } from "@spritely/supabase-types";
import { Bell } from "lucide-react";
import { useEffect, useState } from "react";

type NotificationType = Database["public"]["Enums"]["notification_type"];

interface NotificationSettingsProps {
  userId: string;
}

export default function NotificationSettings({
  userId,
}: NotificationSettingsProps) {
  // Notification preferences
  const [emailNotifications, setEmailNotifications] = useState(true);
  const [smsNotifications, setSmsNotifications] = useState(false);
  const [browserNotifications, setBrowserNotifications] = useState(true);
  const [notificationTypes, setNotificationTypes] = useState<
    Record<NotificationType, boolean>
  >({
    appointment_reminder: true,
    lab_result: true,
    prescription_update: true,
    medical_record_update: true,
    task_assignment: true,
    message: true,
    alert: true,
    system_update: true,
  });

  // Load notification preferences
  useEffect(() => {
    if (!userId) return;

    const loadNotificationPreferences = async () => {
      try {
        const { data, error } = await supabase
          .from("notification_preferences")
          .select("*")
          .eq("user_id", userId);

        if (error) {
          console.error("Error loading notification preferences:", error);
          return;
        }

        if (data && data.length > 0) {
          // Create a map of notification types
          const typeMap: Record<NotificationType, boolean> = {
            appointment_reminder: true,
            lab_result: true,
            prescription_update: true,
            medical_record_update: true,
            task_assignment: true,
            message: true,
            alert: true,
            system_update: true,
          };

          // Process each notification preference
          data.forEach(
            (
              pref: Database["public"]["Tables"]["notification_preferences"]["Row"],
            ) => {
              if (pref.type && isValidNotificationType(pref.type)) {
                // Update the notification type enabled status
                typeMap[pref.type as NotificationType] = Boolean(
                  pref.email_enabled || pref.sms_enabled || pref.in_app_enabled,
                );

                // If this is the appointment_reminder type, update the channel states
                if (pref.type === "appointment_reminder") {
                  setEmailNotifications(pref.email_enabled || false);
                  setSmsNotifications(pref.sms_enabled || false);
                  setBrowserNotifications(pref.in_app_enabled || false);
                }
              }
            },
          );

          // Helper function to validate notification type
          function isValidNotificationType(
            type: string,
          ): type is NotificationType {
            return [
              "appointment_reminder",
              "lab_result",
              "prescription_update",
              "medical_record_update",
              "task_assignment",
              "message",
              "alert",
              "system_update",
            ].includes(type);
          }

          // Update the notification types state
          setNotificationTypes(typeMap);
        }
      } catch (err) {
        console.error("Error loading notification preferences:", err);
      }
    };

    loadNotificationPreferences();
  }, [userId]);

  // Save notification preferences
  const saveNotificationPreference = async (
    type: NotificationType,
    channel: "email" | "sms" | "in_app",
    enabled: boolean,
  ) => {
    if (!userId) return;

    try {
      // Create the update object with the specific channel enabled
      const updateData: Database["public"]["Tables"]["notification_preferences"]["Insert"] =
        {
          user_id: userId,
          type: type,
        };

      // Set the specific channel
      if (channel === "email") updateData.email_enabled = enabled;
      if (channel === "sms") updateData.sms_enabled = enabled;
      if (channel === "in_app") updateData.in_app_enabled = enabled;

      // Update the database
      const { error } = await supabase
        .from("notification_preferences")
        .upsert(updateData, { onConflict: "user_id,type" });

      if (error) {
        console.error("Error saving notification preference:", error);
      }
    } catch (err) {
      console.error("Error saving notification preference:", err);
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <div className="flex items-center gap-2 mb-4">
          <Bell className="h-5 w-5 text-primary" />
          <h3 className="text-lg font-medium">Notification Preferences</h3>
        </div>

        <div className="space-y-4">
          <div className="flex items-center justify-between rounded-lg border p-4">
            <div className="space-y-0.5">
              <div className="font-medium">Email Notifications</div>
              <div className="text-sm text-muted-foreground">
                Receive email notifications for important updates
              </div>
            </div>
            <Switch
              checked={emailNotifications}
              onCheckedChange={(checked) => {
                setEmailNotifications(checked);
                saveNotificationPreference(
                  "appointment_reminder",
                  "email",
                  checked,
                );
              }}
              aria-label="Toggle email notifications"
            />
          </div>

          <div className="flex items-center justify-between rounded-lg border p-4">
            <div className="space-y-0.5">
              <div className="font-medium">SMS Notifications</div>
              <div className="text-sm text-muted-foreground">
                Receive text messages for urgent matters
              </div>
            </div>
            <Switch
              checked={smsNotifications}
              onCheckedChange={(checked) => {
                setSmsNotifications(checked);
                saveNotificationPreference(
                  "appointment_reminder",
                  "sms",
                  checked,
                );
              }}
              aria-label="Toggle SMS notifications"
            />
          </div>

          <div className="flex items-center justify-between rounded-lg border p-4">
            <div className="space-y-0.5">
              <div className="font-medium">Browser Notifications</div>
              <div className="text-sm text-muted-foreground">
                Receive notifications in your browser
              </div>
            </div>
            <Switch
              checked={browserNotifications}
              onCheckedChange={(checked) => {
                setBrowserNotifications(checked);
                saveNotificationPreference(
                  "appointment_reminder",
                  "in_app",
                  checked,
                );
              }}
              aria-label="Toggle browser notifications"
            />
          </div>
        </div>
      </div>

      <Separator />

      <div>
        <div className="flex items-center gap-2 mb-4">
          <Bell className="h-5 w-5 text-primary" />
          <h3 className="text-lg font-medium">Notification Types</h3>
        </div>

        <div className="space-y-4">
          {(
            [
              "appointment_reminder",
              "lab_result",
              "prescription_update",
              "message",
            ] as NotificationType[]
          ).map((type) => (
            <div
              key={type}
              className="flex items-center justify-between rounded-lg border p-4"
            >
              <div className="space-y-0.5">
                <div className="font-medium">
                  {type
                    .split("_")
                    .map(
                      (word: string) =>
                        word.charAt(0).toUpperCase() + word.slice(1),
                    )
                    .join(" ")}
                </div>
                <div className="text-sm text-muted-foreground">
                  Receive notifications for {type.replace(/_/g, " ")}s
                </div>
              </div>
              <Switch
                checked={notificationTypes[type]}
                onCheckedChange={(checked) => {
                  // Update the notification types state
                  setNotificationTypes((prev) => ({
                    ...prev,
                    [type]: checked,
                  }));

                  // Enable/disable all channels for this notification type
                  saveNotificationPreference(type, "email", checked);
                  saveNotificationPreference(type, "sms", checked);
                  saveNotificationPreference(type, "in_app", checked);

                  // Update UI state if this is the appointment_reminder type
                  if (type === "appointment_reminder") {
                    setEmailNotifications(checked);
                    setSmsNotifications(checked);
                    setBrowserNotifications(checked);
                  }
                }}
                aria-label={`Toggle ${type} notifications`}
              />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
