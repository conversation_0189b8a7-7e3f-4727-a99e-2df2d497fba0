import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Switch } from "@/components/ui/switch";
import { ThemeToggle } from "@/components/ui/theme-toggle";
import { secureStorage } from "@/lib/secure-storage";
import { Globe, Palette } from "lucide-react";
import { useEffect, useState } from "react";

export default function AppearanceSettings() {
  // Appearance settings
  const [compactMode, setCompactMode] = useState(false);
  const [reducedMotion, setReducedMotion] = useState(false);

  // Load appearance settings from secure storage
  useEffect(() => {
    const settings = secureStorage.get("appearance-settings");
    if (settings && typeof settings === 'object') {
      try {
        const typedSettings = settings as { compactMode?: boolean; reducedMotion?: boolean };
        if (typedSettings.compactMode !== undefined) {
          setCompactMode(typedSettings.compactMode);
          if (typedSettings.compactMode) {
            document.body.classList.add("compact-mode");
          }
        }
        if (typedSettings.reducedMotion !== undefined) {
          setReducedMotion(typedSettings.reducedMotion);
          if (typedSettings.reducedMotion) {
            document.body.classList.add("reduced-motion");
          }
        }
      } catch (err) {
        console.error("Error parsing appearance settings:", err);
      }
    }
  }, []);

  return (
    <div className="space-y-6">
      <div>
        <div className="flex items-center gap-2 mb-4">
          <Palette className="h-5 w-5 text-primary" />
          <h3 className="text-lg font-medium">Theme Settings</h3>
        </div>

        <div className="space-y-4">
          <div className="flex items-center justify-between rounded-lg border p-4">
            <div className="space-y-0.5">
              <div className="font-medium">Dark Mode</div>
              <div className="text-sm text-muted-foreground">
                Toggle between light and dark mode
              </div>
            </div>
            <ThemeToggle />
          </div>

          <div className="flex items-center justify-between rounded-lg border p-4">
            <div className="space-y-0.5">
              <div className="font-medium">Compact Mode</div>
              <div className="text-sm text-muted-foreground">
                Reduce spacing in the interface
              </div>
            </div>
            <Switch
              checked={compactMode}
              onCheckedChange={(checked) => {
                setCompactMode(checked);
                // Save preference to secure storage
                const currentSettings = secureStorage.get("appearance-settings") as { compactMode?: boolean; reducedMotion?: boolean } || {};
                secureStorage.setUserSetting("appearance-settings", {
                  ...currentSettings,
                  compactMode: checked,
                });

                // Apply compact mode class to body
                if (checked) {
                  document.body.classList.add("compact-mode");
                } else {
                  document.body.classList.remove("compact-mode");
                }
              }}
              aria-label="Toggle compact mode"
            />
          </div>

          <div className="flex items-center justify-between rounded-lg border p-4">
            <div className="space-y-0.5">
              <div className="font-medium">Reduced Motion</div>
              <div className="text-sm text-muted-foreground">
                Minimize animations throughout the application
              </div>
            </div>
            <Switch
              checked={reducedMotion}
              onCheckedChange={(checked) => {
                setReducedMotion(checked);
                // Save preference to secure storage
                const currentSettings = secureStorage.get("appearance-settings") as { compactMode?: boolean; reducedMotion?: boolean } || {};
                secureStorage.setUserSetting("appearance-settings", {
                  ...currentSettings,
                  reducedMotion: checked,
                });

                // Apply reduced motion class to body
                if (checked) {
                  document.body.classList.add("reduced-motion");
                } else {
                  document.body.classList.remove("reduced-motion");
                }
              }}
              aria-label="Toggle reduced motion"
            />
          </div>
        </div>
      </div>

      <Separator />

      <div>
        <div className="flex items-center gap-2 mb-4">
          <Globe className="h-5 w-5 text-primary" />
          <h3 className="text-lg font-medium">Regional Settings</h3>
        </div>

        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="language">Language</Label>
              <select
                id="language"
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                defaultValue="en-US"
              >
                <option value="en-US">English (US)</option>
                <option value="en-GB">English (UK)</option>
                <option value="es">Spanish</option>
                <option value="fr">French</option>
              </select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="dateFormat">Date Format</Label>
              <select
                id="dateFormat"
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                defaultValue="MM/DD/YYYY"
              >
                <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                <option value="YYYY-MM-DD">YYYY-MM-DD</option>
              </select>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
