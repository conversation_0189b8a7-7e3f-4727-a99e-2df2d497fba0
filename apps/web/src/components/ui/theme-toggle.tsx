import { Button } from "@/components/ui/button";
import { useTheme } from "@/providers/theme-provider";
import { Moon, Sun } from "lucide-react";

export function ThemeToggle() {
  const { theme, setTheme } = useTheme();

  const toggleTheme = () => {
    setTheme(theme === "light" ? "dark" : "light");
  };

  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={toggleTheme}
      aria-label="Toggle theme"
      className="h-8 w-8 p-0 rounded-full hover:bg-accent/50 shrink-0"
    >
      {/* Moon shown in light mode */}
      <Moon className="h-4 w-4 rotate-0 scale-100 transition-all dark:rotate-90 dark:scale-0 text-sky-400" />

      {/* Sun shown in dark mode */}
      <Sun className="absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100 text-amber-500" />
      <span className="sr-only">Toggle theme</span>
    </Button>
  );
}
