import { cn } from "@/lib/utils";
import { motion } from "framer-motion";
import { useNavigate } from "react-router-dom";

interface LogoProps {
  size?: "sm" | "md" | "lg" | "xl";
  showText?: boolean;
  className?: string;
  iconClassName?: string;
  textClassName?: string;
  variant?: "default" | "auth" | "loading";
  onClick?: () => void;
}

const sizeConfig = {
  sm: {
    container: "w-6 h-6",
    text: "text-sm",
    fontSize: "text-sm",
  },
  md: {
    container: "w-8 h-8",
    text: "text-xl",
    fontSize: "text-xl",
  },
  lg: {
    container: "w-16 h-16",
    text: "text-2xl",
    fontSize: "text-4xl",
  },
  xl: {
    container: "w-20 h-20",
    text: "text-3xl",
    fontSize: "text-2xl",
  },
};

export function Logo({
  size = "md",
  showText = true,
  className,
  iconClassName,
  textClassName,
  variant = "default",
  onClick,
}: LogoProps) {
  const navigate = useNavigate();
  const config = sizeConfig[size];

  const handleClick = () => {
    if (onClick) {
      onClick();
    } else {
      navigate("/");
    }
  };

  const logoIcon = (
    <div className={cn("relative", config.container, iconClassName)}>
      <div className="absolute inset-0 bg-gradient-to-tr from-emerald-500 to-blue-600 rounded-lg transform rotate-45 shadow-md" />
      <div className={cn(
        "absolute inset-0 flex items-center justify-center text-white font-bold",
        config.fontSize
      )}>
        S
      </div>
      {variant === "loading" && (
        <div className="absolute inset-0 bg-gradient-to-tr from-emerald-500 to-blue-600 rounded-lg transform rotate-45 blur-md opacity-30 -z-10" />
      )}
    </div>
  );

  const logoText = showText && (
    <span className={cn(
      "font-bold bg-gradient-to-r from-emerald-500 to-blue-600 bg-clip-text text-transparent",
      config.text,
      textClassName
    )}>
      {variant === "auth" ? "SpritelyEHR" : "Spritely"}
    </span>
  );

  if (variant === "loading") {
    return (
      <div className={cn("flex flex-col items-center gap-3", className)}>
        {logoIcon}
        {logoText}
      </div>
    );
  }

  return (
    <motion.div
      whileHover={{ scale: 1.03 }}
      whileTap={{ scale: 0.97 }}
      className={cn(
        "flex items-center gap-2 cursor-pointer",
        variant === "auth" && "flex-col gap-3",
        className
      )}
      onClick={handleClick}
    >
      {logoIcon}
      {logoText}
    </motion.div>
  );
} 