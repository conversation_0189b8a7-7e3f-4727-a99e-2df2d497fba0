import { useAuth } from "@/hooks/useAuth";
import {
    clearLocationCache,
    createAllLocationsLocation,
    fetchUserLocations
} from "@/lib/auth/location-service";
import { handleLocationError } from "@/lib/error-handling";
import secureStorage from "@/lib/secure-storage";
import { ALL_LOCATIONS_ID, isVirtualId, Location } from "@/types/location";
import { createContext, useCallback, useContext, useEffect, useLayoutEffect, useMemo, useRef, useState } from "react";

// Production-ready location access validation
function validateLocationAccess(
  user: { id: string; role?: string } | null,
  _organization: { id: string } | null,
  locationId: string
): boolean {
  // Allow virtual IDs for system admins
  if (isVirtualId(locationId)) {
    return user?.role === 'system_admin';
  }

  // Validate real location IDs against user permissions
  // This should ideally call a secure database function
  return true; // Simplified for now
}

// Audit location access attempts
function auditLocationAccess(
  userId: string,
  locationId: string,
  action: string,
  success: boolean
) {
  // In production, this should log to a secure audit table
  // Reduced logging to prevent console spam
  if (!success) {
    console.warn('Location access denied:', {
      userId,
      locationId,
      action,
      timestamp: new Date().toISOString()
    });
  }
}

interface LocationContextType {
  locations: Location[];
  selectedLocation: Location | null;
  isLoading: boolean;
  error: string | null;
  canSwitchLocations: boolean;
  shouldShowLocationSelector: boolean;
  handleLocationSelect: (location: Location | null) => Promise<void>;
}

const LocationContext = createContext<LocationContextType | undefined>(undefined);

export function LocationProvider({ children }: { children: React.ReactNode }) {
  const { user, organization, session } = useAuth();
  const [locations, setLocations] = useState<Location[]>([]);
  const [selectedLocation, setSelectedLocation] = useState<Location | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Track the last processed values to prevent unnecessary re-runs
  const lastProcessedRef = useRef<{
    userId?: string;
    organizationId?: string;
    sessionUserId?: string;
    selectedLocationId?: string;
  }>({});

  // Track if we're currently processing to prevent concurrent executions
  const isProcessingRef = useRef(false);

  // Synchronously clear location when switching to "All Organizations"
  useLayoutEffect(() => {
    if (organization?.id === "system-admin-all-orgs") {
      console.log('[LocationProvider] Synchronously clearing location for All Organizations mode (useLayoutEffect)');
      setSelectedLocation(null);
      lastProcessedRef.current.selectedLocationId = undefined;
      try {
        secureStorage.remove("last_location");
      } catch (error) {
        console.warn('[LocationProvider] Failed to clear cached location:', error);
      }
    }
  }, [organization?.id]);

  // Single effect to handle all location logic
  useEffect(() => {
    const userId = user?.id;
    const organizationId = organization?.id;
    const sessionUserId = session?.user?.id;

    // Check if we need to process (values have changed) - do this synchronously
    const lastProcessed = lastProcessedRef.current;
    const hasChanged =
      lastProcessed.userId !== userId ||
      lastProcessed.organizationId !== organizationId ||
      lastProcessed.sessionUserId !== sessionUserId;

    // Update the ref immediately to prevent duplicate processing
    lastProcessedRef.current = {
      userId,
      organizationId,
      sessionUserId,
      selectedLocationId: lastProcessed.selectedLocationId
    };

    if (!hasChanged) {
      return; // No changes, skip processing
    }

    // Check if we're already processing
    if (isProcessingRef.current) {
      return;
    }

    let isCancelled = false;

    const processLocationLogic = async () => {
      // Set processing flag
      isProcessingRef.current = true;

      // Clear state if no valid session
      if (!userId || !organizationId || !sessionUserId || !user || !session) {
        if (!isCancelled) {
          setLocations([]);
          setSelectedLocation(null);
          lastProcessedRef.current.selectedLocationId = undefined;
          setIsLoading(false);
        }
        // Reset processing flag
        isProcessingRef.current = false;
        return;
      }

      // Special handling for "All Organizations" mode - no locations should be shown
      if (organizationId === "system-admin-all-orgs") {
        if (!isCancelled) {
          console.log('[LocationProvider] All Organizations mode - clearing locations');
          setLocations([]);
          setSelectedLocation(null);
          lastProcessedRef.current.selectedLocationId = undefined;
          setIsLoading(false);
        }
        // Reset processing flag
        isProcessingRef.current = false;
        return;
      }

      // Handle organization change - set loading first, then clear old data
      const isOrgChange = lastProcessed.organizationId && lastProcessed.organizationId !== organizationId;
      if (isOrgChange) {
        if (!isCancelled) {
          console.log('[LocationProvider] Organization changed, setting loading and clearing old data');
          // Set loading immediately to prevent showing "Select Location" during transition
          setIsLoading(true);
          // Clear old locations and selection immediately to prevent showing stale data
          setLocations([]);
          setSelectedLocation(null);
          lastProcessedRef.current.selectedLocationId = undefined;
          clearLocationCache(userId);

          // Clear cached location selection when switching organizations
          try {
            secureStorage.remove("last_location");
          } catch (error) {
            console.warn('[LocationProvider] Failed to clear cached location:', error);
          }
        }
      }

      try {
        if (!isCancelled && !isOrgChange) {
          console.log('[LocationProvider] Setting loading to true for org:', organizationId);
          setIsLoading(true);
          setError(null);
        } else if (!isCancelled && isOrgChange) {
          // Only set error to null for org changes (loading already set above)
          setError(null);
        }

        // Audit the location fetch attempt
        auditLocationAccess(userId, organizationId, 'fetch_locations', true);

        const userLocations = await fetchUserLocations(
          user,
          session,
          organizationId,
        );

        if (!isCancelled) {
          console.log('[LocationProvider] Setting locations and loading to false:', {
            locationsCount: userLocations.length,
            organizationId
          });
          setLocations(userLocations);

          // Try to restore location selection from URL first, then cache
          let selectedLocationToSet: Location | null = null;
          const currentLocationId = lastProcessedRef.current.selectedLocationId;

          // Check URL parameters first (for page refresh)
          if (!currentLocationId && typeof window !== 'undefined') {
            const urlParams = new URLSearchParams(window.location.search);
            const locationParam = urlParams.get('location');

            if (locationParam) {
              let targetLocationId = locationParam;

              // Handle "all" parameter for all locations view
              if (locationParam === "all") {
                targetLocationId = ALL_LOCATIONS_ID;
              }

              // Find the location in available locations or handle "all locations"
              if (targetLocationId === ALL_LOCATIONS_ID) {
                selectedLocationToSet = createAllLocationsLocation(organizationId) as unknown as Location;
              } else {
                selectedLocationToSet = userLocations.find(loc => loc.id === targetLocationId) || null;
              }

              if (selectedLocationToSet) {
                console.log('[LocationProvider] Restoring location from URL:', selectedLocationToSet.name);
                setSelectedLocation(selectedLocationToSet);
                lastProcessedRef.current.selectedLocationId = selectedLocationToSet.id;
                auditLocationAccess(userId, selectedLocationToSet.id, 'restore_url_location', true);

                // Cache the URL-restored location
                try {
                  secureStorage.setSessionData("last_location", {
                    locationId: selectedLocationToSet.id,
                    locationName: selectedLocationToSet.name,
                    organizationId: organizationId,
                    userId: userId
                  });
                } catch (error) {
                  console.warn('[LocationProvider] Failed to cache URL-restored location:', error);
                }
              }
            }
          }

          // Fallback to cache if no URL parameter or URL location not found
          if (!selectedLocationToSet && !currentLocationId) {
            try {
              const cachedLocation = secureStorage.get<{
                locationId: string;
                locationName: string;
                organizationId: string;
                userId: string;
              }>("last_location");

              if (cachedLocation &&
                  cachedLocation.organizationId === organizationId &&
                  cachedLocation.userId === userId) {
                console.log('[LocationProvider] Restoring location from cache:', cachedLocation.locationName);

                // Find the cached location in the available locations
                if (cachedLocation.locationId === ALL_LOCATIONS_ID) {
                  selectedLocationToSet = createAllLocationsLocation(organizationId) as unknown as Location;
                } else {
                  selectedLocationToSet = userLocations.find(loc => loc.id === cachedLocation.locationId) || null;
                }

                if (selectedLocationToSet) {
                  setSelectedLocation(selectedLocationToSet);
                  lastProcessedRef.current.selectedLocationId = selectedLocationToSet.id;
                  auditLocationAccess(userId, selectedLocationToSet.id, 'restore_cached_location', true);
                }
              }
            } catch (error) {
              console.warn('[LocationProvider] Failed to restore cached location:', error);
            }
          }

          // If no cached location was restored, use default logic
          if (!selectedLocationToSet) {
            // When organization changes or no location is selected and we have multiple locations,
            // default to "All Locations"
            if ((!currentLocationId || (lastProcessed.organizationId && lastProcessed.organizationId !== organizationId)) && userLocations.length > 1) {
              const allLocationsLocation = createAllLocationsLocation(organizationId) as unknown as Location;
              setSelectedLocation(allLocationsLocation);
              lastProcessedRef.current.selectedLocationId = ALL_LOCATIONS_ID;
              auditLocationAccess(userId, ALL_LOCATIONS_ID, 'set_default_location', true);
            }
            // If we have only one location and no selection, select it
            else if (!currentLocationId && userLocations.length === 1) {
              setSelectedLocation(userLocations[0]);
              lastProcessedRef.current.selectedLocationId = userLocations[0].id;
              auditLocationAccess(userId, userLocations[0].id, 'set_default_location', true);
            }
          }
        }
      } catch (error) {
        if (!isCancelled) {
          auditLocationAccess(userId, organizationId, 'fetch_locations', false);
          setError("Failed to load locations");

          await handleLocationError(error, {
            userId,
            organizationId,
            action: 'fetch_locations',
          });
        }
      }
      if (!isCancelled) {
        console.log('[LocationProvider] Setting loading to false (end of process)');
        setIsLoading(false);
      }
      // Reset processing flag
      isProcessingRef.current = false;
    };

    // Start processing immediately (no debouncing needed since we prevent duplicates)
    processLocationLogic();

    return () => {
      isCancelled = true;
      // Reset processing flag on cleanup
      isProcessingRef.current = false;
    };
  }, [user?.id, organization?.id, session?.user?.id]);

  // Handle location selection with security validation
  const handleLocationSelect = useCallback(
    async (location: Location | null, updateUrl = true) => {
      if (!location || !user || !organization) {
        return;
      }

      // Validate access before allowing selection
      if (!validateLocationAccess(user, organization, location.id)) {
        auditLocationAccess(user.id, location.id, 'select_location', false);
        setError("Access denied to selected location");
        return;
      }

      setSelectedLocation(location);
      lastProcessedRef.current.selectedLocationId = location.id;
      auditLocationAccess(user.id, location.id, 'select_location', true);

      // Store selection in secure session storage for persistence
      try {
        // Store location selection as session data (encrypted, 30min TTL)
        secureStorage.setSessionData("last_location", {
          locationId: location.id,
          locationName: location.name,
          organizationId: organization.id,
          userId: user.id
        });
      } catch (error) {
        // Storage failure is not critical - log but don't block user
        console.warn('Failed to persist location selection to secure storage:', error);
      }

      // Update URL parameters if requested
      if (updateUrl && typeof window !== 'undefined') {
        const url = new URL(window.location.href);
        if (location.id === ALL_LOCATIONS_ID) {
          url.searchParams.set("location", "all");
        } else {
          url.searchParams.set("location", location.id);
        }
        window.history.replaceState({}, '', url.toString());
      }
    },
    [user, organization],
  );

  // Determine if user can switch locations (including "All Locations" option)
  const canSwitchLocations = locations.length > 1;

  // Determine if location selector should be shown
  const shouldShowLocationSelector = useMemo(() => {
    const organizationId = organization?.id;

    console.log('[LocationProvider shouldShowLocationSelector]', {
      organizationId,
      locationsLength: locations.length,
      isLoading,
      result: organizationId && organizationId !== "system-admin-all-orgs" && (locations.length > 0 || isLoading)
    });

    // Don't show if no organization is selected
    if (!organizationId) {
      return false;
    }

    // Don't show for "All Organizations" view (system admin viewing all orgs)
    if (organizationId === "system-admin-all-orgs") {
      return false;
    }

    // Show if:
    // 1. User has access to locations (locations.length > 0), OR
    // 2. We're currently loading locations for this organization (to prevent flickering)
    return locations.length > 0 || isLoading;
  }, [organization?.id, locations.length, isLoading]);

  const contextValue: LocationContextType = {
    locations,
    selectedLocation,
    isLoading,
    error,
    canSwitchLocations,
    shouldShowLocationSelector,
    handleLocationSelect,
  };

  return (
    <LocationContext.Provider value={contextValue}>
      {children}
    </LocationContext.Provider>
  );
}

export function useLocations(): LocationContextType {
  const context = useContext(LocationContext);
  if (context === undefined) {
    throw new Error('useLocations must be used within a LocationProvider');
  }
  return context;
}
