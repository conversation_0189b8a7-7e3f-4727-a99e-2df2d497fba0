import { useAuth } from "@/hooks/useAuth";
import { useUserRoles } from "@/hooks/useUserRoles";
import { useLocations } from "@/hooks/useLocations";
import { createContext, useContext, ReactNode } from "react";

/**
 * Centralized app data provider to prevent duplicate hook calls
 * This provider fetches common data once and shares it with all child components
 */

interface AppDataContextType {
  // Auth data
  user: any;
  organization: any;
  session: any;
  isLoading: boolean;
  
  // User roles data
  roles: string[];
  organizationIds: string[];
  isSystemAdmin: boolean;
  isOrgAdmin: boolean;
  isAdmin: boolean;
  hasMultipleOrganizations: boolean;
  
  // Locations data
  locations: any[];
  selectedLocation: any;
  canSwitchLocations: boolean;
  shouldShowLocationSelector: boolean;
  handleLocationSelect: (location: any) => void;
  
  // Loading states
  isAuthLoading: boolean;
  isRolesLoading: boolean;
  isLocationsLoading: boolean;
  
  // Error states
  authError: any;
  rolesError: any;
  locationsError: any;
}

const AppDataContext = createContext<AppDataContextType | null>(null);

interface AppDataProviderProps {
  children: ReactNode;
}

export function AppDataProvider({ children }: AppDataProviderProps) {
  // Fetch all data once at the top level
  const authData = useAuth();
  const rolesData = useUserRoles();
  const locationsData = useLocations();

  const contextValue: AppDataContextType = {
    // Auth data
    user: authData.user,
    organization: authData.organization,
    session: authData.session,
    isLoading: authData.isLoading,
    
    // User roles data
    roles: rolesData.roles,
    organizationIds: rolesData.organizationIds,
    isSystemAdmin: rolesData.isSystemAdmin,
    isOrgAdmin: rolesData.isOrgAdmin,
    isAdmin: rolesData.isAdmin,
    hasMultipleOrganizations: rolesData.hasMultipleOrganizations,
    
    // Locations data
    locations: locationsData.locations,
    selectedLocation: locationsData.selectedLocation,
    canSwitchLocations: locationsData.canSwitchLocations,
    shouldShowLocationSelector: locationsData.shouldShowLocationSelector,
    handleLocationSelect: locationsData.handleLocationSelect,
    
    // Loading states
    isAuthLoading: authData.isLoading,
    isRolesLoading: rolesData.isLoading,
    isLocationsLoading: locationsData.isLoading,
    
    // Error states
    authError: null, // authData doesn't expose error
    rolesError: rolesData.error,
    locationsError: locationsData.error,
  };

  return (
    <AppDataContext.Provider value={contextValue}>
      {children}
    </AppDataContext.Provider>
  );
}

export function useAppData() {
  const context = useContext(AppDataContext);
  if (!context) {
    throw new Error("useAppData must be used within an AppDataProvider");
  }
  return context;
}

// Convenience hooks for specific data
export function useAppAuth() {
  const { user, organization, session, isLoading } = useAppData();
  return { user, organization, session, isLoading };
}

export function useAppUserRoles() {
  const { 
    roles, 
    organizationIds, 
    isSystemAdmin, 
    isOrgAdmin, 
    isAdmin, 
    hasMultipleOrganizations,
    isRolesLoading,
    rolesError 
  } = useAppData();
  
  return { 
    roles, 
    organizationIds, 
    isSystemAdmin, 
    isOrgAdmin, 
    isAdmin, 
    hasMultipleOrganizations,
    isLoading: isRolesLoading,
    error: rolesError 
  };
}

export function useAppLocations() {
  const { 
    locations, 
    selectedLocation, 
    canSwitchLocations, 
    shouldShowLocationSelector, 
    handleLocationSelect,
    isLocationsLoading,
    locationsError 
  } = useAppData();
  
  return { 
    locations, 
    selectedLocation, 
    canSwitchLocations, 
    shouldShowLocationSelector, 
    handleLocationSelect,
    isLoading: isLocationsLoading,
    error: locationsError 
  };
}
