import type { AuthState } from "@/lib/auth/auth-state-machine";
import type { Database } from "@spritely/supabase-types";
import type {
    AuthResponse,
    Session,
    User,
    UserResponse,
} from "@supabase/supabase-js";

export type Organization = Database["public"]["Tables"]["organizations"]["Row"];

export type AuthContextType = {
  user: User | null;
  session: Session | null;
  isLoading: boolean;
  organization: Organization | null;
  hasOrganization: boolean;
  authState: AuthState;
  signUp: (
    email: string,
    password: string,
    metadata?: {
      first_name?: string;
      last_name?: string;
      organization_id?: string;
    },
  ) => Promise<AuthResponse>;
  signIn: (email: string, password: string) => Promise<AuthResponse>;
  signOut: () => Promise<void>;
  resetPassword: (
    email: string,
  ) => Promise<{ data: object | null; error: Error | null }>;
  updatePassword: (password: string) => Promise<UserResponse>;
  setOrganization: (org: Organization | null) => void;
};
