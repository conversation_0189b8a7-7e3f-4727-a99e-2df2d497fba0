import { secureStorage } from "@/lib/secure-storage";
import { createContext, useContext, useEffect, useState } from "react";

type Theme = "dark" | "light";

type ThemeProviderProps = {
  children: React.ReactNode;
  defaultTheme?: Theme;
  storageKey?: string;
};

type ThemeProviderState = {
  theme: Theme;
  setTheme: (theme: Theme) => void;
};

const initialState: ThemeProviderState = {
  theme: "dark",
  setTheme: () => null,
};

const ThemeProviderContext = createContext<ThemeProviderState>(initialState);

export function ThemeProvider({
  children,
  defaultTheme = "dark",
  storageKey = "spritely-theme",
  ...props
}: ThemeProviderProps) {
  const [theme, setTheme] = useState<Theme>(() => {
    if (typeof window === "undefined") return defaultTheme;

    try {
      const storedTheme = secureStorage.get(storageKey);
      return (storedTheme && typeof storedTheme === 'string') ? (storedTheme as Theme) : defaultTheme;
    } catch {
      return defaultTheme;
    }
  });

  useEffect(() => {
    const root = window.document.documentElement;
    const html = document.documentElement;

    // Prevent layout shift by always showing scrollbar
    html.style.overflow = "scroll";

    root.classList.remove("light", "dark");
    root.classList.add(theme);
    root.style.colorScheme = theme;

    // Add transition styles to specific properties only
    document.body.style.setProperty(
      "transition",
      "background-color 0.2s ease, color 0.2s ease, border-color 0.2s ease",
    );

    // Clean up
    return () => {
      document.body.style.removeProperty("transition");
    };
  }, [theme]);

  const value = {
    theme,
    setTheme: (theme: Theme) => {
      secureStorage.setUIPreference(storageKey, theme);
      setTheme(theme);
    },
  };

  return (
    <ThemeProviderContext.Provider {...props} value={value}>
      {children}
    </ThemeProviderContext.Provider>
  );
}

export const useTheme = () => {
  const context = useContext(ThemeProviderContext);

  if (context === undefined)
    throw new Error("useTheme must be used within a ThemeProvider");

  return context;
};
