# URL Filter Persistence Test

## Changes Made

1. **Organization Store** (`stores/organization-store.ts`):
   - Modified `loadUserOrganizations` to check URL parameters first
   - Added URL parameter handling for "all" -> "system-admin-all-orgs" mapping
   - Falls back to cache if URL parameter not found

2. **Location Provider** (`contexts/LocationProvider.tsx`):
   - Modified location restoration logic to check URL parameters first
   - Added URL parameter handling for "all" -> ALL_LOCATIONS_ID mapping
   - Falls back to cache if URL parameter not found
   - Updated interface to support `updateUrl` parameter

3. **Organization Selector** (`components/organization/OrganizationSelector.tsx`):
   - Modified `handleOrganizationSelect` to pass `updateUrl: true` to `switchOrganization`

4. **Location Selector** (`components/location-selector/LocationSelector.tsx`):
   - Modified `handleLocationClick` to pass `updateUrl: true` to `handleLocationSelect`

5. **Auth Provider** (`contexts/AuthProvider.tsx`):
   - Added URL parameter sync when organization changes in auth context

## Test Steps

1. Navigate to any page (e.g., `/patients`)
2. Select a specific organization in the sidebar
3. Select a specific location in the sidebar
4. Verify URL shows: `?org=<org-id>&location=<location-id>`
5. Refresh the page
6. Verify sidebar shows the same organization and location
7. Verify URL parameters are preserved

## Expected Behavior

- When changing org/location in sidebar → URL updates immediately
- When refreshing page → sidebar restores from URL parameters
- URL parameters should persist across page refreshes
- No more resetting to "All Organizations" on refresh
