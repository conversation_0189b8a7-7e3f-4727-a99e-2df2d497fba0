# Spritely Mobile Application

This is the mobile frontend for the Spritely healthcare management system, built with React Native and Expo.

## Features

- **Authentication**: User login and session management using Supabase Auth
- **Organization Management**: View and manage healthcare organizations
- **Patient Management**: Track patient information on the go
- **Appointment Scheduling**: Schedule and manage patient appointments from mobile
- **Medical Records**: View patient medical records
- **Push Notifications**: Receive alerts for important events

## Tech Stack

- **Framework**: React Native
- **Build System**: Expo
- **State Management**: React Query and Context API
- **Authentication**: Supabase Auth
- **Database**: Supabase PostgreSQL
- **UI Components**: Native Base

## Development

### Prerequisites

- Node.js 16+
- npm or yarn
- Expo CLI: `npm install -g expo-cli`
- iOS Simulator (for Mac) or Android Emulator

### Environment Setup

Create a `.env` file in this directory with the following variables:

```
EXPO_PUBLIC_SUPABASE_URL=http://127.0.0.1:54321
EXPO_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0
```

### Running the Development Server

From the root of the monorepo:

```bash
# Navigate to the mobile app directory
cd apps/mobile

# Start the Expo development server
npm start
```

This will open the Expo developer tools in your browser. From there, you can:

- Run on an iOS Simulator
- Run on an Android Emulator
- Scan the QR code with the Expo Go app on your physical device

### Building for Production

```bash
# Build for iOS
expo build:ios

# Build for Android
expo build:android
```

## Project Structure

- `src/components/` - Reusable UI components
- `src/features/` - Feature-specific components and logic
- `src/hooks/` - Custom React hooks
- `src/navigation/` - Navigation configuration
- `src/screens/` - Screen components
- `src/services/` - API and service integrations
- `src/utils/` - Helper functions

## Authentication Flow

The application implements the following authentication flow:

1. Users sign in with their email and password
2. After successful authentication:
   - If the user has no organization, they are shown an error message
   - If the user has an organization, they are directed to the main app
3. Protected screens require authentication and redirect to login if not authenticated

## Shared Code

This mobile app shares code with the web application through the following packages:

- `supabase-types`: TypeScript definitions for the database schema
- `ui`: Shared UI components (adapted for React Native)
- `eslint-config`: Shared ESLint configurations

## Troubleshooting

### Common Issues

- **Supabase Connection Issues**: Make sure your local Supabase instance is running and the environment variables are set correctly
- **Expo Build Errors**: Check that all dependencies are compatible with the current Expo SDK version
- **iOS Simulator Issues**: Try resetting the simulator or clearing the app data
