{"name": "mobile", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "dev": "expo start", "dev:no-types": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@supabase/supabase-js": "^2.49.4", "expo": "~52.0.43", "expo-status-bar": "~2.0.1", "react": "18.3.1", "react-native": "0.76.9", "@spritely/supabase-types": "*"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/expo": "^32.0.13", "@types/react": "~18.3.12", "typescript": "^5.3.3"}, "private": true}