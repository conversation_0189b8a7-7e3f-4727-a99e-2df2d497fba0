{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env.*local"], "tasks": {"build": {"dependsOn": ["^build"], "outputs": ["dist/**", ".next/**", "!.next/cache/**"]}, "lint": {"outputs": []}, "check-types": {"outputs": []}, "dev": {"cache": false, "persistent": true, "dependsOn": ["^build"]}, "dev:no-types": {"cache": false, "persistent": true, "dependsOn": ["^build"]}, "dev:full": {"cache": false, "persistent": true, "dependsOn": ["supabase:setup", "^build"]}, "clean": {"cache": false}, "supabase:setup": {"cache": false}, "supabase:start": {"cache": false, "persistent": true}, "supabase:stop": {"cache": false}, "generate-types": {"outputs": ["packages/supabase-types/dist/**", "packages/supabase-types/src/generated-types.ts"]}}}