Enum "administration_route" {
  "intramuscular"
  "subcutaneous"
  "intradermal"
  "oral"
  "nasal"
}

Enum "administration_site" {
  "left_arm"
  "right_arm"
  "left_thigh"
  "right_thigh"
  "other"
}

Enum "alert_severity" {
  "low"
  "medium"
  "high"
  "critical"
}

Enum "alert_status" {
  "active"
  "inactive"
  "resolved"
}

Enum "allergy_severity" {
  "mild"
  "moderate"
  "severe"
  "life_threatening"
}

Enum "allergy_status" {
  "active"
  "inactive"
  "resolved"
}

Enum "appointment_status" {
  "scheduled"
  "checked_in"
  "in_progress"
  "completed"
  "cancelled"
  "no_show"
}

Enum "conversation_type" {
  "direct"
  "group"
  "department"
  "announcement"
}

Enum "department_type" {
  "primary_care"
  "pediatrics"
  "cardiology"
  "neurology"
  "orthopedics"
  "emergency"
  "laboratory"
  "pharmacy"
  "radiology"
  "billing"
  "administration"
}

Enum "gender" {
  "male"
  "female"
  "other"
  "prefer_not_to_say"
}

Enum "material_format" {
  "text"
  "pdf"
  "video"
  "audio"
  "interactive"
}

Enum "message_state" {
  "sent"
  "delivered"
  "read"
}

Enum "notification_priority" {
  "low"
  "medium"
  "high"
  "urgent"
}

Enum "notification_status" {
  "pending"
  "sent"
  "delivered"
  "read"
  "failed"
}

Enum "notification_type" {
  "appointment_reminder"
  "lab_result"
  "prescription_update"
  "medical_record_update"
  "task_assignment"
  "message"
  "alert"
  "system_update"
}

Enum "order_priority" {
  "routine"
  "urgent"
  "stat"
  "emergency"
}

Enum "order_status" {
  "pending"
  "approved"
  "in_progress"
  "completed"
  "cancelled"
  "declined"
}

Enum "order_type" {
  "lab"
  "imaging"
  "medication"
  "procedure"
  "referral"
  "consultation"
  "other"
}

Enum "provider_type" {
  "doctor"
  "nurse"
  "specialist"
  "admin"
}

Enum "referral_priority" {
  "routine"
  "urgent"
  "emergency"
}

Enum "referral_status" {
  "pending"
  "scheduled"
  "completed"
  "cancelled"
  "declined"
}

Enum "task_priority" {
  "low"
  "medium"
  "high"
  "urgent"
}

Enum "task_status" {
  "pending"
  "in_progress"
  "completed"
  "cancelled"
  "blocked"
}

Enum "user_role" {
  "system_admin"
  "org_admin"
  "clinical_admin"
  "physician"
  "nurse_practitioner"
  "registered_nurse"
  "medical_assistant"
  "front_desk"
  "billing_staff"
  "pharmacist"
  "lab_technician"
  "patient"
}

Enum "workflow_status" {
  "pending"
  "in_progress"
  "completed"
  "cancelled"
  "failed"
}

Enum "workflow_trigger" {
  "scheduled"
  "event_based"
  "manual"
}

Enum "workflow_type" {
  "appointment_reminder"
  "lab_result_notification"
  "prescription_renewal"
  "patient_followup"
  "referral_management"
  "insurance_verification"
  "document_review"
}

Table "activity_logs" {
  "id" uuid [pk, not null, default: `uuid_generate_v4()`]
  "organization_id" uuid [not null]
  "user_id" uuid [not null]
  "action_type" text [not null]
  "resource_type" text [not null]
  "resource_id" text [not null]
  "details" jsonb [default: `{}`]
  "created_at" timestamptz [default: `CURRENT_TIMESTAMP`]

  Indexes {
    created_at [type: btree, name: "idx_activity_logs_created_at"]
    organization_id [type: btree, name: "idx_activity_logs_organization_id"]
    resource_type [type: btree, name: "idx_activity_logs_resource_type"]
    user_id [type: btree, name: "idx_activity_logs_user_id"]
  }
}

Table "allergies" {
  "id" uuid [pk, not null, default: `uuid_generate_v4()`]
  "patient_id" uuid
  "allergen" text [not null]
  "reaction" text
  "severity" allergy_severity [not null]
  "onset_date" date
  "status" allergy_status [default: 'active']
  "reported_by" uuid
  "metadata" jsonb [default: `{}`]
  "created_at" timestamptz [default: `CURRENT_TIMESTAMP`]
  "updated_at" timestamptz [default: `CURRENT_TIMESTAMP`]

  Indexes {
    allergen [type: btree, name: "idx_allergies_allergen"]
    patient_id [type: btree, name: "idx_allergies_patient_id"]
    reported_by [type: btree, name: "idx_allergies_reported_by"]
    severity [type: btree, name: "idx_allergies_severity"]
    status [type: btree, name: "idx_allergies_status"]
  }
}

Table "appointments" {
  "id" uuid [pk, not null, default: `gen_random_uuid()`]
  "patient_id" uuid
  "provider_id" uuid
  "appointment_date" timestamptz [not null]
  "duration_minutes" int4 [not null, default: 30]
  "status" appointment_status [not null, default: 'scheduled']
  "reason" text
  "notes" text
  "created_at" timestamptz [default: `now()`]
  "updated_at" timestamptz [default: `now()`]
  "organization_id" uuid
  "department_id" uuid

  Indexes {
    appointment_date [type: brin, name: "idx_appointments_date_brin"]
    department_id [type: btree, name: "idx_appointments_department_id"]
    organization_id [type: btree, name: "idx_appointments_organization_id"]
    (patient_id, appointment_date) [type: btree, name: "idx_appointments_patient_date"]
    patient_id [type: btree, name: "idx_appointments_patient_id"]
    (provider_id, appointment_date) [type: btree, name: "idx_appointments_provider_date"]
    provider_id [type: btree, name: "idx_appointments_provider_id"]
    (provider_id, patient_id, appointment_date) [type: btree, name: "idx_appointments_provider_patient_date"]
    (provider_id, status, appointment_date) [type: btree, name: "idx_appointments_provider_status"]
    (status, appointment_date) [type: btree, name: "idx_appointments_status_date"]
    (status, provider_id, appointment_date) [type: btree, name: "idx_appointments_status_provider_date"]
  }
}

Table "analytics_events" {
  "id" uuid [pk, not null, default: `uuid_generate_v4()`]
  "organization_id" uuid
  "event_type" text [not null]
  "event_data" jsonb [not null]
  "user_id" uuid
  "timestamp" timestamptz [default: `CURRENT_TIMESTAMP`]
  "created_at" timestamptz [default: `CURRENT_TIMESTAMP`]

  Indexes {
    (organization_id, event_type) [type: btree, name: "idx_analytics_events_org_type"]
    organization_id [type: btree, name: "idx_analytics_events_organization_id"]
    timestamp [type: brin, name: "idx_analytics_events_timestamp_brin"]
    user_id [type: btree, name: "idx_analytics_events_user_id"]
  }
}

Table "analytics_metrics" {
  "id" uuid [pk, not null, default: `uuid_generate_v4()`]
  "organization_id" uuid
  "metric_name" text [not null]
  "metric_value" numeric [not null]
  "dimensions" jsonb [default: `{}`]
  "timestamp" timestamptz [default: `CURRENT_TIMESTAMP`]
  "created_at" timestamptz [default: `CURRENT_TIMESTAMP`]

  Indexes {
    (organization_id, metric_name) [type: btree, name: "idx_analytics_metrics_org_metric"]
    organization_id [type: btree, name: "idx_analytics_metrics_organization_id"]
    timestamp [type: brin, name: "idx_analytics_metrics_timestamp_brin"]
  }
}

Table "medical_records" {
  "id" uuid [pk, not null, default: `gen_random_uuid()`]
  "patient_id" uuid
  "provider_id" uuid
  "visit_date" timestamptz [not null]
  "chief_complaint" text
  "diagnosis" "text[]"
  "treatment_plan" text
  "notes" text
  "attachments" jsonb
  "created_at" timestamptz [default: `now()`]
  "updated_at" timestamptz [default: `now()`]
  "search_vector" tsvector
  "organization_id" uuid
  "department_id" uuid

  Indexes {
    department_id [type: btree, name: "idx_medical_records_department_id"]
    (organization_id, department_id) [type: btree, name: "idx_medical_records_org_dept"]
    organization_id [type: btree, name: "idx_medical_records_organization_id"]
    (patient_id, visit_date) [type: btree, name: "idx_medical_records_patient_date"]
    patient_id [type: btree, name: "idx_medical_records_patient_id"]
    provider_id [type: btree, name: "idx_medical_records_provider_id"]
    (provider_id, patient_id, visit_date) [type: btree, name: "idx_medical_records_provider_patient_date"]
    (provider_id, visit_date) [type: btree, name: "idx_medical_records_provider_visit"]
    visit_date [type: brin, name: "idx_medical_records_visit_date_brin"]
  }
}

Table "medications" {
  "id" uuid [pk, not null, default: `gen_random_uuid()`]
  "patient_id" uuid
  "provider_id" uuid
  "medication_name" text [not null]
  "dosage" text [not null]
  "frequency" text [not null]
  "start_date" date [not null]
  "end_date" date
  "instructions" text
  "active" bool [default: true]
  "created_at" timestamptz [default: `now()`]
  "updated_at" timestamptz [default: `now()`]

  Indexes {
    (patient_id, active) [type: btree, name: "idx_medications_patient_active"]
    patient_id [type: btree, name: "idx_medications_patient_id"]
    provider_id [type: btree, name: "idx_medications_provider_id"]
    (provider_id, patient_id, active) [type: btree, name: "idx_medications_provider_patient_active"]
  }
}

Table "patients" {
  "id" uuid [pk, not null, default: `gen_random_uuid()`]
  "user_id" uuid
  "first_name" text [not null]
  "last_name" text [not null]
  "date_of_birth" date [not null]
  "gender" gender [not null]
  "phone" text
  "email" text
  "address" text
  "emergency_contact" text
  "insurance_info" jsonb
  "medical_history" jsonb
  "created_at" timestamptz [default: `now()`]
  "updated_at" timestamptz [default: `now()`]
  "search_vector" tsvector
  "organization_id" uuid

  Indexes {
    organization_id [type: btree, name: "idx_patients_organization_id"]
    user_id [type: btree, name: "idx_patients_user_id"]
  }
}

Table "healthcare_providers" {
  "id" uuid [pk, not null, default: `gen_random_uuid()`]
  "user_id" uuid
  "first_name" text [not null]
  "last_name" text [not null]
  "provider_type" provider_type [not null]
  "specialization" text
  "license_number" text
  "created_at" timestamptz [default: `now()`]
  "updated_at" timestamptz [default: `now()`]
  "organization_id" uuid
  "department_id" uuid
  "role" user_role
  "specialties" "text[]"
  "credentials" jsonb
  "schedule_settings" jsonb
  "permissions" jsonb

  Indexes {
    department_id [type: btree, name: "idx_healthcare_providers_department_id"]
    organization_id [type: btree, name: "idx_healthcare_providers_organization_id"]
    user_id [type: btree, name: "idx_healthcare_providers_user_id"]
  }
}

Table "audit_logs" {
  "id" uuid [pk, not null, default: `uuid_generate_v4()`]
  "table_name" text [not null]
  "record_id" uuid [not null]
  "action" text [not null]
  "old_data" jsonb
  "new_data" jsonb
  "changed_by" uuid
  "timestamp" timestamptz [default: `CURRENT_TIMESTAMP`]

  Indexes {
    changed_by [type: btree, name: "idx_audit_logs_changed_by"]
    record_id [type: btree, name: "idx_audit_logs_record_id"]
    table_name [type: btree, name: "idx_audit_logs_table_name"]
    timestamp [type: btree, name: "idx_audit_logs_timestamp"]
  }
}

Table "billing_codes" {
  "id" uuid [pk, not null, default: `uuid_generate_v4()`]
  "code" text [not null]
  "description" text [not null]
  "type" text [not null]
  "effective_date" date [not null]
  "end_date" date
  "created_at" timestamptz [default: `CURRENT_TIMESTAMP`]
  "updated_at" timestamptz [default: `CURRENT_TIMESTAMP`]

  Indexes {
    code [type: btree, name: "idx_billing_codes_code"]
    type [type: btree, name: "idx_billing_codes_type"]
  }
}

Table "care_team_members" {
  "id" uuid [pk, not null, default: `uuid_generate_v4()`]
  "patient_id" uuid
  "provider_id" uuid
  "role" text [not null]
  "start_date" date [not null]
  "end_date" date
  "primary_contact" bool [default: false]
  "notes" text
  "metadata" jsonb [default: `{}`]
  "created_at" timestamptz [default: `CURRENT_TIMESTAMP`]
  "updated_at" timestamptz [default: `CURRENT_TIMESTAMP`]

  Indexes {
    patient_id [type: btree, name: "idx_care_team_members_patient_id"]
    primary_contact [type: btree, name: "idx_care_team_members_primary_contact"]
    provider_id [type: btree, name: "idx_care_team_members_provider_id"]
    role [type: btree, name: "idx_care_team_members_role"]
  }
}

Table "claims" {
  "id" uuid [pk, not null, default: `uuid_generate_v4()`]
  "patient_id" uuid
  "provider_id" uuid
  "insurance_provider_id" uuid
  "service_date" date [not null]
  "billing_codes" jsonb [not null]
  "status" text [not null]
  "amount" numeric [not null]
  "submitted_at" timestamptz
  "processed_at" timestamptz
  "metadata" jsonb [default: `{}`]
  "created_at" timestamptz [default: `CURRENT_TIMESTAMP`]
  "updated_at" timestamptz [default: `CURRENT_TIMESTAMP`]

  Indexes {
    insurance_provider_id [type: btree, name: "idx_claims_insurance_provider_id"]
    patient_id [type: btree, name: "idx_claims_patient_id"]
    provider_id [type: btree, name: "idx_claims_provider_id"]
    service_date [type: btree, name: "idx_claims_service_date"]
    status [type: btree, name: "idx_claims_status"]
  }
}

Table "clinical_notes" {
  "id" uuid [pk, not null, default: `uuid_generate_v4()`]
  "medical_record_id" uuid
  "note_type" text [not null]
  "content" text [not null]
  "signed_by" uuid
  "signed_at" timestamptz
  "metadata" jsonb [default: `{}`]
  "created_at" timestamptz [default: `CURRENT_TIMESTAMP`]
  "updated_at" timestamptz [default: `CURRENT_TIMESTAMP`]

  Indexes {
    medical_record_id [type: btree, name: "idx_clinical_notes_medical_record_id"]
    signed_by [type: btree, name: "idx_clinical_notes_signed_by"]
  }
}

Table "conversation_participants" {
  "id" uuid [pk, not null, default: `uuid_generate_v4()`]
  "conversation_id" uuid
  "user_id" uuid
  "role" text [default: 'member']
  "last_read_at" timestamptz
  "created_at" timestamptz [default: `CURRENT_TIMESTAMP`]

  Indexes {
    (conversation_id, user_id) [type: btree, name: "conversation_participants_conversation_id_user_id_key"]
    conversation_id [type: btree, name: "idx_conversation_participants_conversation_id"]
    user_id [type: btree, name: "idx_conversation_participants_user_id"]
  }
}

Table "conversations" {
  "id" uuid [pk, not null, default: `uuid_generate_v4()`]
  "organization_id" uuid
  "type" conversation_type [not null]
  "title" text
  "metadata" jsonb [default: `{}`]
  "created_by" uuid
  "created_at" timestamptz [default: `CURRENT_TIMESTAMP`]
  "updated_at" timestamptz [default: `CURRENT_TIMESTAMP`]

  Indexes {
    created_by [type: btree, name: "idx_conversations_created_by"]
    organization_id [type: btree, name: "idx_conversations_organization_id"]
  }
}

Table "departments" {
  "id" uuid [pk, not null, default: `uuid_generate_v4()`]
  "location_id" uuid
  "name" text [not null]
  "type" department_type [not null]
  "settings" jsonb [default: `{}`]
  "created_at" timestamptz [default: `CURRENT_TIMESTAMP`]
  "updated_at" timestamptz [default: `CURRENT_TIMESTAMP`]

  Indexes {
    location_id [type: btree, name: "idx_departments_location_id"]
  }
}

Table "documents" {
  "id" uuid [pk, not null, default: `uuid_generate_v4()`]
  "organization_id" uuid
  "patient_id" uuid
  "document_type" text [not null]
  "title" text [not null]
  "description" text
  "file_path" text [not null]
  "mime_type" text [not null]
  "metadata" jsonb [default: `{}`]
  "created_by" uuid
  "created_at" timestamptz [default: `CURRENT_TIMESTAMP`]
  "updated_at" timestamptz [default: `CURRENT_TIMESTAMP`]

  Indexes {
    created_by [type: btree, name: "idx_documents_created_by"]
    document_type [type: btree, name: "idx_documents_document_type"]
    organization_id [type: btree, name: "idx_documents_organization_id"]
    patient_id [type: btree, name: "idx_documents_patient_id"]
  }
}

Table "education_materials" {
  "id" uuid [pk, not null, default: `uuid_generate_v4()`]
  "title" text [not null]
  "content" text [not null]
  "category" text [not null]
  "language" text [default: 'en']
  "format" material_format [not null]
  "metadata" jsonb [default: `{}`]
  "created_at" timestamptz [default: `CURRENT_TIMESTAMP`]
  "updated_at" timestamptz [default: `CURRENT_TIMESTAMP`]

  Indexes {
    category [type: btree, name: "idx_education_materials_category"]
    format [type: btree, name: "idx_education_materials_format"]
    language [type: btree, name: "idx_education_materials_language"]
  }
}

Table "immunizations" {
  "id" uuid [pk, not null, default: `uuid_generate_v4()`]
  "patient_id" uuid
  "vaccine_name" text [not null]
  "vaccine_code" text
  "dose_number" int4
  "administered_date" date [not null]
  "administered_by" uuid
  "manufacturer" text
  "lot_number" text
  "expiration_date" date
  "site" administration_site
  "route" administration_route
  "notes" text
  "metadata" jsonb [default: `{}`]
  "created_at" timestamptz [default: `CURRENT_TIMESTAMP`]
  "updated_at" timestamptz [default: `CURRENT_TIMESTAMP`]

  Indexes {
    administered_by [type: btree, name: "idx_immunizations_administered_by"]
    administered_date [type: btree, name: "idx_immunizations_administered_date"]
    patient_id [type: btree, name: "idx_immunizations_patient_id"]
    vaccine_code [type: btree, name: "idx_immunizations_vaccine_code"]
    vaccine_name [type: btree, name: "idx_immunizations_vaccine_name"]
  }
}

Table "insurance_providers" {
  "id" uuid [pk, not null, default: `uuid_generate_v4()`]
  "name" text [not null]
  "contact_info" jsonb [not null]
  "settings" jsonb [default: `{}`]
  "created_at" timestamptz [default: `CURRENT_TIMESTAMP`]
  "updated_at" timestamptz [default: `CURRENT_TIMESTAMP`]
}

Table "inventory_items" {
  "id" uuid [pk, not null, default: `uuid_generate_v4()`]
  "organization_id" uuid
  "location_id" uuid
  "name" text [not null]
  "type" text [not null]
  "quantity" int4 [not null]
  "unit" text [not null]
  "minimum_quantity" int4
  "location" text
  "metadata" jsonb [default: `{}`]
  "created_at" timestamptz [default: `CURRENT_TIMESTAMP`]
  "updated_at" timestamptz [default: `CURRENT_TIMESTAMP`]

  Indexes {
    location_id [type: btree, name: "idx_inventory_items_location_id"]
    organization_id [type: btree, name: "idx_inventory_items_organization_id"]
    type [type: btree, name: "idx_inventory_items_type"]
  }
}

Table "inventory_transactions" {
  "id" uuid [pk, not null, default: `uuid_generate_v4()`]
  "item_id" uuid
  "transaction_type" text [not null]
  "quantity" int4 [not null]
  "performed_by" uuid
  "reason" text
  "metadata" jsonb [default: `{}`]
  "created_at" timestamptz [default: `CURRENT_TIMESTAMP`]

  Indexes {
    item_id [type: btree, name: "idx_inventory_transactions_item_id"]
    performed_by [type: btree, name: "idx_inventory_transactions_performed_by"]
  }
}

Table "lab_results" {
  "id" uuid [pk, not null, default: `gen_random_uuid()`]
  "patient_id" uuid
  "provider_id" uuid
  "test_name" text [not null]
  "test_date" timestamptz [not null]
  "results" jsonb [not null]
  "normal_range" jsonb
  "notes" text
  "created_at" timestamptz [default: `now()`]
  "updated_at" timestamptz [default: `now()`]

  Indexes {
    (patient_id, test_date) [type: btree, name: "idx_lab_results_patient_date"]
    patient_id [type: btree, name: "idx_lab_results_patient_id"]
    provider_id [type: btree, name: "idx_lab_results_provider_id"]
  }
}

Table "locations" {
  "id" uuid [pk, not null, default: `uuid_generate_v4()`]
  "organization_id" uuid
  "name" text [not null]
  "type" text [not null]
  "address" jsonb [not null]
  "contact_info" jsonb [not null]
  "operating_hours" jsonb
  "settings" jsonb [default: `{}`]
  "created_at" timestamptz [default: `CURRENT_TIMESTAMP`]
  "updated_at" timestamptz [default: `CURRENT_TIMESTAMP`]

  Indexes {
    organization_id [type: btree, name: "idx_locations_organization_id"]
  }
}

Table "message_states" {
  "id" uuid [pk, not null, default: `uuid_generate_v4()`]
  "message_id" uuid
  "user_id" uuid
  "state" message_state [not null]
  "updated_at" timestamptz [default: `CURRENT_TIMESTAMP`]

  Indexes {
    (message_id, user_id) [type: btree, name: "message_states_message_id_user_id_key"]
    message_id [type: btree, name: "idx_message_states_message_id"]
    user_id [type: btree, name: "idx_message_states_user_id"]
  }
}

Table "messages" {
  "id" uuid [pk, not null, default: `uuid_generate_v4()`]
  "conversation_id" uuid
  "sender_id" uuid
  "content" text [not null]
  "attachments" jsonb [default: `[]`]
  "metadata" jsonb [default: `{}`]
  "created_at" timestamptz [default: `CURRENT_TIMESTAMP`]
  "updated_at" timestamptz [default: `CURRENT_TIMESTAMP`]

  Indexes {
    conversation_id [type: btree, name: "idx_messages_conversation_id"]
    sender_id [type: btree, name: "idx_messages_sender_id"]
  }
}

Table "notification_preferences" {
  "id" uuid [pk, not null, default: `uuid_generate_v4()`]
  "user_id" uuid
  "type" notification_type [not null]
  "email_enabled" bool [default: true]
  "sms_enabled" bool [default: false]
  "push_enabled" bool [default: true]
  "in_app_enabled" bool [default: true]
  "created_at" timestamptz [default: `CURRENT_TIMESTAMP`]
  "updated_at" timestamptz [default: `CURRENT_TIMESTAMP`]

  Indexes {
    (user_id, type) [type: btree, name: "notification_preferences_user_id_type_key"]
    user_id [type: btree, name: "idx_notification_preferences_user_id"]
  }
}

Table "notification_templates" {
  "id" uuid [pk, not null, default: `uuid_generate_v4()`]
  "organization_id" uuid
  "type" notification_type [not null]
  "name" text [not null]
  "subject_template" text [not null]
  "content_template" text [not null]
  "metadata_template" jsonb [default: `{}`]
  "created_at" timestamptz [default: `CURRENT_TIMESTAMP`]
  "updated_at" timestamptz [default: `CURRENT_TIMESTAMP`]

  Indexes {
    (organization_id, name) [type: btree, name: "notification_templates_organization_id_name_key"]
    organization_id [type: btree, name: "idx_notification_templates_organization_id"]
  }
}

Table "notifications" {
  "id" uuid [pk, not null, default: `uuid_generate_v4()`]
  "organization_id" uuid
  "type" notification_type [not null]
  "priority" notification_priority [not null, default: 'medium']
  "status" notification_status [not null, default: 'pending']
  "sender_id" uuid
  "recipient_id" uuid
  "title" text [not null]
  "content" text [not null]
  "metadata" jsonb [default: `{}`]
  "scheduled_for" timestamptz
  "sent_at" timestamptz
  "read_at" timestamptz
  "created_at" timestamptz [default: `CURRENT_TIMESTAMP`]
  "updated_at" timestamptz [default: `CURRENT_TIMESTAMP`]

  Indexes {
    organization_id [type: btree, name: "idx_notifications_organization_id"]
    recipient_id [type: btree, name: "idx_notifications_recipient_id"]
    sender_id [type: btree, name: "idx_notifications_sender_id"]
  }
}

Table "orders" {
  "id" uuid [pk, not null, default: `uuid_generate_v4()`]
  "patient_id" uuid
  "ordering_provider_id" uuid
  "order_type" order_type [not null]
  "status" order_status [default: 'pending']
  "priority" order_priority [default: 'routine']
  "order_details" jsonb [not null]
  "diagnosis_codes" jsonb
  "notes" text
  "ordered_at" timestamptz [not null, default: `CURRENT_TIMESTAMP`]
  "scheduled_date" timestamptz
  "completed_at" timestamptz
  "metadata" jsonb [default: `{}`]
  "created_at" timestamptz [default: `CURRENT_TIMESTAMP`]
  "updated_at" timestamptz [default: `CURRENT_TIMESTAMP`]

  Indexes {
    order_type [type: btree, name: "idx_orders_order_type"]
    ordered_at [type: btree, name: "idx_orders_ordered_at"]
    ordering_provider_id [type: btree, name: "idx_orders_ordering_provider_id"]
    patient_id [type: btree, name: "idx_orders_patient_id"]
    (patient_id, priority) [type: btree, name: "idx_orders_pending_priority"]
    priority [type: btree, name: "idx_orders_priority"]
    scheduled_date [type: btree, name: "idx_orders_scheduled_date"]
    (status, priority) [type: btree, name: "idx_orders_status_priority"]
  }
}

Table "organization_invites" {
  "id" uuid [pk, not null, default: `uuid_generate_v4()`]
  "email" text [not null]
  "organization_id" uuid
  "role" user_role [not null]
  "status" text [default: 'pending']
  "invited_by" uuid
  "created_at" timestamptz [default: `now()`]
  "expires_at" timestamptz [default: `(now() + '7 days'::interval)`]
  "updated_at" timestamptz [default: `now()`]

  Indexes {
    email [type: btree, name: "idx_org_invites_email"]
    organization_id [type: btree, name: "idx_org_invites_org_id"]
    invited_by [type: btree, name: "idx_organization_invites_invited_by"]
  }
}

Table "organizations" {
  "id" uuid [pk, not null, default: `uuid_generate_v4()`]
  "name" text [not null]
  "type" text [not null]
  "settings" jsonb [default: `{}`]
  "subscription_tier" text
  "billing_info" jsonb
  "created_at" timestamptz [default: `CURRENT_TIMESTAMP`]
  "updated_at" timestamptz [default: `CURRENT_TIMESTAMP`]
  "owner_id" uuid
  "parent_id" uuid
  "hierarchy_level" int4 [default: 0]
  "hierarchy_path" text

  Indexes {
    hierarchy_level [type: btree, name: "idx_organizations_hierarchy_level"]
    hierarchy_path [type: btree, name: "idx_organizations_hierarchy_path"]
    owner_id [type: btree, name: "idx_organizations_owner_id"]
    parent_id [type: btree, name: "idx_organizations_parent_id"]
  }
}

Table "patient_alerts" {
  "id" uuid [pk, not null, default: `uuid_generate_v4()`]
  "patient_id" uuid
  "alert_type" text [not null]
  "description" text [not null]
  "severity" alert_severity [not null]
  "status" alert_status [default: 'active']
  "start_date" date [not null]
  "end_date" date
  "created_by" uuid
  "metadata" jsonb [default: `{}`]
  "created_at" timestamptz [default: `CURRENT_TIMESTAMP`]
  "updated_at" timestamptz [default: `CURRENT_TIMESTAMP`]

  Indexes {
    alert_type [type: btree, name: "idx_patient_alerts_alert_type"]
    created_by [type: btree, name: "idx_patient_alerts_created_by"]
    patient_id [type: btree, name: "idx_patient_alerts_patient_id"]
    severity [type: btree, name: "idx_patient_alerts_severity"]
    status [type: btree, name: "idx_patient_alerts_status"]
  }
}

Table "patient_education_records" {
  "id" uuid [pk, not null, default: `uuid_generate_v4()`]
  "patient_id" uuid
  "material_id" uuid
  "provider_id" uuid
  "provided_date" timestamptz [not null, default: `CURRENT_TIMESTAMP`]
  "notes" text
  "metadata" jsonb [default: `{}`]
  "created_at" timestamptz [default: `CURRENT_TIMESTAMP`]
  "updated_at" timestamptz [default: `CURRENT_TIMESTAMP`]

  Indexes {
    material_id [type: btree, name: "idx_patient_education_records_material_id"]
    patient_id [type: btree, name: "idx_patient_education_records_patient_id"]
    provided_date [type: btree, name: "idx_patient_education_records_provided_date"]
    provider_id [type: btree, name: "idx_patient_education_records_provider_id"]
  }
}

Table "patient_portal_settings" {
  "id" uuid [pk, not null, default: `uuid_generate_v4()`]
  "patient_id" uuid
  "preferences" jsonb [default: `{}`]
  "communication_settings" jsonb [default: `{}`]
  "created_at" timestamptz [default: `CURRENT_TIMESTAMP`]
  "updated_at" timestamptz [default: `CURRENT_TIMESTAMP`]

  Indexes {
    patient_id [type: btree, name: "idx_patient_portal_settings_patient_id"]
  }
}

Table "patient_questionnaires" {
  "id" uuid [pk, not null, default: `uuid_generate_v4()`]
  "patient_id" uuid
  "questionnaire_type" text [not null]
  "responses" jsonb [not null]
  "completed_at" timestamptz
  "created_at" timestamptz [default: `CURRENT_TIMESTAMP`]
  "updated_at" timestamptz [default: `CURRENT_TIMESTAMP`]

  Indexes {
    patient_id [type: btree, name: "idx_patient_questionnaires_patient_id"]
    questionnaire_type [type: btree, name: "idx_patient_questionnaires_type"]
  }
}

Table "referrals" {
  "id" uuid [pk, not null, default: `uuid_generate_v4()`]
  "patient_id" uuid
  "referring_provider_id" uuid
  "referred_to_provider_id" uuid
  "reason" text [not null]
  "priority" referral_priority [default: 'routine']
  "status" referral_status [default: 'pending']
  "referral_date" date [not null]
  "scheduled_date" date
  "completed_date" date
  "notes" text
  "metadata" jsonb [default: `{}`]
  "created_at" timestamptz [default: `CURRENT_TIMESTAMP`]
  "updated_at" timestamptz [default: `CURRENT_TIMESTAMP`]

  Indexes {
    patient_id [type: btree, name: "idx_referrals_patient_id"]
    priority [type: btree, name: "idx_referrals_priority"]
    referred_to_provider_id [type: btree, name: "idx_referrals_referred_to_provider_id"]
    referring_provider_id [type: btree, name: "idx_referrals_referring_provider_id"]
    status [type: btree, name: "idx_referrals_status"]
  }
}

Table "role_definitions" {
  "role" user_role [pk, not null]
  "description" text [not null]
  "base_permissions" jsonb [not null]
  "created_at" timestamptz [not null, default: `now()`]
  "updated_at" timestamptz [not null, default: `now()`]
}

Table "role_permissions" {
  "id" uuid [pk, not null, default: `uuid_generate_v4()`]
  "role" user_role [not null]
  "resource" text [not null]
  "actions" "text[]" [not null]
  "conditions" jsonb [default: `{}`]
  "created_at" timestamptz [default: `CURRENT_TIMESTAMP`]
  "updated_at" timestamptz [default: `CURRENT_TIMESTAMP`]

  Indexes {
    (role, resource) [type: btree, name: "role_permissions_role_resource_key"]
    role [type: btree, name: "idx_role_permissions_role"]
  }
}

Table "task_comments" {
  "id" uuid [pk, not null, default: `uuid_generate_v4()`]
  "task_id" uuid
  "user_id" uuid
  "content" text [not null]
  "attachments" jsonb [default: `[]`]
  "created_at" timestamptz [default: `CURRENT_TIMESTAMP`]
  "updated_at" timestamptz [default: `CURRENT_TIMESTAMP`]

  Indexes {
    task_id [type: btree, name: "idx_task_comments_task_id"]
    user_id [type: btree, name: "idx_task_comments_user_id"]
  }
}

Table "task_watchers" {
  "id" uuid [pk, not null, default: `uuid_generate_v4()`]
  "task_id" uuid
  "user_id" uuid
  "created_at" timestamptz [default: `CURRENT_TIMESTAMP`]

  Indexes {
    (task_id, user_id) [type: btree, name: "task_watchers_task_id_user_id_key"]
    task_id [type: btree, name: "idx_task_watchers_task_id"]
    user_id [type: btree, name: "idx_task_watchers_user_id"]
  }
}

Table "tasks" {
  "id" uuid [pk, not null, default: `uuid_generate_v4()`]
  "organization_id" uuid
  "department_id" uuid
  "title" text [not null]
  "description" text
  "priority" task_priority [not null, default: 'medium']
  "status" task_status [not null, default: 'pending']
  "assigned_to" uuid
  "assigned_by" uuid
  "due_date" timestamptz
  "completed_at" timestamptz
  "related_to" jsonb [default: `{}`]
  "metadata" jsonb [default: `{}`]
  "created_at" timestamptz [default: `CURRENT_TIMESTAMP`]
  "updated_at" timestamptz [default: `CURRENT_TIMESTAMP`]

  Indexes {
    assigned_by [type: btree, name: "idx_tasks_assigned_by"]
    assigned_to [type: btree, name: "idx_tasks_assigned_to"]
    department_id [type: btree, name: "idx_tasks_department_id"]
    due_date [type: btree, name: "idx_tasks_due_date"]
    organization_id [type: btree, name: "idx_tasks_organization_id"]
    priority [type: btree, name: "idx_tasks_priority"]
    status [type: btree, name: "idx_tasks_status"]
  }
}

Table "teams" {
  "id" uuid [pk, not null, default: `uuid_generate_v4()`]
  "department_id" uuid
  "name" text [not null]
  "description" text
  "leader_id" uuid
  "members" jsonb [default: `[]`]
  "created_at" timestamptz [default: `CURRENT_TIMESTAMP`]
  "updated_at" timestamptz [default: `CURRENT_TIMESTAMP`]

  Indexes {
    department_id [type: btree, name: "idx_teams_department_id"]
    leader_id [type: btree, name: "idx_teams_leader_id"]
  }
}

Table "templates" {
  "id" uuid [pk, not null, default: `uuid_generate_v4()`]
  "organization_id" uuid
  "name" text [not null]
  "type" text [not null]
  "content" text [not null]
  "metadata" jsonb [default: `{}`]
  "created_at" timestamptz [default: `CURRENT_TIMESTAMP`]
  "updated_at" timestamptz [default: `CURRENT_TIMESTAMP`]

  Indexes {
    organization_id [type: btree, name: "idx_templates_organization_id"]
    type [type: btree, name: "idx_templates_type"]
  }
}

Table "user_roles" {
  "id" uuid [pk, not null, default: `uuid_generate_v4()`]
  "user_id" uuid [not null]
  "organization_id" uuid
  "role" user_role [not null]
  "department_id" uuid
  "custom_permissions" jsonb [default: `{}`]
  "created_at" timestamptz [default: `CURRENT_TIMESTAMP`]
  "updated_at" timestamptz [default: `CURRENT_TIMESTAMP`]
  "invitation_status" text [default: 'direct']

  Indexes {
    (user_id, organization_id, role) [type: btree, name: "user_roles_user_id_organization_id_role_key"]
    department_id [type: btree, name: "idx_user_roles_department_id"]
    organization_id [type: btree, name: "idx_user_roles_organization_id"]
    role [type: btree, name: "idx_user_roles_role"]
    user_id [type: btree, name: "idx_user_roles_user_id"]
    (user_id, organization_id) [type: btree, name: "idx_user_roles_user_org"]
  }
}

Table "vital_signs" {
  "id" uuid [pk, not null, default: `uuid_generate_v4()`]
  "patient_id" uuid
  "recorded_by" uuid
  "recorded_at" timestamptz [not null, default: `CURRENT_TIMESTAMP`]
  "blood_pressure_systolic" int4
  "blood_pressure_diastolic" int4
  "heart_rate" int4
  "respiratory_rate" int4
  "temperature" numeric
  "temperature_unit" text [default: 'C']
  "oxygen_saturation" int4
  "height" numeric
  "weight" numeric
  "bmi" numeric
  "pain_level" int4
  "notes" text
  "metadata" jsonb [default: `{}`]
  "created_at" timestamptz [default: `CURRENT_TIMESTAMP`]
  "updated_at" timestamptz [default: `CURRENT_TIMESTAMP`]

  Indexes {
    patient_id [type: btree, name: "idx_vital_signs_patient_id"]
    recorded_at [type: brin, name: "idx_vital_signs_recorded_at_brin"]
    recorded_by [type: btree, name: "idx_vital_signs_recorded_by"]
  }
}

Table "workflow_instances" {
  "id" uuid [pk, not null, default: `uuid_generate_v4()`]
  "workflow_id" uuid
  "status" workflow_status [not null, default: 'pending']
  "context" jsonb [not null]
  "current_step" int4 [default: 0]
  "results" jsonb [default: `[]`]
  "started_at" timestamptz [default: `CURRENT_TIMESTAMP`]
  "completed_at" timestamptz
  "created_at" timestamptz [default: `CURRENT_TIMESTAMP`]
  "updated_at" timestamptz [default: `CURRENT_TIMESTAMP`]

  Indexes {
    workflow_id [type: btree, name: "idx_workflow_instances_workflow_id"]
  }
}

Table "workflow_logs" {
  "id" uuid [pk, not null, default: `uuid_generate_v4()`]
  "workflow_instance_id" uuid
  "step_number" int4 [not null]
  "step_name" text [not null]
  "status" workflow_status [not null]
  "message" text
  "details" jsonb [default: `{}`]
  "created_at" timestamptz [default: `CURRENT_TIMESTAMP`]

  Indexes {
    workflow_instance_id [type: btree, name: "idx_workflow_logs_instance_id"]
  }
}

Table "workflows" {
  "id" uuid [pk, not null, default: `uuid_generate_v4()`]
  "organization_id" uuid
  "type" workflow_type [not null]
  "name" text [not null]
  "description" text
  "trigger_type" workflow_trigger [not null]
  "trigger_config" jsonb [not null]
  "steps" "jsonb[]" [not null]
  "enabled" bool [default: true]
  "created_by" uuid
  "created_at" timestamptz [default: `CURRENT_TIMESTAMP`]
  "updated_at" timestamptz [default: `CURRENT_TIMESTAMP`]

  Indexes {
    created_by [type: btree, name: "idx_workflows_created_by"]
    organization_id [type: btree, name: "idx_workflows_organization_id"]
  }
}

Ref "activity_logs_organization_id_fkey":"organizations"."id" < "activity_logs"."organization_id" [delete: cascade]

Ref "allergies_patient_id_fkey":"patients"."id" < "allergies"."patient_id"

Ref "analytics_events_organization_id_fkey":"organizations"."id" < "analytics_events"."organization_id"

Ref "analytics_metrics_organization_id_fkey":"organizations"."id" < "analytics_metrics"."organization_id"

Ref "appointments_department_id_fkey":"departments"."id" < "appointments"."department_id"

Ref "appointments_organization_id_fkey":"organizations"."id" < "appointments"."organization_id"

Ref "appointments_patient_id_fkey":"patients"."id" < "appointments"."patient_id" [delete: cascade]

Ref "appointments_provider_id_fkey":"healthcare_providers"."id" < "appointments"."provider_id" [delete: cascade]

Ref "care_team_members_patient_id_fkey":"patients"."id" < "care_team_members"."patient_id"

Ref "care_team_members_provider_id_fkey":"healthcare_providers"."id" < "care_team_members"."provider_id"

Ref "claims_insurance_provider_id_fkey":"insurance_providers"."id" < "claims"."insurance_provider_id"

Ref "claims_patient_id_fkey":"patients"."id" < "claims"."patient_id"

Ref "claims_provider_id_fkey":"healthcare_providers"."id" < "claims"."provider_id"

Ref "clinical_notes_medical_record_id_fkey":"medical_records"."id" < "clinical_notes"."medical_record_id"

Ref "conversation_participants_conversation_id_fkey":"conversations"."id" < "conversation_participants"."conversation_id"

Ref "conversations_organization_id_fkey":"organizations"."id" < "conversations"."organization_id"

Ref "departments_location_id_fkey":"locations"."id" < "departments"."location_id"

Ref "documents_organization_id_fkey":"organizations"."id" < "documents"."organization_id"

Ref "documents_patient_id_fkey":"patients"."id" < "documents"."patient_id"

Ref "healthcare_providers_department_id_fkey":"departments"."id" < "healthcare_providers"."department_id"

Ref "healthcare_providers_organization_id_fkey":"organizations"."id" < "healthcare_providers"."organization_id"

Ref "immunizations_administered_by_fkey":"healthcare_providers"."id" < "immunizations"."administered_by"

Ref "immunizations_patient_id_fkey":"patients"."id" < "immunizations"."patient_id"

Ref "inventory_items_location_id_fkey":"locations"."id" < "inventory_items"."location_id"

Ref "inventory_items_organization_id_fkey":"organizations"."id" < "inventory_items"."organization_id"

Ref "inventory_transactions_item_id_fkey":"inventory_items"."id" < "inventory_transactions"."item_id"

Ref "lab_results_patient_id_fkey":"patients"."id" < "lab_results"."patient_id" [delete: cascade]

Ref "lab_results_provider_id_fkey":"healthcare_providers"."id" < "lab_results"."provider_id"

Ref "locations_organization_id_fkey":"organizations"."id" < "locations"."organization_id"

Ref "medical_records_department_id_fkey":"departments"."id" < "medical_records"."department_id"

Ref "medical_records_organization_id_fkey":"organizations"."id" < "medical_records"."organization_id"

Ref "medical_records_patient_id_fkey":"patients"."id" < "medical_records"."patient_id" [delete: cascade]

Ref "medical_records_provider_id_fkey":"healthcare_providers"."id" < "medical_records"."provider_id"

Ref "medications_patient_id_fkey":"patients"."id" < "medications"."patient_id" [delete: cascade]

Ref "medications_provider_id_fkey":"healthcare_providers"."id" < "medications"."provider_id"

Ref "message_states_message_id_fkey":"messages"."id" < "message_states"."message_id"

Ref "messages_conversation_id_fkey":"conversations"."id" < "messages"."conversation_id"

Ref "notification_templates_organization_id_fkey":"organizations"."id" < "notification_templates"."organization_id"

Ref "notifications_organization_id_fkey":"organizations"."id" < "notifications"."organization_id"

Ref "orders_ordering_provider_id_fkey":"healthcare_providers"."id" < "orders"."ordering_provider_id"

Ref "orders_patient_id_fkey":"patients"."id" < "orders"."patient_id"

Ref "organization_invites_organization_id_fkey":"organizations"."id" < "organization_invites"."organization_id" [delete: cascade]

Ref "organizations_parent_id_fkey":"organizations"."id" < "organizations"."parent_id" [delete: set null]

Ref "patient_alerts_patient_id_fkey":"patients"."id" < "patient_alerts"."patient_id"

Ref "patient_education_records_material_id_fkey":"education_materials"."id" < "patient_education_records"."material_id"

Ref "patient_education_records_patient_id_fkey":"patients"."id" < "patient_education_records"."patient_id"

Ref "patient_education_records_provider_id_fkey":"healthcare_providers"."id" < "patient_education_records"."provider_id"

Ref "patient_portal_settings_patient_id_fkey":"patients"."id" < "patient_portal_settings"."patient_id"

Ref "patient_questionnaires_patient_id_fkey":"patients"."id" < "patient_questionnaires"."patient_id"

Ref "patients_organization_id_fkey":"organizations"."id" < "patients"."organization_id"

Ref "referrals_patient_id_fkey":"patients"."id" < "referrals"."patient_id"

Ref "referrals_referred_to_provider_id_fkey":"healthcare_providers"."id" < "referrals"."referred_to_provider_id"

Ref "referrals_referring_provider_id_fkey":"healthcare_providers"."id" < "referrals"."referring_provider_id"

Ref "fk_role_permissions_role":"role_definitions"."role" < "role_permissions"."role" [delete: cascade]

Ref "task_comments_task_id_fkey":"tasks"."id" < "task_comments"."task_id"

Ref "task_watchers_task_id_fkey":"tasks"."id" < "task_watchers"."task_id"

Ref "tasks_department_id_fkey":"departments"."id" < "tasks"."department_id"

Ref "tasks_organization_id_fkey":"organizations"."id" < "tasks"."organization_id"

Ref "teams_department_id_fkey":"departments"."id" < "teams"."department_id"

Ref "teams_leader_id_fkey":"healthcare_providers"."id" < "teams"."leader_id"

Ref "templates_organization_id_fkey":"organizations"."id" < "templates"."organization_id"

Ref "fk_user_roles_role":"role_definitions"."role" < "user_roles"."role" [delete: cascade]

Ref "user_roles_department_id_fkey":"departments"."id" < "user_roles"."department_id"

Ref "user_roles_organization_id_fkey":"organizations"."id" < "user_roles"."organization_id"

Ref "vital_signs_patient_id_fkey":"patients"."id" < "vital_signs"."patient_id"

Ref "workflow_instances_workflow_id_fkey":"workflows"."id" < "workflow_instances"."workflow_id"

Ref "workflow_logs_workflow_instance_id_fkey":"workflow_instances"."id" < "workflow_logs"."workflow_instance_id"

Ref "workflows_organization_id_fkey":"organizations"."id" < "workflows"."organization_id"
